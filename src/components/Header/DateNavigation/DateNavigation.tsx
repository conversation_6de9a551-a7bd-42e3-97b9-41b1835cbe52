// UI library imports
import { Button, IconButton, palette } from 'lion-ui'

// Icon imports
import ChevronLeftOutlinedIcon from '@mui/icons-material/ChevronLeftOutlined'
import ChevronRightOutlinedIcon from '@mui/icons-material/ChevronRightOutlined'
import HomeIcon from '@components/Icons/HomeIcon/HomeIcon'

// Utils imports
import updateActiveDates from '@utils/updateActiveDates'

// Constants imports
import { numOfWeeksPerStep } from '@constants/timeOptions'

const DateNavigation = () => {
  return (
    <div data-testid="date-navigation">
      <IconButton
        onClick={() => {
          updateActiveDates('prev', numOfWeeksPerStep)
        }}
        aria-label="previous"
        sx={{
          padding: 1
        }}
      >
        <ChevronLeftOutlinedIcon />
      </IconButton>
      <Button
        sx={{ backgroundColor: palette.common.white, gap: '0.5rem', borderColor: palette.secondary.main }}
        variant="outlined"
        onClick={() => {
          updateActiveDates('')
        }}
        data-testid="Calendar.Header.Navigation.Day.Today.Button"
      >
        <HomeIcon height="20" width="20" />
        Today
      </Button>
      <IconButton
        onClick={() => {
          updateActiveDates('next', numOfWeeksPerStep)
        }}
        aria-label="next"
        sx={{
          padding: 1
        }}
      >
        <ChevronRightOutlinedIcon />
      </IconButton>
    </div>
  )
}

export default DateNavigation
