import { type IconProps } from '../Icon.props'

const TSPlusIcon = ({ height, width }: IconProps) => {
  return (
    <svg width={width} height={height} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="32" height="32" rx="16" fill="#EBFAED" />
      <path
        d="M16.5166 15.8201L18.7695 18.0437C18.9007 18.2029 18.9663 18.3849 18.9663 18.5897C18.9663 18.7945 18.8968 18.9633 18.7577 19.096C18.6047 19.2428 18.4279 19.3182 18.2271 19.3222C18.0264 19.3262 17.8604 19.2468 17.7292 19.0841L15.2939 16.6537C15.2244 16.5845 15.1648 16.4961 15.1153 16.3885C15.0657 16.2808 15.0409 16.1665 15.0409 16.0454V12.5149C15.0409 12.3138 15.1097 12.1409 15.2474 11.9962C15.3851 11.8515 15.5627 11.7791 15.7803 11.7791C15.998 11.7791 16.1751 11.847 16.3117 11.9828C16.4483 12.1186 16.5166 12.297 16.5166 12.518V15.8201ZM15.8967 24C14.8098 24 13.7876 23.7945 12.8301 23.3834C11.8727 22.9724 11.0334 22.3982 10.3124 21.6609C9.5913 20.9235 9.02564 20.0712 8.61539 19.104C8.20513 18.1367 8 17.1046 8 16.0076C8 14.9013 8.2071 13.8642 8.62129 12.8966C9.03547 11.929 9.60113 11.0785 10.3183 10.3451C11.0354 9.61172 11.8769 9.03752 12.8428 8.62251C13.8088 8.2075 14.8371 8 15.9276 8C16.1189 8 16.305 8.00637 16.4858 8.01912C16.6667 8.03186 16.847 8.04965 17.0269 8.07249C17.2825 8.10857 17.4965 8.20807 17.669 8.371C17.8416 8.53392 17.9278 8.73466 17.9278 8.97323C17.9278 9.22975 17.8416 9.44881 17.669 9.63043C17.4965 9.81205 17.2825 9.89278 17.0269 9.8726C16.8452 9.84447 16.6635 9.81985 16.4818 9.79874C16.3001 9.77764 16.1121 9.76709 15.9178 9.76709C14.2059 9.76709 12.7475 10.3737 11.5425 11.5869C10.3375 12.8001 9.73506 14.2704 9.73506 15.9978C9.73506 17.7251 10.3337 19.1982 11.5311 20.4168C12.7284 21.6355 14.1826 22.2448 15.8935 22.2448C17.6143 22.2448 19.0744 21.6362 20.2737 20.4191C21.4731 19.2019 22.0727 17.7308 22.0727 16.006C22.0727 15.7645 22.1561 15.5541 22.3228 15.3748C22.4896 15.1955 22.6956 15.1059 22.9408 15.1059C23.1861 15.1059 23.3938 15.1955 23.5641 15.3748C23.7344 15.5541 23.8196 15.7645 23.8196 16.006C23.8196 17.1059 23.6144 18.139 23.2042 19.1053C22.7939 20.0717 22.2263 20.9235 21.5013 21.6609C20.7763 22.3982 19.9327 22.9724 18.9704 23.3834C18.0082 23.7945 16.9836 24 15.8967 24Z"
        fill="#0F9156"
      />
      <path
        d="M19.3514 10.9686L20.6155 12.1675L23.6278 9.23095C23.7884 9.07698 23.9762 9 24.1912 9C24.4062 9 24.5914 9.07698 24.7469 9.23095C24.9024 9.3849 24.9863 9.56622 24.9987 9.7749C25.011 9.9836 24.9369 10.1647 24.7764 10.3181L21.2228 13.7447C21.0469 13.9149 20.8369 14 20.5929 14C20.3489 14 20.1381 13.9149 19.9606 13.7447L18.228 12.068C18.0689 11.914 17.9931 11.7306 18.0005 11.5177C18.0079 11.3048 18.0912 11.1215 18.2503 10.968C18.4094 10.8145 18.5944 10.7377 18.8052 10.7377C19.016 10.7377 19.1981 10.8147 19.3514 10.9686Z"
        fill="#0F9156"
      />
    </svg>
  )
}

export default TSPlusIcon
