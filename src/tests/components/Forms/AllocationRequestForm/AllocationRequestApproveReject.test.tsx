import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import AllocationRequestForm from '@components/Forms/AllocationRequestForm/AllocationRequestForm'
import { resourceDetailsVar, showAllocationDetailsFormVar, userProfileVar } from '@gql/client/localState'

import { MockedProvider } from '@apollo/client/testing'
import { GET_RESOURCE_AND_PLACEHOLDER } from '@gql/queries/searchResourcePlaceholder'
import { APPROVE_REQUEST_MUTATION } from '@gql/mutations/assignment.mutation'
import { ApolloClient, InMemoryCache } from '@apollo/client'
import { UserProfile } from '@constants/userProfile'

const mockAssignment = {
  id: '1',
  startDate: '2023-01-01',
  dueDate: '2023-01-10',
  hoursPerDay: 8,
  state: 'active',
  userId: 'user123',
  projectId: 'project123',
  taskId: 'task123',
  activeNonWorkingDays: ['2023-01-05', '2023-01-06'],
  x: 10,
  y: 20,
  width: 100,
  height: 50,
  totalDays: 10,
  taskName: 'Task Name',
  projectName: 'Project Name',
  externalLink: 'http://example.com',
  altairJobNumber: 'ALT123',
  isPlaceholder: false,
  assignmentState: 'in-progress',
  projectManagerName: 'John Doe',
  taskState: 'not-started',
  taskNotes: 'Task notes',
  assignmentNotes: 'Assignment notes',
  taskStartDate: '2024-09-23T08:00:00+00:00',
  taskDueDate: '2024-11-15T16:00:00+00:00',
  projectAgencyCode: 'agency-2',
  projectBrandCode: 'brand-2',
  projectIntegrationId: 'project-integration-id-1',
  userIntegrationId: 'user-integration-id-1',
  agencyCode: 'agency-1',
  agencyName: 'Agency 1',
  costCenterCode: 'cost-center-1',
  costCenterName: 'Cost Center 1',
  locationName: 'Location 1',
  blockedByTimesheet: false,
  assignmentIcaStatus: 'Needs Approval'
}

const mockResourceDetails = {
  id: 'resource1',
  name: 'John Doe',
  location: 'New York',
  position: 'Senior Developer',
  jobTitle: 'Software Engineer',
  profitCenter: 'IT Department',
  altairNumber: 'ALT456',
  totalCapacity: 160,
  minimumAgencyHoursPerDay: 8,
  workCode: 'DEV123',
  agencyName: 'PXP',
  timeOffDetails: [],
  holidays: [],
  requiresAssignApproval: true,
}

const mocks = [
  {
    request: {
      query: GET_RESOURCE_AND_PLACEHOLDER,
      variables: {
        params: {
          userId: '10082922',
          searchName: ''
        }
      }
    },
    result: {
      data: {
        getResourceAndPlaceholder: [mockResourceDetails]
      }
    }
  },
  {
    request: {
      query: APPROVE_REQUEST_MUTATION,
      variables: {
        params: {
          externalId: '1',
          taskAssignmentState: 1,
          userLoggedInExternalId: '10082922'
        }
      }
    },
    result: {
      data: {
        approveRequest: true
      }
    }
  },
  {
    request: {
      query: APPROVE_REQUEST_MUTATION,
      variables: {
        params: {
          externalId: '1',
          taskAssignmentState: 6,
          reason: '',
          userLoggedInExternalId: '10082922'
        }
      }
    },
    result: {
      data: {
        approveRequest: true
      }
    }
  }
]

// eslint-disable-next-line @typescript-eslint/no-unused-vars
let apolloClientInit: ApolloClient<unknown>
const cache = new InMemoryCache()

describe('AllocationRequestForm Component', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    apolloClientInit = new ApolloClient({
      cache
    })
    showAllocationDetailsFormVar(mockAssignment)
    userProfileVar(UserProfile.RESOURCE_MANAGER)
  })
  test('approves the request correctly', async () => {
    showAllocationDetailsFormVar(mockAssignment)
    resourceDetailsVar(mockResourceDetails)
    userProfileVar(UserProfile.RESOURCE_MANAGER)

    render(
      <MockedProvider mocks={mocks} addTypename={false}>
        <AllocationRequestForm />
      </MockedProvider>
    )

    const approveButton = screen.getByText('Approve Request')
    fireEvent.click(approveButton)
  })

  test('rejects the request correctly', async () => {
    showAllocationDetailsFormVar(mockAssignment)
    resourceDetailsVar(mockResourceDetails)

    render(
      <MockedProvider mocks={mocks} addTypename={false}>
        <AllocationRequestForm />
      </MockedProvider>
    )

    const rejectButton = screen.getByText('Reject Request')
    fireEvent.click(rejectButton)

    const rejectRequestButton = screen.getByText('Confirm rejection')
    fireEvent.click(rejectRequestButton)
  })
})
