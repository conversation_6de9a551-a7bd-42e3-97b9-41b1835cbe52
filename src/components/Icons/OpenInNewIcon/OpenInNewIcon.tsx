import { type IconProps } from '../Icon.props'

const OpenInNewIcon = ({ fillColor, height = '12', width = '12' }: IconProps) => {
  return (
    <svg width={width} height={height} viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.6665 1.40961C6.6665 1.04142 6.96498 0.742948 7.33317 0.742948H10.6298C10.998 0.742948 11.2965 1.04142 11.2965 1.40961V4.63895C11.2965 5.00714 10.998 5.30561 10.6298 5.30561C10.2616 5.30561 9.96317 5.00714 9.96317 4.63895V2.07628H7.33317C6.96498 2.07628 6.6665 1.7778 6.6665 1.40961Z"
        fill={fillColor}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.1026 0.941936C11.3623 1.20291 11.3613 1.62502 11.1003 1.88474L6.48031 6.48274C6.21934 6.74247 5.79724 6.74146 5.53751 6.48049C5.27778 6.21952 5.27879 5.79741 5.53976 5.53769L10.1598 0.939686C10.4207 0.679959 10.8428 0.680966 11.1026 0.941936Z"
        fill={fillColor}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.33317 0.666748C1.70136 0.666748 1.99984 0.965225 1.99984 1.33341V10.0001H10.6665C11.0347 10.0001 11.3332 10.2986 11.3332 10.6667C11.3332 11.0349 11.0347 11.3334 10.6665 11.3334H1.33317C0.964981 11.3334 0.666504 11.0349 0.666504 10.6667V1.33341C0.666504 0.965225 0.964981 0.666748 1.33317 0.666748Z"
        fill={fillColor}
      />
    </svg>
  )
}

export default OpenInNewIcon
