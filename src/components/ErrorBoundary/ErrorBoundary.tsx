/* eslint-disable @typescript-eslint/no-explicit-any */
// component extracted from next.js official docs
import { Component, type ReactNode } from 'react'

import { ErrorFallback } from './ErrorFallback'

interface ErrorBoundaryProps {
  children: ReactNode
}

interface ErrorBoundaryState {
  hasError: boolean
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(_error: any) {
    return { hasError: true }
  }

  componentDidCatch(error: any, errorInfo: any) {
    // Use your own error logging service here
    console.log({ error, errorInfo })
  }

  // eslint-disable-next-line @typescript-eslint/promise-function-async
  render() {
    // Check if the error is thrown
    if (this.state.hasError) {
      return (
        <ErrorFallback
          title="Oops! Something went wrong on our end. We're working to fix it as soon as possible!"
          subtitle="Even calendars need a break sometimes!"
          action={() => {
            this.setState({ hasError: false })
          }}
        ></ErrorFallback>
      )
    }

    // Return children components in case of no error
    return this.props.children
  }
}

export default ErrorBoundary
