import { getAccessToken } from '@auth/msalUtil'
import type ExtendedJwtPayload from '@typeDefs/ExtendedJwtPayload'
import { decodeJWT } from './decodeJWT'
import { allowedUserRoles, DEFAULT_USER_ROLE } from '@constants/userProfile'

export const getUserLLid = async (): Promise<string> => {
  const token = await getAccessToken()
  const decoded = decodeJWT(token ?? '') as ExtendedJwtPayload
  return decoded?.upn?.split('@')[0] ?? ''
}

export const getInitials = (str: string): string => {
  return str
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase())
    .join('')
}

export function normalizeRoles(inputRoles: string[]): string[] {
  const flatRoles = inputRoles.flatMap((role) => role.split(',').map((r) => r.trim()))

  const filteredRoles = allowedUserRoles.filter((role) => flatRoles.includes(role))

  if (!filteredRoles.includes(DEFAULT_USER_ROLE)) {
    filteredRoles.push(DEFAULT_USER_ROLE)
  }

  return allowedUserRoles.filter((role) => filteredRoles.includes(role))
}

export async function getUsername(): Promise<string> {
  try {
    const token = await getAccessToken()

    if (!token) {
      throw new Error('Access token is missing')
    }

    const decoded = decodeJWT(token) as ExtendedJwtPayload
    const { given_name = '', family_name = '' } = decoded

    if (!given_name && !family_name) {
      throw new Error('given_name and family_name not found in token')
    }

    return `${given_name} ${family_name}`.trim()
  } catch (error) {
    console.error('Failed to decode JWT:', error)
    return ''
  }
}
