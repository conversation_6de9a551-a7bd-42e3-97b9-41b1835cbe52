// React imports
import { memo, useEffect, useState } from 'react'

// Third-party library imports
import {
  Autocomplete,
  Box,
  Button,
  Divider,
  FormLabel,
  LionDateRangePicker,
  OutlinedInput,
  TextField,
  Typography,
  palette,
  CircularProgress,
  LinearProgress
} from 'lion-ui'
import { useQuery, useReactiveVar, useLazyQuery } from '@apollo/client'

// Component imports
import EditIcon from '@components/Icons/EditIcon/EditIcon'
import SeeDetails from '../Common/SeeDetails/SeeDetails'
import CloseIcon from '@mui/icons-material/Close'
import CheckIcon from '@components/Icons/CheckIcon/CheckIcon'

// Type definitions
import { type UpdateAssignmentParams, type Assignment } from '@typeDefs/Assignments'
import { type ResourceOrgStructure, type Resource, type ResourceHolidayAndTimeOffDetails } from '@typeDefs/Resource'
import { type AllocationDetailsFormErrors, type AllocationDetailsFormData } from '@typeDefs/Forms'
import { type SearchResourcePlaceholder } from '@typeDefs/SearchResourcePlaceholder'
import { type Holiday } from '@typeDefs/Holiday'
import { type TimeOffDetails } from '@typeDefs/Timesheets'

// Utility functions
import {
  handleHoursChange,
  handleDateChange,
  filterAndMapNonWorkingDates,
  mergeDistinctNonWorkingDates,
  generateWarningMessage,
  getWeekendDates
} from '../Common/Handlers/FormHandlers'
import { gplError } from '@utils/gplError'
import dayjs from 'dayjs'

// GraphQL queries
import { GET_RESOURCE_AND_PLACEHOLDER } from '@gql/queries/searchResourcePlaceholder'
import { userInfoVar, generalLoadingStateVar } from '@gql/client/localState'
import getLocaleDateFormat from '@utils/getLocaleDateFormat'
import ResourceAdditionalDetails from '../Common/ResourceAdditionalDetails/ResourceAdditionalDetails'
import PlaceholderAdditionalDetails from '../Common/PlaceholderAdditionalDetails/PlaceholderAdditionalDetails'
import { NonWorkingDatesTypes } from '@constants/nonWorkingDatesTypes'
import {
  HOLIDAYS_AND_TIMEOFFDETAILS_QUERY,
  RESOURCE_ORG_STRUCTURE_QUERY,
  ORG_STRUCTURE_AGENCY_COST_CENTER_QUERY
} from '@gql/queries/resources.query'
import { PROJECTS_BY_PROJECT_ID_QUERY } from '@gql/queries/projects.query'
interface AssignmentUpdateProps {
  assignment: Assignment
  resourceDetails: Resource | undefined
  updateAssignment: (formValues: UpdateAssignmentParams) => void
  isEditingAssignment: (isEditing: boolean) => void
  isLoading: boolean
}

const AssignmentUpdate = (props: AssignmentUpdateProps) => {
  const activeUser = useReactiveVar(userInfoVar)
  const { assignment, isLoading } = props
  const externalLink = process.env.WF_SERVER_URL
  const [isEditing, setIsEditing] = useState(false)
  const [resourceOrgStructure, setResourceOrgStructure] = useState<
    | {
        data: ResourceOrgStructure
        loading: boolean
      }
    | null
    | undefined
  >(undefined)

  const formattedStartDate = getLocaleDateFormat(assignment?.startDate)
  const formattedDueDate = getLocaleDateFormat(assignment?.dueDate)
  const initialFormErrors: AllocationDetailsFormErrors = {
    hoursPerDay: '',
    duration: '',
    assignTo: ''
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [fetchProjectDetails, { error: projectOrgError }] = useLazyQuery(PROJECTS_BY_PROJECT_ID_QUERY, {
    onError: (error) => {
      gplError(error, projectOrgError)
    }
  })

  const [fetchOrgStructure, { error: orgStructureError }] = useLazyQuery(ORG_STRUCTURE_AGENCY_COST_CENTER_QUERY, {
    onError: (error) => {
      gplError(error, orgStructureError)
    }
  })
  const initialFormState: AllocationDetailsFormData = {
    hoursPerDay: assignment?.hoursPerDay ?? 0,
    duration: [dayjs(assignment?.startDate).toDate(), dayjs(assignment?.dueDate).toDate()],
    assignTo: null
  }

  const [allocationDetailsFormData, setAllocationDetailsFormData] =
    useState<AllocationDetailsFormData>(initialFormState)

  const [allocationDetailsFormErrors, setAllocationDetailsFormErrors] =
    useState<AllocationDetailsFormErrors>(initialFormErrors)

  const [searchTermName, setSearchTermName] = useState('')

  const [resourceOrgStructureData, setResourceOrgStructureData] = useState<ResourceOrgStructure | null | undefined>({
    agencyName: assignment?.agencyName ?? '',
    costCenterName: assignment?.costCenterName ?? '',
    locationName: assignment?.locationName ?? '',
    agencyCode: assignment?.agencyCode ?? '',
    costCenterCode: assignment?.costCenterCode ?? ''
  })

  const {
    data: assignToData,
    error: assignToDataError,
    loading: assignToDataLoading
  } = useQuery<{ getResourceAndPlaceholder: SearchResourcePlaceholder[] }>(GET_RESOURCE_AND_PLACEHOLDER, {
    variables: {
      params: {
        userId: activeUser?.altairNumber,
        searchName: searchTermName
      }
    },
    onError: (error) => {
      gplError(error, assignToDataError)
    }
  })

  const { error: resourceOrgStructureError } = useQuery<{
    getResourceOrgStructure?: ResourceOrgStructure
  }>(RESOURCE_ORG_STRUCTURE_QUERY, {
    variables: {
      resourceId: activeUser?.altairNumber
    },
    onError: (error) => {
      gplError(error, resourceOrgStructureError)
    }
  })

  const { error: resourceHolidayAndTimeOffDetailsError, loading: resourceHolidayAndTimeOffDetailsLoading } = useQuery<{
    getHolidaysAndTimeOffDetailsByResourceId: ResourceHolidayAndTimeOffDetails
  }>(HOLIDAYS_AND_TIMEOFFDETAILS_QUERY, {
    variables: {
      params: {
        resourceId: allocationDetailsFormData.assignTo?.id,
        startDate: dayjs(allocationDetailsFormData.duration[0]).format('YYYY-MM-DD'),
        endDate: dayjs(allocationDetailsFormData.duration[1]).format('YYYY-MM-DD')
      }
    },
    skip:
      !allocationDetailsFormData.assignTo?.id ||
      !allocationDetailsFormData.duration ||
      allocationDetailsFormData.assignTo?.isPlaceholder, // Skip query if data is missing
    onError: (error) => {
      gplError(error, resourceHolidayAndTimeOffDetailsError)
    },
    onCompleted: ({ getHolidaysAndTimeOffDetailsByResourceId }) => {
      const holidays = filterAndMapNonWorkingDates(
        (getHolidaysAndTimeOffDetailsByResourceId?.holidays ?? []).map((holiday: Holiday) => ({
          date: holiday.holidayDate
        })),
        allocationDetailsFormData.duration[0],
        allocationDetailsFormData.duration[1],
        NonWorkingDatesTypes.HOLIDAY
      )

      const timeOffDetails = filterAndMapNonWorkingDates(
        (getHolidaysAndTimeOffDetailsByResourceId?.timeOffDetails ?? []).map((timeOff: TimeOffDetails) => ({
          date: timeOff.absenceDate
        })),
        allocationDetailsFormData.duration[0],
        allocationDetailsFormData.duration[1],
        NonWorkingDatesTypes.ABSENCE
      )

      const distinctNonWorkingDates = mergeDistinctNonWorkingDates([...holidays, ...timeOffDetails])

      if (distinctNonWorkingDates.length) {
        setAllocationDetailsFormData((prev) => ({
          ...prev,
          nonWorkingDates: distinctNonWorkingDates
        }))
      }
    }
  })

  const hasFormErrors = () => {
    return Boolean(
      allocationDetailsFormErrors.assignTo ||
        allocationDetailsFormErrors.hoursPerDay ||
        allocationDetailsFormErrors.duration ||
        !allocationDetailsFormData.hoursPerDay ||
        !allocationDetailsFormData.assignTo?.id ||
        !resourceOrgStructure?.data ||
        resourceOrgStructure?.loading
    )
  }

  const handleEditAssignment = () => {
    setIsEditing(true)
    props.isEditingAssignment(true)
  }

  const handleCancelEditAssignment = () => {
    setIsEditing(false)
    props.isEditingAssignment(false)
  }

  const handleSubmit = () => {
    if (hasFormErrors() || !allocationDetailsFormData.assignTo) {
      return
    }

    generalLoadingStateVar(true)

    const weekends = getWeekendDates(allocationDetailsFormData.duration[0], allocationDetailsFormData.duration[1])

    const params: UpdateAssignmentParams = {
      externalId: assignment.id,
      hoursPerDay: allocationDetailsFormData.hoursPerDay ?? 0,
      startDate: dayjs(allocationDetailsFormData.duration[0]).format('YYYY-MM-DD'),
      dueDate: dayjs(allocationDetailsFormData.duration[1]).format('YYYY-MM-DD'),
      userId: allocationDetailsFormData.assignTo.id,
      userLoggedInExternalId: activeUser?.altairNumber,
      updateAssignmentDate: dayjs().toISOString(),
      isSplit: false,
      nonWorkingDates: mergeDistinctNonWorkingDates([
        ...(allocationDetailsFormData.nonWorkingDates ?? []),
        ...(weekends ?? [])
      ])
    }

    if (allocationDetailsFormData.assignTo?.id !== assignment.userId) {
      params.agencyName = resourceOrgStructure?.data?.agencyName
      params.costCenterName = resourceOrgStructure?.data?.costCenterName
      params.locationName = resourceOrgStructure?.data?.locationName
      params.agencyCode = resourceOrgStructure?.data?.agencyCode
      params.costCenterCode = resourceOrgStructure?.data?.costCenterCode
    }

    props.updateAssignment(params)
  }

  // Updates the form values once assignToData is successfully fetched
  useEffect(() => {
    if (assignToData && assignment) {
      const assignTo = assignToData?.getResourceAndPlaceholder.find((x) => x.id === assignment.userId) ?? null

      setTimeout(() => {
        setAllocationDetailsFormData({
          hoursPerDay: assignment.hoursPerDay,
          duration: [dayjs(assignment?.startDate).toDate(), dayjs(assignment?.dueDate).toDate()],
          assignTo: assignToData?.getResourceAndPlaceholder.find((x) => x.id === assignment.userId) ?? assignTo
        })
      }, 0)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [assignToData?.getResourceAndPlaceholder])

  useEffect(() => {
    if (assignment) {
      setResourceOrgStructureData({
        agencyName: assignment?.agencyName ?? '',
        costCenterName: assignment?.costCenterName ?? '',
        locationName: assignment?.locationName ?? '',
        agencyCode: assignment?.agencyCode ?? '',
        costCenterCode: assignment?.costCenterCode ?? ''
      })
    }
  }, [assignment])

  return (
    <Box
      sx={{
        width: '100%',
        background: palette.grey['50'],
        borderRadius: '0.5rem',
        padding: '1rem',
        border: isEditing ? `2px solid ${palette['500-Accent-1'].main}` : 'none'
      }}
    >
      {!isEditing && (
        <>
          <Box sx={{ display: 'flex' }}>
            <Box
              sx={{
                display: 'flex',
                width: '50%',
                flexDirection: 'column',
                gap: '0.375rem;',
                alignItems: 'flex-start'
              }}
            >
              <Box sx={{ marginBottom: '1rem' }}>
                <Typography variant="BodyMBold" sx={{ color: palette.grey['700'] }}>
                  Hours per day
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    padding: ' 0.25rem 0rem',
                    alignItems: 'center',
                    gap: '0.25rem',
                    flex: ' 1 0 0'
                  }}
                >
                  <Typography
                    sx={{ fontSize: '0.875rem', color: palette.grey['700'] }}
                    data-testid="ResourceRequestForm-hoursPerDay"
                  >
                    {assignment.hoursPerDay}
                  </Typography>
                </Box>
              </Box>

              <Box sx={{ marginBottom: '1rem' }}>
                <Typography variant="BodyMBold" sx={{ color: palette.grey['700'] }}>
                  Assigned to:
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    padding: ' 0.25rem 0rem',
                    alignItems: 'center',
                    gap: '0.25rem',
                    flex: ' 1 0 0'
                  }}
                >
                  <Typography
                    sx={{ fontSize: '0.875rem', color: palette.grey['700'] }}
                    data-testid="ResourceRequestFormAsignee"
                  >
                    {props.resourceDetails?.name}
                  </Typography>
                </Box>
              </Box>
            </Box>

            <Box
              sx={{
                display: 'flex',
                width: '50%',
                flexDirection: 'column',
                gap: '0.375rem;',
                alignItems: 'flex-start'
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '0.375rem;',
                  alignItems: 'flex-start'
                }}
              >
                <Typography variant="BodyMBold" sx={{ color: palette.grey['700'] }}>
                  Duration
                </Typography>

                <Typography
                  sx={{ fontSize: '0.875rem', color: palette.grey['700'] }}
                  data-testid="ResourceRequestFormDates"
                >
                  {formattedStartDate} - {formattedDueDate}
                </Typography>
              </Box>
            </Box>
          </Box>
          <Box>
            <Box
              sx={{
                marginTop: '1rem',
                display: 'flex',
                padding: '0',
                alignItems: 'center',
                gap: '1rem',
                alignSelf: 'stretch'
              }}
            >
              <Typography variant="OverlineS" sx={{ letterSpacing: '0.0625rem', textTransform: 'uppercase' }}>
                Additional Details
              </Typography>
              <Divider
                sx={{
                  display: 'flex',
                  height: '0.0625rem',
                  justifyContent: 'center',
                  alignItems: 'center',
                  flex: ' 1 0 0'
                }}
              ></Divider>
            </Box>
          </Box>

          <ResourceAdditionalDetails
            assignToId={props.resourceDetails?.id}
            resourceOrgStructure={resourceOrgStructureData}
          />
          <Box>
            <Button
              sx={{ borderColor: palette.secondary.main, width: '100%', fontWeight: '600' }}
              onClick={handleEditAssignment}
              variant="outlined"
              data-testid="ResourceRequestForm-updateAssignmentButton"
              startIcon={<EditIcon height="15" width="15" />}
              disabled={
                (assignment.assignmentState?.toLowerCase() !== 'rejected' &&
                  (assignment.taskState?.toLowerCase() === 'external' ||
                    assignment.taskState?.toLowerCase() === 'complete' ||
                    assignment.blockedByTimesheet)) ||
                isLoading
              }
            >
              Edit Allocation
            </Button>
          </Box>
        </>
      )}

      {isEditing && (
        <>
          <Box sx={{ display: 'flex' }}>
            <span
              style={{
                display: isLoading ? 'inline-block' : 'none',
                position: 'absolute',
                width: '100%',
                height: '100%',
                top: 0,
                right: 0,
                zIndex: 1,
                background: 'white',
                opacity: 0.3
              }}
            ></span>
            <form style={{ width: '100%' }} data-testid="resourceRequestForm" id="resourceRequestForm">
              <Box
                sx={{
                  display: 'flex',
                  flex: '1 1 0rem',
                  alignItems: 'flex-start',
                  justifyContent: 'space-between',
                  margin: '0 0',
                  width: '100%'
                }}
              >
                <Box sx={{ width: '47%' }}>
                  <FormLabel
                    className={'MuiFormLabel-asterisk'}
                    required
                    sx={{
                      display: 'block',
                      fontWeight: 'bold',
                      color: palette.primary.dark,
                      my: 3,
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      '& .MuiFormLabel-asterisk': {
                        color: palette['500-Accent-3'].main
                      }
                    }}
                    htmlFor="hoursPerDay"
                  >
                    Hours per Day
                  </FormLabel>
                  <OutlinedInput
                    id="hoursPerDay"
                    data-testid="hoursPerDay"
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    inputProps={{ 'data-testid': 'hoursPerDayInput' }}
                    name="hoursPerDay"
                    type="number"
                    value={allocationDetailsFormData.hoursPerDay}
                    onChange={handleHoursChange(setAllocationDetailsFormData, setAllocationDetailsFormErrors)}
                    size="small"
                    placeholder="Must be more than 0"
                    sx={{ width: '100%' }}
                    error={Boolean(allocationDetailsFormErrors.hoursPerDay)}
                  />
                  {allocationDetailsFormErrors.hoursPerDay && (
                    <Typography color="error">{allocationDetailsFormErrors.hoursPerDay}</Typography>
                  )}
                </Box>
                <Box sx={{ width: '49%' }}>
                  <Typography
                    variant="body1"
                    fontWeight="bolder"
                    component="label"
                    htmlFor="duration"
                    sx={{ display: 'block', my: 3 }}
                  >
                    Duration{' '}
                    <Typography fontWeight="bolder" component="span" color={palette['500-Accent-3'].main}>
                      *
                    </Typography>
                  </Typography>
                  <Box data-testid="dateRangePicker">
                    <LionDateRangePicker
                      locale={navigator.language.toLowerCase()}
                      startDate={allocationDetailsFormData.duration[0]}
                      endDate={allocationDetailsFormData.duration[1]}
                      onDateChange={handleDateChange(setAllocationDetailsFormData, allocationDetailsFormData)}
                      monthsInView={2}
                      minDate={assignment.taskStartDate ? new Date(assignment.taskStartDate) : null}
                      maxDate={assignment.taskDueDate ? new Date(assignment.taskDueDate) : null}
                    />
                    {resourceHolidayAndTimeOffDetailsLoading && (
                      <Box>
                        <LinearProgress />
                      </Box>
                    )}

                    {generateWarningMessage(allocationDetailsFormData) && (
                      <Box
                        sx={{
                          background: `${palette.info.light}33`,
                          marginTop: '5px',
                          borderRadius: '10px',
                          padding: '4px'
                        }}
                      >
                        <Typography fontWeight={'bold'} fontSize={'small'} color={palette.info.dark}>
                          Note:{' '}
                          <span style={{ color: palette.primary.dark, fontWeight: '500' }}>
                            The selected date range includes {generateWarningMessage(allocationDetailsFormData)}. Please
                            review carefully!
                          </span>
                        </Typography>
                      </Box>
                    )}
                  </Box>
                </Box>
              </Box>
              <Box sx={{ margin: '1.5rem 0', width: '100%' }}>
                <FormLabel
                  className={'MuiFormLabel-asterisk'}
                  required
                  sx={{
                    fontWeight: 'bolder',
                    color: palette.primary.dark,
                    my: 3,
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    '& .MuiFormLabel-asterisk': {
                      color: palette['500-Accent-3'].main
                    }
                  }}
                  htmlFor="assignTo"
                >
                  Assign To
                </FormLabel>
                <Autocomplete
                  sx={{ marginTop: '0.5rem' }}
                  fullWidth
                  disablePortal
                  id="assignTo"
                  data-testid="AssignUpdateForm-AssignTo"
                  options={assignToData?.getResourceAndPlaceholder ?? []}
                  getOptionLabel={(option) => option?.name}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      error={allocationDetailsFormErrors.assignTo !== ''}
                      placeholder="Search for a resource..."
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {assignToDataLoading ? (
                              <CircularProgress size={20} sx={{ color: '#999', marginRight: '8px' }} />
                            ) : null}
                            {params.InputProps.endAdornment}
                          </>
                        )
                      }}
                    />
                  )}
                  size="small"
                  isOptionEqualToValue={(option, value) => option.id === value.id}
                  onChange={(_e, value) => {
                    setResourceOrgStructureData(undefined)
                    setResourceOrgStructure(undefined)
                    setAllocationDetailsFormData((prev) => ({ ...prev, assignTo: value, nonWorkingDates: [] }))
                    if (value?.isPlaceholder) {
                      if (value?.id) {
                        fetchProjectDetails({ variables: { projectId: assignment.projectId } })
                          .then((response) => {
                            const projectData = response.data?.getProjectsByProjectId
                            if (projectData) {
                              fetchOrgStructure({
                                variables: {
                                  params: {
                                    costCenterCode: projectData[0].costCenterCode,
                                    agencyCode: projectData[0].agencyCode
                                  }
                                }
                              })
                                .then((orgResponse) => {
                                  const orgData = orgResponse.data?.getOrgStructureByAgencyCodeCostCenterCode
                                  if (orgData) {
                                    setResourceOrgStructureData((prev) => ({
                                      ...prev,
                                      costCenterCode: projectData[0].costCenterCode ?? '',
                                      agencyCode: projectData[0].agencyCode ?? '',
                                      agencyName: orgData.agencyName ?? '',
                                      costCenterName: orgData.costCenterName ?? '',
                                      locationName: orgData.locationName ?? ''
                                    }))
                                  }
                                })
                                .catch((error) => {
                                  console.error('Error fetching organization structure:', error)
                                })
                            }
                          })
                          .catch((error) => {
                            console.error('Error fetching project details:', error)
                          })
                      }
                    }
                  }}
                  onInputChange={(_event, value, reason) => {
                    if (reason === 'clear') {
                      setSearchTermName('')
                      setAllocationDetailsFormData((prev) => ({ ...prev, assignTo: null, nonWorkingDates: [] }))
                    } else if (reason === 'input') {
                      if (value.length >= 3) {
                        setSearchTermName(value)
                      } else {
                        setSearchTermName('')
                        setAllocationDetailsFormData((prev) => ({ ...prev, assignTo: null, nonWorkingDates: [] }))
                      }
                    }
                  }}
                  onBlur={() => {
                    setAllocationDetailsFormErrors({
                      ...allocationDetailsFormErrors,
                      assignTo: allocationDetailsFormData.assignTo === null ? 'Assign to is required' : ''
                    })
                  }}
                  value={allocationDetailsFormData.assignTo}
                />
                {allocationDetailsFormErrors.assignTo && (
                  <Typography color="error"> {allocationDetailsFormErrors.assignTo}</Typography>
                )}
                {allocationDetailsFormData.assignTo && (
                  <SeeDetails
                    externalLink={`${externalLink}/${allocationDetailsFormData.assignTo.isPlaceholder ? 'jobrole' : 'user'}/${allocationDetailsFormData.assignTo?.integrationId}/details`}
                  />
                )}
              </Box>
              <Box>
                <Box
                  sx={{
                    marginTop: '1rem',
                    display: 'flex',
                    padding: '0',
                    alignItems: 'center',
                    gap: '1rem',
                    alignSelf: 'stretch'
                  }}
                >
                  <Typography variant="OverlineS" sx={{ letterSpacing: '0.0625rem', textTransform: 'uppercase' }}>
                    Additional Details
                  </Typography>
                  <Divider
                    sx={{
                      display: 'flex',
                      height: '0.0625rem',
                      justifyContent: 'center',
                      alignItems: 'center',
                      flex: ' 1 0 0'
                    }}
                  ></Divider>
                </Box>
              </Box>
              {allocationDetailsFormData.assignTo?.isPlaceholder && (
                <PlaceholderAdditionalDetails
                  updateResourceOrgStructure={setResourceOrgStructure}
                  resourceOrgStructureData={resourceOrgStructureData}
                />
              )}
              {!allocationDetailsFormData.assignTo?.isPlaceholder && (
                <ResourceAdditionalDetails
                  assignToId={allocationDetailsFormData?.assignTo?.id}
                  updateResourceOrgStructure={setResourceOrgStructure}
                  resourceOrgStructure={resourceOrgStructureData}
                />
              )}
            </form>
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Button
              sx={{ borderColor: palette.secondary.main, width: '49%', fontWeight: '600' }}
              onClick={handleCancelEditAssignment}
              variant="outlined"
              data-testid="ResourceRequestForm-cancelButton"
              startIcon={<CloseIcon />}
              disabled={isLoading}
            >
              Dismiss
            </Button>
            <Button
              sx={{
                background: palette['500-Accent-1'].main,
                width: '49%',
                fontWeight: '600',
                border: '1px solid',
                borderColor: palette['500-Accent-1'].main,
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ':hover': {
                  background: palette['500-Accent-1'].main,
                  border: '1px solid',
                  borderColor: palette['Error-Main'].main
                }
              }}
              onClick={handleSubmit}
              variant="contained"
              data-testid="ResourceRequestForm-updateAssignmentTriggerButton"
              startIcon={<CheckIcon height="15" width="15" />}
              disabled={hasFormErrors() || isLoading || resourceHolidayAndTimeOffDetailsLoading}
            >
              Update Allocation{' '}
              {isLoading && (
                <CircularProgress
                  data-testid="ResourceRequestForm-updateAssignmentLoading"
                  size={24}
                  sx={{
                    color: palette.primary.main,
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    marginTop: '-12px',
                    marginLeft: '-12px'
                  }}
                />
              )}
            </Button>
          </Box>
        </>
      )}
    </Box>
  )
}

export default memo(AssignmentUpdate)
