import { Box, Button, palette } from 'lion-ui'
import type AuditTrailGoToDetailsProps from './AuditTrailGoToDetails.props'
import CalendarIcon from '@components/Icons/CalendarIcon/CalendarIcon'
import { ASSIGNMENTS_QUERY, SINGLE_ASSIGNMENT_QUERY } from '@gql/queries/assignments.query'
import { gplError } from '@utils/gplError'
import { type ApolloError, useApolloClient, useLazyQuery, useReactiveVar } from '@apollo/client'
import { activeDatesVar, resourceDetailsVar, showAllocationDetailsFormVar, userInfoVar } from '@gql/client/localState'
import { useEffect } from 'react'
import { type Assignment } from '@typeDefs/Assignments'
import { AssignmentStatus } from '@constants/taskStatus'
import { RESOURCES_QUERY } from '@gql/queries/resources.query'
import { type Resource } from '@typeDefs/Resource'
import { getAssignmentsQueryParams } from '@utils/getAssignmentsQueryParams'

const AuditTrailGoToDetails = ({ assignmentId, resourceId }: AuditTrailGoToDetailsProps) => {
  const activeUser = useReactiveVar(userInfoVar)
  const [fetchAssignment, { data: assignmentData, error: assignmentDataError, loading: assignmentDataLoading }] =
    useLazyQuery(SINGLE_ASSIGNMENT_QUERY, {
      onError: (error: ApolloError) => {
        gplError(error, assignmentDataError)
      },
      fetchPolicy: 'network-only'
    })

  const client = useApolloClient()

  const handleClick = async () => {
    try {
      const assignmentsData = await client.readQuery({
        query: ASSIGNMENTS_QUERY,
        variables: {
          params: getAssignmentsQueryParams()
        }
      }).assignments

      const resourcesData = await client.readQuery({
        query: RESOURCES_QUERY,
        variables: {
          params: {
            userId: activeUser?.altairNumber,
            pageNumber: 1,
            pageSize: 50,
            startDate: activeDatesVar().startDate,
            endDate: activeDatesVar().endDate,
            sort: {
              field: 'name',
              order: 'asc'
            }
          }
        }
      }).resources.items

      const isAssignmentInMemory: Assignment = assignmentsData.find(
        (item: Assignment) => item.id === assignmentId.toLowerCase()
      )

      const isResourceInMemory: Resource = resourcesData.find((item: Resource) => item.id === resourceId.toLowerCase())

      if (isAssignmentInMemory && isAssignmentInMemory.assignmentState.toLowerCase() === AssignmentStatus.REQUESTED) {
        resourceDetailsVar(isResourceInMemory)
        showAllocationDetailsFormVar(isAssignmentInMemory)
      } else if (isAssignmentInMemory) {
        showAllocationDetailsFormVar(isAssignmentInMemory)
      }

      if (!isAssignmentInMemory) {
        const assignmentData = await fetchAssignment({ variables: { assignmentId } })
        // const resourcetData = await fetchResource({ variables: { resourceId } })

        if (
          assignmentData &&
          assignmentData.data.getAssignmentById.assignmentState.toLowerCase() === AssignmentStatus.REQUESTED
        ) {
          resourceDetailsVar(isResourceInMemory)
          showAllocationDetailsFormVar(assignmentData.data.getAssignmentById as Assignment)
        } else if (assignmentData) {
          showAllocationDetailsFormVar(assignmentData.data.getAssignmentById as Assignment)
        }
      }
    } catch (error) {
      console.error('Error:', error)
    }
  }

  useEffect(() => {
    if (assignmentData) {
      showAllocationDetailsFormVar(assignmentData.getAssignmentById as Assignment)
    } else if (assignmentDataError) {
      console.error('Error fetching assignment data:', assignmentDataError)
    }
  }, [assignmentData, assignmentDataError])

  return (
    <Box>
      <Button
        sx={{
          backgroundColor: palette.common.white,
          gap: '0.5rem',
          borderColor: palette.secondary.main,
          marginTop: '1rem'
        }}
        disabled={assignmentDataLoading}
        variant="outlined"
        onClick={() => {
          void handleClick()
        }}
        data-testid="audit-trail-go-to-details-button"
      >
        <CalendarIcon width="1rem" height="1rem" />
        Go to details
      </Button>
    </Box>
  )
}

export default AuditTrailGoToDetails
