# Project Boilerplate

This project boilerplate provides a starting point for developing web applications using Next.js, React, and Redux Toolkit. It includes the necessary configuration and folder structure to help you get started quickly.

## Technologies Used

- [Next.js](https://nextjs.org/): A React framework for building server-rendered applications.
- [React](https://reactjs.org/): A JavaScript library for building user interfaces.
- [Redux Toolkit](https://redux-toolkit.js.org/): A package that provides simplified, opinionated Redux logic for efficient state management.

## Getting Started

To use this project boilerplate, follow the steps below:

1. Clone this repository to your local machine:

   ```shell
   git clone <repository_url>
   ```

2. Delete the .git folder, then create a new GIT instance

   ```shell
   git init
   ```

3. Install the project dependencies:

   ```shell
   npm install
   ```

4. Start the development server:

   ```shell
   npm run dev
   ```

   Alternatively, you can use Docker to run the application. Make sure you have Docker installed on your machine. Run the following command to build and start the application:

   ```shell
   docker-compose up --build
   ```

5. Open your browser and navigate to [http://localhost:3000] to see the application running.

## Folder Structure

The folder structure of this project boilerplate is as follows:

```
├── public/          # Static assets (e.g., images, fonts)
├── src/             # Application source code
│   ├── components/  # Reusable React components
│   ├── hooks/       # Hooks
│   ├── pages/       # Next.js pages
│   ├── redux/       # Redux related files (reducers, actions, store)
│   ├── styles/      # Global CSS styles
│   └── theme/       # Marcel Theme Config for MUI
├── .gitignore       # Files and directories to ignore in version control
├── package.json     # Project dependencies and scripts
└── README.md        # Project documentation (you're reading it!)
```

Feel free to modify the folder structure to suit your project's needs.

## Branding

This boilerplate is preconfigured with the Marcel branding, giving it a distinct visual identity. You can customize the branding by updating the config in the `src/theme` directory. Apart from that, this is a vanilla implementation of MUI v5.

## Contributing

Contributions to this project boilerplate are welcome! If you find any issues or have suggestions for improvements, please open an issue or submit a pull request.

## License

This project is licensed under the [MIT License](LICENSE). You can freely use, modify, and distribute this boilerplate.
