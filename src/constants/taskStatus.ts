export enum AssignmentStatus {
  REQUESTED = 'requested',
  REJECTED = 'rejected',
  APPROVED = 'approved',
  CANCELLED = 'cancelled',
  PROPOSED = 'proposed',
  EXTERNAL = 'external'
}

export const TaskAssignmentState = {
  unknown: 0,
  approved: 1,
  cancelled: 2,
  NA: 3,
  proposed: 4,
  requested: 5,
  rejected: 6
}

export const TaskStates = {
  new: 'New',
  inprogress: 'In-Progress',
  complete: 'Complete'
}
