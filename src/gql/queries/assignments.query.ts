import { gql } from '@apollo/client'

export const assignmentFieldsQuery = `
      id
      startDate
      dueDate
      hoursPerDay
      state
      assignmentState
      userId
      projectId
      taskId
      totalDays
      x
      y
      width
      height: hoursPerDay
      isPlaceholder
      taskName
      projectName
      externalLink
      altairJobNumber
      projectManagerName
      taskState
      taskNotes
      assignmentNotes
      taskStartDate
      taskDueDate
      projectAgencyCode
      projectBrandCode
      projectAgencyName
      projectIntegrationId
      userIntegrationId
      locationName
      costCenterCode
      agencyCode
      agencyName
      costCenterName
      blockedByTimesheet
      assignmentIcaStatus
    `

const SINGLE_ASSIGNMENT_QUERY = gql`
  query getAssignmentById($assignmentId: ID) {
    getAssignmentById(assignmentId: $assignmentId) {
      ${assignmentFieldsQuery}
    }
  }
`

const ASSIGNMENTS_QUERY = gql`
  query Assignments($params: AssignmentParams) {
    assignments(params: $params) {
      ${assignmentFieldsQuery}
    }
  }
`

export { SINGLE_ASSIGNMENT_QUERY, ASSIGNMENTS_QUERY }
