// lion iu imports
import { palette } from 'lion-ui'

// Test imports
import getCalendarItemStyle, {
  commonBeforeStyle,
  commonDashedBorder,
  getTileStatus
} from '@components/Calendar/CalendarDragItem/CalendarDragItem.style'
import type { TileStatus } from '@typeDefs/Tile'

describe('CalendarDragItem Styles', () => {
  const commonStyles = {
    borderRadius: '8px',
    cursor: 'pointer',
    // eslint-disable-next-line @typescript-eslint/naming-convention
    '.ui-resizable-sw': {
      opacity: 0,
      bottom: '0 !important',
      left: '0 !important'
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    '.ui-resizable-se': {
      opacity: 0,
      bottom: '0 !important',
      right: '0 !important'
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    '.ui-resizable-e': {
      right: '0 !important'
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    '.ui-resizable-w': {
      left: '0 !important'
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    '.ui-resizable-s': {
      bottom: '0 !important'
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    '.ui-resizable-handle': {
      display: 'unset !important'
    }
  }
  it('should return correct dashed border string from commonDashedBorder', () => {
    const color = '#ff0000'
    const expected = '1px dashed #ff0000'
    expect(commonDashedBorder(color)).toBe(expected)
  })

  it('should return correct before style object from commonBeforeStyle', () => {
    const color = '#00ff00'
    const expected = {
      content: '""',
      position: 'absolute',
      left: 0,
      top: 0,
      height: '100%',
      width: '4px',
      backgroundColor: color,
      borderRadius: '8px'
    }
    expect(commonBeforeStyle(color)).toEqual(expected)
  })

  it('should return correct style object from getCalendarItemStyle for active status', () => {
    const status = 'inprogress'
    const expected = {
      backgroundColor: '#ADD5CB',
      color: palette['Success-Dark'].dark,
      ...commonStyles
    }
    expect(getCalendarItemStyle(status)).toEqual(expected)
  })

  it('should return correct style object from getCalendarItemStyle for cancelled status', () => {
    const status = 'cancelled'
    const expected = {
      backgroundColor: palette['Error-Light'].main,
      color: palette['Error-Dark'].main,
      ...commonStyles
    }
    expect(getCalendarItemStyle(status)).toEqual(expected)
  })

  it('should return correct style object from getCalendarItemStyle  for onhold status', () => {
    const status = 'onhold'
    const expected = {
      backgroundColor: palette.grey['700'],
      color: palette.common.white,
      ...commonStyles
    }
    expect(getCalendarItemStyle(status)).toEqual(expected)
  })

  it('should return correct style object from getCalendarItemStyle  for completed status', () => {
    const status = 'complete'
    const expected = {
      backgroundColor: palette['Success-Dark'].main,
      color: palette.common.white,
      ...commonStyles
    }
    expect(getCalendarItemStyle(status)).toEqual(expected)
  })

  it('should return correct style object from getCalendarItemStyle  for draft status', () => {
    const status = 'new'
    const expected = {
      backgroundColor: palette.grey['100'],
      color: '#4D4D4D',
      border: commonDashedBorder('#999999'),
      ...commonStyles
    }
    expect(getCalendarItemStyle(status)).toEqual(expected)
  })

  it('should return correct style object from getCalendarItemStyle  for tentative status', () => {
    const status = 'tentative'
    const expected = {
      backgroundColor: palette['800-Accent-1'].main,
      color: '#370275',
      border: commonDashedBorder('#370275'), // we dont have #370275
      backgroundImage: `repeating-linear-gradient(125deg, ${palette['100-Accent-2'].main}, ${palette['100-Accent-2'].main} 1px, transparent 1px, transparent 5px)`,
      ...commonStyles
    }
    expect(getCalendarItemStyle(status)).toEqual(expected)
  })

  it('should return correct tile colors for approved status from getCalendarItemStyle', () => {
    const status = 'approved'
    const expected = {
      backgroundColor: '#E7F3F0',
      color: palette['Success-Dark'].dark
    }
    const itemStyle = getCalendarItemStyle(status)
    expect(itemStyle.backgroundColor).toEqual(expected.backgroundColor)
    expect(itemStyle.color).toEqual(expected.color)
  })

  it('should return correct tile colors for rejected status from getCalendarItemStyle', () => {
    const status = 'rejected'
    const expected = {
      backgroundColor: palette['50-Accent-3'].main,
      color: '#800942'
    }
    const itemStyle = getCalendarItemStyle(status)
    expect(itemStyle.backgroundColor).toEqual(expected.backgroundColor)
    expect(itemStyle.color).toEqual(expected.color)
  })

  it('should return assignment state over task state from getTileStatus', () => {
    const taskState = 'inprogress'
    const assignmentState = 'Approved'
    const expected = 'Approved'
    const tileStatus = getTileStatus(assignmentState, taskState, 'resource')
    expect(tileStatus).toEqual(expected)
  })

  it('should return default style object from getCalendarItemStyle for unknown status', () => {
    const status = 'unknown' as TileStatus
    const expected = {
      backgroundColor: 'white',
      ...commonStyles
    }
    expect(getCalendarItemStyle(status)).toEqual(expected)
  })
})
