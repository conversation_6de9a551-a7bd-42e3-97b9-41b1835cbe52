import { useEffect, useState } from 'react'

import { Box, Typography, palette, CircularProgress } from 'lion-ui'

import { useQuery } from '@apollo/client'
import { gplError } from '@utils/gplError'
import { RESOURCE_ORG_STRUCTURE_QUERY } from '@gql/queries/resources.query'

import { type ResourceOrgStructure } from '@typeDefs/Resource'
import { type ResourceAdditionalDetailsProps } from './ResourceAdditionalDetails.props'

const ResourceAdditionalDetails = ({
  assignToId,
  updateResourceOrgStructure,
  resourceOrgStructure
}: ResourceAdditionalDetailsProps) => {
  const {
    data: resourceOrgStructureData,
    error: resourceOrgStructureError,
    loading: resourceOrgStructureLoading
  } = useQuery<{ getResourceOrgStructure?: ResourceOrgStructure }>(RESOURCE_ORG_STRUCTURE_QUERY, {
    variables: {
      resourceId: assignToId
    },
    onError: (error) => {
      gplError(error, resourceOrgStructureError)
    },
    skip: !assignToId || assignToId === '' || resourceOrgStructure !== undefined
  })

  const [businesUnitData, setBusinesUnitData] = useState('')
  const [costCenterData, setCostCenterData] = useState('')

  useEffect(() => {
    if (resourceOrgStructureData?.getResourceOrgStructure && updateResourceOrgStructure) {
      updateResourceOrgStructure({
        data: resourceOrgStructureData.getResourceOrgStructure ?? {},
        loading: resourceOrgStructureLoading
      })
    }

    if (resourceOrgStructureData?.getResourceOrgStructure) {
      setBusinesUnitData(
        `${resourceOrgStructureData?.getResourceOrgStructure?.agencyCode} : ${resourceOrgStructureData?.getResourceOrgStructure?.agencyName}`
      )
      setCostCenterData(
        `${resourceOrgStructureData?.getResourceOrgStructure?.costCenterCode} : ${resourceOrgStructureData?.getResourceOrgStructure?.costCenterName}`
      )
    }
  }, [resourceOrgStructureData?.getResourceOrgStructure, resourceOrgStructureLoading, updateResourceOrgStructure])

  useEffect(() => {
    if (resourceOrgStructure && updateResourceOrgStructure) {
      updateResourceOrgStructure({
        data: resourceOrgStructure,
        loading: false
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <>
      {!resourceOrgStructureLoading && (resourceOrgStructureData ?? resourceOrgStructure) && (
        <Box
          sx={{
            marginTop: '1rem',
            display: 'flex',
            padding: '0',
            alignItems: 'flex-start',
            gap: '1rem',
            alignSelf: 'stretch',
            flexDirection: 'column',
            marginBottom: '1rem'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: '1.5rem', alignSelf: 'stretch' }}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
                gap: '0.375rem',
                flex: '1 0 0'
              }}
            >
              <Typography variant="BodyMBold" sx={{ color: palette.grey['700'], fontWeight: 600 }}>
                Agency
              </Typography>
              <Typography
                variant="BodyMRegular"
                sx={{
                  display: 'flex',
                  padding: '0.625rem 0rem',
                  alignItems: 'center',
                  gap: '0.5rem',
                  flex: '1 0 0'
                }}
              >
                {resourceOrgStructureData?.getResourceOrgStructure
                  ? `${businesUnitData}`
                  : `${resourceOrgStructure?.agencyCode} : ${resourceOrgStructure?.agencyName}`}
              </Typography>
            </Box>

            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
                gap: '0.375rem',
                flex: '1 0 0'
              }}
            >
              <Typography variant="BodyMBold" sx={{ color: palette.grey['700'], fontWeight: 600 }}>
                Location
              </Typography>
              <Typography
                variant="BodyMRegular"
                sx={{
                  display: 'flex',
                  padding: '0.625rem 0rem',
                  alignItems: 'center',
                  gap: '0.5rem',
                  flex: '1 0 0'
                }}
              >
                {resourceOrgStructureData?.getResourceOrgStructure?.locationName ?? resourceOrgStructure?.locationName}
              </Typography>
            </Box>
          </Box>

          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'flex-start',
              gap: '0.375rem',
              flex: '1 0 0'
            }}
          >
            <Typography variant="BodyMBold" sx={{ color: palette.grey['700'], fontWeight: 600 }}>
              Cost Center
            </Typography>
            <Typography
              variant="BodyMRegular"
              sx={{
                display: 'flex',
                padding: '0.625rem 0rem',
                alignItems: 'center',
                gap: '0.5rem',
                flex: '1 0 0'
              }}
            >
              {resourceOrgStructureData?.getResourceOrgStructure
                ? `${costCenterData}`
                : `${resourceOrgStructure?.costCenterCode} : ${resourceOrgStructure?.costCenterName}`}
            </Typography>
          </Box>
        </Box>
      )}
      {resourceOrgStructureLoading && <CircularProgress />}
    </>
  )
}

export default ResourceAdditionalDetails
