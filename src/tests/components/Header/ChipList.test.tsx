import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import { MockedProvider } from '@apollo/client/testing'
import ChipList from '@components/Header/FilterSearch/ChipList/ChipList'
import { activeFiltersVar } from '@gql/client/localState'
import { type LabelItem } from '@typeDefs/LabelItem'

// Mock data
const mockFilters: LabelItem[] = [
  { id: '1', name: 'Filter 1', category: 'projects', icon: { src: '/icon1.png' } },
  { id: '2', name: 'Filter 2', category: 'projects', icon: { src: '/icon2.png' } },
  { id: '3', name: 'Filter 3', category: 'projects', icon: { src: '/icon3.png' } },
  { id: '4', name: 'Filter 4', category: 'projects', icon: { src: '/icon4.png' } }
]

describe('ChipList', () => {
  beforeEach(() => {
    activeFiltersVar({
      projects: mockFilters,
      resources: [],
      brands: [],
      usersGroups: [],
      tasks: [],
      taskStates: [],
      placeholders: []
    })
  })

  it('renders the correct number of chips', () => {
    render(
      <MockedProvider>
        <ChipList />
      </MockedProvider>
    )
    expect(screen.getAllByRole('button', { name: /Filter/i })).toHaveLength(5)
  })

  it('shows additional filters when the button is clicked', () => {
    render(
      <MockedProvider>
        <ChipList />
      </MockedProvider>
    )
    fireEvent.click(screen.getByText('+1'))
    expect(screen.getByText('Filter 4')).toBeInTheDocument()
  })

  it('removes a filter when the delete icon is clicked', () => {
    render(
      <MockedProvider>
        <ChipList />
      </MockedProvider>
    )
    fireEvent.click(screen.getAllByTestId('ClearIcon')[0])
    expect(screen.queryByText('Filter 1')).not.toBeInTheDocument()
  })

  it('should remove the filter on the popup', () => {
    render(
      <MockedProvider>
        <ChipList />
      </MockedProvider>
    )
    fireEvent.click(screen.getByRole('button', { name: /See other filters/i }))
    fireEvent.click(screen.getAllByTestId('ClearIcon')[3])
    expect(screen.queryByText('Filter 4')).not.toBeInTheDocument()
  })

  it('removes all filters when the clear all button is clicked', () => {
    render(
      <MockedProvider>
        <ChipList />
      </MockedProvider>
    )
    fireEvent.click(screen.getByRole('button', { name: /remove all filters/i }))
    expect(screen.queryByText('Filter 1')).not.toBeInTheDocument()
    expect(screen.queryByText('Filter 2')).not.toBeInTheDocument()
    expect(screen.queryByText('Filter 3')).not.toBeInTheDocument()
    expect(screen.queryByText('Filter 4')).not.toBeInTheDocument()
  })
})
