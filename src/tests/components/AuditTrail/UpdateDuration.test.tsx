import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import UpdateDuration from '@components/AuditTrail/AuditTrailContentCases/UpdateDuration'
import mockAuditTrailEntry from '../../__mocks__/auditTrailEntry.mock'

// Mock dependencies
jest.mock('@components/Icons/UserIcon/UserIcon', () => jest.fn(() => <div data-testid="UserIcon" />))
jest.mock('@components/Icons/TaskIcon/TaskIcon', () => jest.fn(() => <div data-testid="TaskIcon" />))
jest.mock('@components/Icons/ScheduleIcon/ScheduleIcon', () => jest.fn(() => <div data-testid="ScheduleIcon" />))

describe('UpdateDuration Component', () => {
  const mockEntry = mockAuditTrailEntry

  xit('renders correctly when is not collapsed', async () => {
    render(<UpdateDuration entry={mockEntry} isOpen={false} isCollapsed={false} />)

    const scheduleIcons = screen.getAllByTestId('ScheduleIcon')
    expect(screen.getByTestId('UserIcon')).toBeInTheDocument()
    expect(screen.getByTestId('TaskIcon')).toBeInTheDocument()

    expect(scheduleIcons.length).toBeGreaterThan(0)
    expect(screen.getByText(mockEntry.details.oldAssignment.resourceName)).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.taskName)).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.projectName)).toBeInTheDocument()
    expect(
      screen.getByText(`${mockEntry.details.oldAssignment.startDate} - ${mockEntry.details.oldAssignment.dueDate}`)
    ).toBeInTheDocument()
    expect(
      screen.getByText(`${mockEntry.details.newAssignment.startDate} - ${mockEntry.details.newAssignment.dueDate}`)
    ).toBeInTheDocument()
  })

  xit('renders correctly when isCollapsed is true and isOpen is false', () => {
    render(<UpdateDuration entry={mockEntry} isOpen={false} isCollapsed={false} />)
    const scheduleIcons = screen.getAllByTestId('ScheduleIcon')
    expect(screen.getByTestId('UserIcon')).toBeInTheDocument()
    expect(screen.getByTestId('TaskIcon')).toBeInTheDocument()

    expect(scheduleIcons.length).toBeGreaterThan(0)
    expect(screen.getByText(mockEntry.details.oldAssignment.resourceName)).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.taskName)).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.projectName)).toBeInTheDocument()
    expect(
      screen.getByText(`${mockEntry.details.oldAssignment.startDate} - ${mockEntry.details.oldAssignment.dueDate}`)
    ).toBeInTheDocument()
    expect(
      screen.getByText(`${mockEntry.details.newAssignment.startDate} - ${mockEntry.details.newAssignment.dueDate}`)
    ).toBeInTheDocument()
  })

  test('renders correctly when isCollapsed is true and isOpen is true', () => {
    render(<UpdateDuration entry={mockEntry} isOpen={true} isCollapsed={true} />)

    expect(screen.getByText('Changed duration for')).toBeInTheDocument()
  })
})
