import { render, screen, waitFor } from '@testing-library/react'
import { useDebounce } from '../../hooks/useDebounce'

interface Project {
  name: string
}

const projects: Project[] = [{ name: 'Project Alpha' }, { name: 'project beta' }, { name: 'PROJECT Gamma' }]

// Test component to use the hook
const TestComponent: React.FC<{ searchTerm: string }> = ({ searchTerm }) => {
  const filteredProjects = useDebounce(
    projects,
    (project) => project.name.toLowerCase().includes(searchTerm.toLowerCase()),
    300
  )

  return (
    <ul>
      {filteredProjects.map((project) => (
        <li key={project.name}>{project.name}</li>
      ))}
    </ul>
  )
}

describe('useDebounce', () => {
  it('should return the initial list when no search term is provided', () => {
    render(<TestComponent searchTerm="" />)
    const listItems = screen.getAllByRole('listitem')
    expect(listItems).toHaveLength(projects.length)
  })

  xit('should filter the list based on the search term', async () => {
    render(<TestComponent searchTerm="alpha" />)

    await waitFor(() => {
      const listItems = screen.getAllByRole('listitem')
      expect(listItems).toHaveLength(1)
    })
  })

  it('should debounce the filtering', async () => {
    const { rerender } = render(<TestComponent searchTerm="alpha" />)

    await waitFor(() => {
      const listItems = screen.getAllByRole('listitem')
      expect(listItems[0]).toHaveTextContent('Project Alpha')
    })

    rerender(<TestComponent searchTerm="beta" />)

    await waitFor(() => {
      const listItems = screen.getAllByRole('listitem')
      expect(listItems).toHaveLength(1)
    })
  })
})
