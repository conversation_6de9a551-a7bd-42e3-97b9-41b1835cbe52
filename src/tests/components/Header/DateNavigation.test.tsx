import { render, screen, fireEvent } from '@testing-library/react'
import DateNavigation from '@components/Header/DateNavigation/DateNavigation'

import updateActiveDates from '@utils/updateActiveDates'

jest.mock('@utils/updateActiveDates', () => jest.fn())

describe('DateNavigation', () => {
  it('renders the component', () => {
    render(<DateNavigation />)
    const dateNavigation = screen.getByTestId('date-navigation')
    expect(dateNavigation).toBeInTheDocument()
  })

  it('calls updateActiveDates with "prev" when previous button is clicked', () => {
    const updateActiveDatesMock = updateActiveDates as jest.Mock

    render(<DateNavigation />)
    const prevButton = screen.getByRole('button', { name: /previous/i })
    fireEvent.click(prevButton)

    expect(updateActiveDatesMock).toHaveBeenCalledWith('prev', 1)
  })

  it('calls updateActiveDates with "" when today button is clicked', () => {
    const updateActiveDatesMock = updateActiveDates as jest.Mock

    render(<DateNavigation />)
    const homeButton = screen.getByRole('button', { name: /today/i })
    fireEvent.click(homeButton)

    expect(updateActiveDatesMock).toHaveBeenCalledWith('')
  })

  it('calls updateActiveDates with "next" when next button is clicked', () => {
    const updateActiveDatesMock = updateActiveDates as jest.Mock

    render(<DateNavigation />)
    const nextButton = screen.getByRole('button', { name: /next/i })
    fireEvent.click(nextButton)

    expect(updateActiveDatesMock).toHaveBeenCalledWith('next', 1)
  })
})
