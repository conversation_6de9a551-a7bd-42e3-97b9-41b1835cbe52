import { render, waitFor } from '@testing-library/react'
import { useRouter } from 'next/router'
import PluginComponent from '../../../pages/Workfront/Plugin'
import apolloClientWorkfront from '@gql/client/apolloClientWorkfront'
import { wflionLoginIDVar } from '@gql/client/localState'

// Mock Next.js router
const mockPush = jest.fn()
jest.mock('next/router', () => ({
  useRouter: jest.fn()
}))

// Mock Apollo client
jest.mock('@gql/client/apolloClientWorkfront', () => ({
  query: jest.fn()
}))

// Mock reactive variables
jest.mock('@gql/client/localState', () => ({
  wflionLoginIDVar: jest.fn()
}))

// Mock workfront utilities
jest.mock('@utils/workfront', () => ({
  workfrontConfig: {
    id: 'test-id',
    uixId: 'test-uix-id',
    instanceId: 'test-instance-id',
    isInIframe: true
  }
}))

jest.mock('@utils/workfront-debug', () => ({
  debugWorkfrontConfig: jest.fn()
}))

// Mock Adobe UIX guest module
const mockAttach = jest.fn()
jest.mock('@adobe/uix-guest', () => ({
  attach: mockAttach
}))

// Mock console methods
const consoleSpy = {
  log: jest.spyOn(console, 'log'),
  error: jest.spyOn(console, 'error')
}

// Mock window.location
const mockLocation = {
  href: ''
}

describe('PluginComponent', () => {
  const mockRouter = {
    push: mockPush,
    pathname: '/workfront/plugin',
    query: {},
    asPath: '/workfront/plugin'
  }

  const mockConnection = {
    sharedContext: {
      get: jest.fn()
    }
  }

  beforeEach(() => {
    jest.clearAllMocks()
    consoleSpy.log.mockClear()
    consoleSpy.error.mockClear()
    ;(useRouter as jest.Mock).mockReturnValue(mockRouter)

    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: mockLocation,
      writable: true
    })

    // Reset location href
    mockLocation.href = ''
  })

  it('renders loading message', () => {
    const { getByText } = render(<PluginComponent />)
    expect(getByText('Loading...')).toBeInTheDocument()
  })

  it('calls debugWorkfrontConfig on mount', async () => {
    const { debugWorkfrontConfig } = await import('@utils/workfront-debug')

    render(<PluginComponent />)

    expect(debugWorkfrontConfig).toHaveBeenCalled()
  })

  it('logs initialization config', async () => {
    render(<PluginComponent />)

    // Wait for useEffect to run
    await new Promise((resolve) => setTimeout(resolve, 100))

    expect(consoleSpy.log).toHaveBeenCalledWith(
      'Plugin Component - Initializing with config:',
      expect.objectContaining({
        id: 'test-id',
        uixId: 'test-uix-id',
        instanceId: 'test-instance-id',
        isInIframe: true
      })
    )
  })

  it('attempts to attach with Adobe UIX', async () => {
    mockAttach.mockResolvedValue(mockConnection)

    render(<PluginComponent />)

    await waitFor(() => {
      expect(mockAttach).toHaveBeenCalledWith({ id: 'test-uix-id' })
    })
  })

  it('handles attachment errors gracefully', async () => {
    const mockError = new Error('Attachment failed')
    mockAttach.mockRejectedValue(mockError)

    render(<PluginComponent />)

    await waitFor(() => {
      expect(consoleSpy.error).toHaveBeenCalledWith('Plugin Component - Error from connection:', mockError)
    })
  })

  it('fetches Workfront user data when connection is established', async () => {
    const mockWorkfrontData = {
      getWorkfrontUser: {
        lionLoginID: 'test-lion-id'
      }
    }

    const mockUserData = {
      getUserByLLID: {
        employeeCode: 'EMP123',
        businessRole: 'Manager',
        userName: 'Test User'
      }
    }

    mockConnection.sharedContext.get.mockImplementation((key: string) => {
      switch (key) {
        case 'auth':
          return { imsToken: 'test-token' }
        case 'user':
          return { ID: 'test-user-id' }
        case 'hostname':
          return 'test-hostname'
        case 'objCode':
          return 'test-obj-code'
        case 'objID':
          return 'test-obj-id'
        default:
          return null
      }
    })
    ;(apolloClientWorkfront.query as jest.Mock)
      .mockResolvedValueOnce({ data: mockWorkfrontData })
      .mockResolvedValueOnce({ data: mockUserData })

    mockAttach.mockResolvedValue(mockConnection)

    render(<PluginComponent />)

    await waitFor(() => {
      expect(wflionLoginIDVar).toHaveBeenCalledWith('test-lion-id')
    })

    await waitFor(() => {
      expect(mockLocation.href).toBe('/rm/?employeeCode=EMP123&userName=Test+User&businessRole=Manager')
    })
  })

  it('handles array business roles correctly', async () => {
    const mockWorkfrontData = {
      getWorkfrontUser: {
        lionLoginID: 'test-lion-id'
      }
    }

    const mockUserData = {
      getUserByLLID: {
        employeeCode: 'EMP123',
        businessRole: ['Manager', 'Admin'],
        userName: 'Test User'
      }
    }

    mockConnection.sharedContext.get.mockImplementation((key: string) => {
      switch (key) {
        case 'auth':
          return { imsToken: 'test-token' }
        case 'user':
          return { ID: 'test-user-id' }
        case 'hostname':
          return 'test-hostname'
        default:
          return null
      }
    })
    ;(apolloClientWorkfront.query as jest.Mock)
      .mockResolvedValueOnce({ data: mockWorkfrontData })
      .mockResolvedValueOnce({ data: mockUserData })

    mockAttach.mockResolvedValue(mockConnection)

    render(<PluginComponent />)

    await waitFor(() => {
      expect(mockLocation.href).toBe('/rm/?employeeCode=EMP123&userName=Test+User&businessRole=Manager%2CAdmin')
    })
  })

  it('handles fetch user errors gracefully', async () => {
    const mockError = new Error('Fetch failed')

    mockConnection.sharedContext.get.mockReturnValue({ imsToken: 'test-token' })
    ;(apolloClientWorkfront.query as jest.Mock).mockRejectedValue(mockError)
    mockAttach.mockResolvedValue(mockConnection)

    render(<PluginComponent />)

    // Wait for async operations to complete
    await new Promise((resolve) => setTimeout(resolve, 200))

    expect(consoleSpy.log).toHaveBeenCalledWith('Error', mockError)
  })

  it('does not fetch user data when connection is not established', async () => {
    mockAttach.mockResolvedValue(null)

    render(<PluginComponent />)

    // Wait a bit to ensure no queries are made
    await new Promise((resolve) => setTimeout(resolve, 100))

    expect(apolloClientWorkfront.query).not.toHaveBeenCalled()
  })
})
