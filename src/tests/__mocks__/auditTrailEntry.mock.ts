import { type AuditTrailEntry } from '@typeDefs/AuditTrailEntry'

const mockAuditTrailEntry: AuditTrailEntry = {
  id: '1',
  date: '2025/04/07',
  time: '16:06',
  action: 'UPDATED',
  details: {
    oldAssignment: {
      externalId: '/RLTimePhaseDaily/66gxi2jymr2okewqzvxgjy0x32224',
      hoursPerDay: 5,
      startDate: '04/07/2025',
      dueDate: '04/11/2025',
      assignmentStatus: 'Requested',
      resourceName: '<PERSON>a10',
      resourceId: '/User/6df87aac-1894-4bdd-97e4-45f5b2807c41_537E',
      taskName: 'POC',
      projectName: 'Vikash- Nike Web development',
      isPlaceholder: false
    },
    newAssignment: {
      externalId: '/RLTimePhaseDaily/66gxi2jymr2okewqzvxgjy0x32224',
      hoursPerDay: 8,
      startDate: '04/09/2025',
      dueDate: '04/13/2025',
      assignmentStatus: 'Approved',
      resourceName: '<PERSON>qa<PERSON>',
      resourceId: '/User/6df87aac-1894-4bdd-97e4-45f5b2807c41_537E',
      taskName: 'POC',
      projectName: 'Vikash- Nike Web development',
      isPlaceholder: false
    }
  }
}

export default mockAuditTrailEntry
