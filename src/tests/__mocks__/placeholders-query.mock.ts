import { activeSortVar } from '@gql/client/localState'
import { PLACEHOLDERS_QUERY } from '@gql/queries/placeholders.query'

export const mockPlaceHoldersQuery = {
  query: PLACEHOLDERS_QUERY,
  variables: {
    params: {
      userId: '10082922',
      pageNumber: 1,
      pageSize: 1000,
      sort: { field: activeSortVar().field, order: activeSortVar().order }
    }
  },
  data: {
    placeholders: {
      items: [
        {
          id: 'placeholder-1',
          name: 'Placeholder one'
        }
      ],
      totalCount: 1
    }
  }
}
