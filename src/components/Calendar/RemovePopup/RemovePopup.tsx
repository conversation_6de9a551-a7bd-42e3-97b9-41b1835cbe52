import type React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Fade, CircularProgress } from '@mui/material'

import { palette, Typography } from 'lion-ui'

interface RemoveAllocationPopupProps {
  anchorEl: HTMLElement | null
  message: string
  closeText: string
  removeText: string
  onRemove: () => void
  onClose: () => void
  isLoading?: boolean
}

const RemovePopup: React.FC<RemoveAllocationPopupProps> = ({
  anchorEl,
  message,
  closeText,
  removeText,
  onRemove,
  onClose,
  isLoading
}) => {
  const handleRemove = () => {
    onRemove()
  }

  const handleClose = () => {
    onClose()
  }

  const openPopper = Boolean(anchorEl)
  const id = openPopper ? 'simple-popper' : undefined

  return (
    <Popper
      id={id}
      open={openPopper}
      anchorEl={anchorEl}
      transition
      style={{ zIndex: 1300 }}
      placement="top-start"
      sx={{ boxShadow: '0px 4px 24px 0px rgba(0, 0, 0, 0.10)', borderRadius: '0.5rem', width: '20.9375rem' }}
    >
      {({ TransitionProps }) => (
        <Fade {...TransitionProps} timeout={350}>
          <Box sx={{ bgcolor: 'background.paper', borderRadius: '0.5rem' }}>
            <Box sx={{ padding: '1.25rem 1.5rem' }}>
              <Typography variant="BodyMRegular">{message}</Typography>
            </Box>
            <hr />
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                padding: '1.25rem 1.5rem',
                gap: '1rem',
                alignItems: 'center',
                alignSelf: 'stretch'
              }}
            >
              <Button
                sx={{
                  borderColor: palette.secondary.main,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-start'
                }}
                variant="outlined"
                onClick={handleClose}
                data-testid="close-remove-allocation-button"
              >
                {closeText}
              </Button>
              <Button
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-start'
                }}
                onClick={handleRemove}
                color="error"
                data-testid="confirm-remove-allocation-button"
              >
                {removeText}{' '}
                {isLoading && (
                  <CircularProgress
                    size={24}
                    sx={{
                      color: palette.primary.main,
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      marginTop: '-12px',
                      marginLeft: '-12px'
                    }}
                  />
                )}
              </Button>
            </Box>
          </Box>
        </Fade>
      )}
    </Popper>
  )
}

export default RemovePopup
