import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import Custom404 from '../../pages/404' // Import the custom500 component correctly
import { ErrorFallback } from '@components/ErrorBoundary/ErrorFallback'

// Mock ErrorFallback component
jest.mock('@components/ErrorBoundary/ErrorFallback', () => {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  return { ErrorFallback: jest.fn(() => <div>ErrorFallback Component</div>) }
})

describe('Custom404 Component', () => {
  test('renders ErrorFallback component with correct props', () => {
    render(<Custom404 />)

    expect(screen.getByText('ErrorFallback Component')).toBeInTheDocument()
    expect(ErrorFallback).toHaveBeenCalledWith(
      expect.objectContaining({
        title: 'Error: 404 Not Found Error',
        subtitle:
          'The page you are looking for cannot be found. Please check the URL and try again, or contact the system administrator for assistance.'
      }),
      {}
    )
  })
})
