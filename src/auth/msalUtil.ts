import { msalInstance as instance } from './msalConfig'
import { InteractionRequiredAuthError } from '@azure/msal-browser'

let scope = 'api://a0ce10c3-4f71-4f9a-bff6-db67e89174b1/user_api_access'

if (typeof window !== 'undefined') {
  if (window.location.hostname === 'roar.marcel.ai') {
    console.log('PRODUCTION')
    // TODO: Remove this when we have a proper prod environment
    scope = 'api://a0ce10c3-4f71-4f9a-bff6-db67e89174b1/user_api_access'
  }
}

export const getAccessToken = async (): Promise<string | undefined> => {
  try {
    let activeAccount = instance.getActiveAccount()

    if (!activeAccount) {
      const accounts = instance.getAllAccounts()
      if (accounts.length === 0) {
        throw new Error('No user account found. Please log in.')
      }
      activeAccount = accounts[0]
      instance.setActiveAccount(activeAccount)
    }

    const response = await instance.acquireTokenSilent({
      scopes: [scope]
    })

    return response.accessToken
  } catch (error) {
    console.error('%cToken Refresh Failed', 'color: red; font-size: 2em;', error)

    if (error instanceof InteractionRequiredAuthError) {
      console.log('%cUser interaction required. Redirecting to login...', 'color: orange; font-size: 2em;')
      await instance.loginRedirect({
        scopes: [scope]
      })
    }
  }
}
