import { fireEvent, render, screen } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import Alert from '@components/Alert/Alert'
import { showAlertVar } from '@gql/client/localState'

describe('Alert Component', () => {
  test('renders the alert message', () => {
    showAlertVar({ show: true, message: 'Test Alert' })
    render(<Alert />)
    const alertElement = screen.getByText(/Test Alert/i)
    expect(alertElement).toBeInTheDocument()
  })

  test('applies the correct class based on success type', () => {
    showAlertVar({ show: true, message: 'Success Alert', severity: 'success' })
    render(<Alert />)
    screen.getByText(/Success Alert/i)
    const alertContainer = screen.getByRole('alert')
    expect(alertContainer).toHaveClass('MuiAlert-colorSuccess')
  })

  test('applies the correct class based on error type', () => {
    showAlertVar({ show: true, message: 'Error Alert', severity: 'error' })
    render(<Alert />)
    screen.getByText(/Error Alert/i)
    const alertErrorContainer = screen.getByRole('alert')
    expect(alertErrorContainer).toHaveClass('MuiAlert-colorError')
  })

  test('does not render when is disabled and no message', () => {
    showAlertVar({ show: false, message: '' })
    const { container } = render(<Alert />)
    expect(container).toBeEmptyDOMElement()
  })

  test('closes the alert when close action is triggered', () => {
    showAlertVar({ show: true, message: 'Closable Alert', severity: 'info' })
    render(<Alert />)
    const closeButton = screen.getByRole('button', { name: /close/i })
    fireEvent.click(closeButton)
    expect(screen.queryByText(/Closable Alert/i)).not.toBeInTheDocument()
  })
})
