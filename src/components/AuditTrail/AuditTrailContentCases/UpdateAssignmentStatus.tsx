import type React from 'react'
import { Box, palette, Typography } from 'lion-ui'
import type AuditTrailContentCaseProps from './AuditTrailContentCase.props'
import AuditTrailGoToDetails from '@components/AuditTrail/AuditTrailGoToDetails/AuditTrailGoToDetails'
import UserIcon from '@components/Icons/UserIcon/UserIcon'
import TaskIcon from '@components/Icons/TaskIcon/TaskIcon'
import getLocaleDateFormat from '@utils/getLocaleDateFormat'
import ScheduleIcon from '@components/Icons/ScheduleIcon/ScheduleIcon'

const UpdateAssignmentStatus = ({ entry, isOpen, isCollapsed }: AuditTrailContentCaseProps): React.JSX.Element => {
  const formattedNewStartDate = getLocaleDateFormat(entry.details.newAssignment.startDate)
  const formattedNewDueDate = getLocaleDateFormat(entry.details.newAssignment.dueDate)

  return (
    <Box sx={{ color: '#4D4D4D' }}>
      {!isCollapsed && (
        <Box sx={{ paddingLeft: '10rem', width: '35rem' }}>
          <UserIcon width="12" height="12" />{' '}
          <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
            {entry.details.newAssignment.resourceName}
          </Typography>{' '}
          in{' '}
          <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
            {entry.details.newAssignment.projectName}
          </Typography>{' '}
          under <TaskIcon width="12" height="12" />{' '}
          <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
            {entry.details.newAssignment.taskName}
          </Typography>{' '}
          on <ScheduleIcon width="14" height="14" />{' '}
          <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
            {formattedNewStartDate} - {formattedNewDueDate}
          </Typography>
          <AuditTrailGoToDetails
            assignmentId={entry.details.newAssignment.externalId}
            resourceId={entry.details.newAssignment.resourceId}
          />
        </Box>
      )}

      {isCollapsed && (
        <Box>
          {!isOpen && (
            <Box
              sx={{
                display: 'inline-block',
                maxWidth: '25rem',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }}
            >
              {entry.details.newAssignment.assignmentStatus} request for <UserIcon width="12" height="12" />{' '}
              <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
                {entry.details.newAssignment.resourceName}
              </Typography>{' '}
              in{' '}
              <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
                {entry.details.newAssignment.projectName}
              </Typography>{' '}
              under <TaskIcon width="12" height="12" />{' '}
              <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
                {entry.details.newAssignment.taskName}
              </Typography>{' '}
              on <ScheduleIcon width="14" height="14" />{' '}
              <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
                {formattedNewStartDate} - {formattedNewDueDate}
              </Typography>
            </Box>
          )}

          {isOpen && (
            <Box
              sx={{
                display: 'inline-block',
                maxWidth: '21rem',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }}
            >
              {entry.details.newAssignment.assignmentStatus} request for
            </Box>
          )}
        </Box>
      )}
    </Box>
  )
}

export default UpdateAssignmentStatus
