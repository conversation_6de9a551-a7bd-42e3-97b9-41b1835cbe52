import { IconButton, palette, Typography } from 'lion-ui'

import { type SeeDetailProps } from './SeeDetails.props'
import OpenInNewIcon from '@components/Icons/OpenInNewIcon/OpenInNewIcon'

const SeeDetails: React.FC<SeeDetailProps> = ({ externalLink, showText = true }: SeeDetailProps) => {
  return (
    <Typography
      variant="BodySBold"
      sx={{ color: palette.grey['700'], display: 'flex', alignItems: 'center', paddingTop: showText ? 3 : 0 }}
    >
      {showText ? 'See details' : ''}
      <a href={externalLink ?? '#'} target="_blank" rel="noreferrer">
        <IconButton aria-label="See details">
          <OpenInNewIcon fillColor={palette['500-Accent-1'].main} />
        </IconButton>
      </a>
    </Typography>
  )
}

export default SeeDetails
