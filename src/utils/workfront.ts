import type React from 'react'
import { getAuthEnvConfig } from './environments'

const isDev = process.env.NODE_ENV === 'development'
const basePath = isDev ? '/Workfront' : '/rm/Workfront'
const url = `${basePath}/Plugin/?isWorkfront=true`

function getInstanceIdentifier(): string {
  if (typeof window === 'undefined') return 'server'

  const hostname = window.location.hostname

  if (hostname.includes('dev1')) return 'dev1'
  if (hostname.includes('qa1')) return 'qa1'
  if (hostname.includes('uat')) return 'uat'
  if (hostname === 'roar.marcel.ai') return 'prod'

  if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) {
    return 'local'
  }

  return 'default'
}

const instanceId = getInstanceIdentifier()

export const isWorkfrontPage = (): boolean => {
  if (typeof window === 'undefined') return false

  const pathname = window.location.pathname
  return pathname.includes('/Workfront/Register') || pathname.includes('/Workfront/Plugin')
}

export const isWorkfrontComponent = (Component: React.ComponentType<unknown> | undefined): boolean => {
  return Component?.name === 'ExtensionRegistration' || Component?.name === 'PluginComponent'
}

type Env = 'DEV1' | 'QA1' | 'UAT'

const ENV_MAP: Record<
  Env | 'DEFAULT',
  {
    id: string
    mainMenuId: string
    menuLabel: string
  }
> = {
  DEV1: {
    id: 'roar-dev',
    mainMenuId: 'rm-dev',
    menuLabel: 'RM Calendar (DEV)'
  },
  QA1: {
    id: 'roar-qa',
    mainMenuId: 'rm-qa',
    menuLabel: 'RM Calendar (QA)'
  },
  UAT: {
    id: 'roar-uat',
    mainMenuId: 'rm-uat',
    menuLabel: 'RM Calendar (UAT)'
  },
  DEFAULT: {
    id: 'roar-rm',
    mainMenuId: 'rm',
    menuLabel: 'RM Calendar'
  }
}

function getWorkfrontConfig(): {
  id: string
  mainMenuId: string
  url: string
  menuLabel: string
  uixId: string
  uixMainMenuId: string
  instanceId: string
  isInIframe: boolean
} {
  const { env } = getAuthEnvConfig()
  const config = (ENV_MAP as Record<string, (typeof ENV_MAP)['DEFAULT']>)[env] ?? ENV_MAP.DEFAULT

  const isInIframe = typeof window !== 'undefined' && window.self !== window.top

  const originalId = config.id
  const originalMainMenuId = config.mainMenuId

  let uixId = originalId
  let uixMainMenuId = originalMainMenuId

  if (isInIframe) {
    uixId = `${originalId}-${instanceId}`
    uixMainMenuId = `${originalMainMenuId}-${instanceId}`
  }

  console.log('Workfront config generated:', {
    env,
    originalId,
    uixId,
    instanceId,
    isInIframe,
    hostname: typeof window !== 'undefined' ? window.location.hostname : 'server',
    note: isInIframe ? 'Using unique UIX IDs for iframe context' : 'Using original IDs'
  })

  return {
    id: originalId,
    mainMenuId: originalMainMenuId,
    url,
    menuLabel: config.menuLabel,
    uixId,
    uixMainMenuId,
    instanceId,
    isInIframe
  }
}

export const workfrontConfig = getWorkfrontConfig()
