import { getAllActiveFilters, removeFilter, removeAllFilters, getIconByCategory } from '@utils/filterSearch'
import { availableFilters } from '@constants/filters'
import { type ActiveFilters } from '@typeDefs/ActiveFilters'
import { type LabelItem } from '@typeDefs/LabelItem'

describe('filterSearch utility functions', () => {
  const mockActiveFilters: ActiveFilters = {
    projects: [{ id: '1', name: 'Project 1' }],
    resources: [{ id: '2', name: 'Resource 1' }],
    brands: [],
    usersGroups: [],
    tasks: [],
    taskStates: [],
    placeholders: []
  }

  const mockFilter: LabelItem = { id: '1', name: 'Project 1', category: 'projects' }

  test('getAllActiveFilters should return all active filters', () => {
    const result = getAllActiveFilters(mockActiveFilters)
    expect(result).toEqual([
      { id: '1', name: 'Project 1' },
      { id: '2', name: 'Resource 1' }
    ])
  })

  test('removeFilter should remove a specific filter', () => {
    const result = removeFilter(mockActiveFilters, mockFilter)
    expect(result.projects).toEqual([])
  })

  test('removeAllFilters should remove all filters', () => {
    const result = removeAllFilters()
    expect(result).toEqual({
      projects: [],
      resources: [],
      brands: [],
      usersGroups: [],
      tasks: [],
      taskStates: [],
      placeholders: []
    })
  })

  test('getIconByCategory should return the correct icon', () => {
    const category = 'projects'
    const expectedIcon = availableFilters.find((filter) => filter.id === category)?.icon
    const result = getIconByCategory(category)
    expect(result).toEqual(expectedIcon)
  })
})
