import { type IconProps } from '../Icon.props'

const CloseCircleIcon = ({ height, width, fillColor }: IconProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={height}
      height={width}
      viewBox="0 0 14 15"
      style={{ top: '0px', position: 'relative' }}
    >
      <path
        d="M7 8.53912L9.02463 10.5471C9.12946 10.663 9.26207 10.7226 9.42246 10.7257C9.58284 10.7289 9.72414 10.6682 9.84636 10.5438C9.96376 10.4305 10.0225 10.2956 10.0225 10.139C10.0225 9.98236 9.96376 9.84135 9.84636 9.71594L7.82173 7.71739L9.84636 5.69276C9.95119 5.58792 10.0068 5.45531 10.013 5.29492C10.0193 5.13454 9.96427 4.99325 9.84788 4.87102C9.72409 4.75362 9.58436 4.69492 9.42868 4.69492C9.273 4.69492 9.13832 4.75362 9.02463 4.87102L7 6.89566L4.99203 4.87102C4.87608 4.76619 4.73949 4.71063 4.58224 4.70436C4.425 4.69807 4.29082 4.75312 4.17971 4.86951C4.05121 4.99329 3.98696 5.13303 3.98696 5.28871C3.98696 5.44438 4.05121 5.57907 4.17971 5.69276L6.17826 7.71739L4.1703 9.72536C4.05435 9.8413 3.99481 9.97789 3.99166 10.1351C3.98853 10.2924 4.04916 10.4266 4.17356 10.5377C4.28685 10.6662 4.4218 10.7304 4.57841 10.7304C4.73502 10.7304 4.87603 10.6662 5.00145 10.5377L7 8.53912ZM7.0013 14.6978C6.0426 14.6978 5.1372 14.516 4.28511 14.1522C3.43302 13.7885 2.69095 13.2905 2.0589 12.6585C1.42684 12.0264 0.928934 11.2848 0.565179 10.4336C0.201434 9.58235 0.0195618 8.67739 0.0195618 7.71869C0.0195618 6.75714 0.201762 5.85065 0.566162 4.99919C0.930562 4.14774 1.42937 3.40498 2.06258 2.77091C2.69579 2.13683 3.43716 1.63774 4.2867 1.27366C5.13623 0.909577 6.03997 0.727539 6.99791 0.727539C7.95988 0.727539 8.86705 0.909283 9.71943 1.27277C10.5718 1.63626 11.3148 2.13461 11.9484 2.76782C12.5819 3.40103 13.0806 4.14373 13.4443 4.99592C13.808 5.84811 13.9898 6.75573 13.9898 7.71877C13.9898 8.68142 13.8078 9.58634 13.4437 10.4335C13.0796 11.2807 12.5806 12.021 11.9465 12.6545C11.3124 13.2879 10.5701 13.7868 9.71949 14.1512C8.86891 14.5156 7.96284 14.6978 7.0013 14.6978ZM6.99785 13.3116C8.54507 13.3116 9.8646 12.7656 10.9564 11.6736C12.0483 10.5816 12.5942 9.26355 12.5942 7.71954C12.5942 6.17232 12.049 4.85279 10.9586 3.76096C9.8682 2.66911 8.54781 2.12319 6.99745 2.12319C5.45819 2.12319 4.14143 2.66839 3.04718 3.75879C1.95292 4.84919 1.4058 6.16957 1.4058 7.71994C1.4058 9.2592 1.9518 10.576 3.04381 11.6702C4.13582 12.7645 5.45383 13.3116 6.99785 13.3116Z"
        fill="#202020"
      />
    </svg>
  )
}

export default CloseCircleIcon
