import { type Agency } from './Agency'
import { type Pagination } from './common/Pagination'
import { type CostCenter } from './CostCenter'

export interface Placeholder {
  id: string
  name: string
  minimumAgencyHoursPerDay: number
}

export interface PlaceholderResponse extends Pagination {
  items: Placeholder[]
}

export interface PlaceholderOrgStructure {
  businessUnits: Agency[] | null
  locations: string[] | null
  costCenters: CostCenter[] | null
}
