import { type IconProps } from '../Icon.props'

const ReturnArrowIcon = ({ height, width, rotation }: IconProps) => {
  return (
    <svg
      style={{ transform: `rotate(${rotation}deg)` }}
      width={height}
      height={width}
      viewBox="0 0 18 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="return">
        <path
          id="Vector"
          d="M6.69045 14.1584L1.89697 9.36495L6.69045 4.57147L7.80322 5.67363L4.8929 8.60516H14.5434V5.36387H16.1135V10.1459H4.91165L7.80322 13.0481L6.69045 14.1584Z"
          fill="#202020"
        />
      </g>
    </svg>
  )
}

export default ReturnArrowIcon
