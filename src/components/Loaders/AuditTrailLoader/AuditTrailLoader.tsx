import { Box, Skeleton } from 'lion-ui'
import type AuditTrailLoaderProps from './AuditTrailLoader.props'

export const AuditTrailLoader = (props: AuditTrailLoaderProps) => {
  const { numOfRows } = props
  const rows = []

  for (let i = 0; i < numOfRows; i++) {
    rows.push(
      <Box data-testid="audit-trail-loader" key={i} sx={{ display: 'flex', gap: 2, padding: 2 }}>
        <Box sx={{ display: 'flex', gap: 8, justifyContent: 'space-evenly', width: '100%' }}>
          <Skeleton animation="wave" height={80} sx={{ width: '100%' }} />
        </Box>
      </Box>
    )
  }

  return <>{rows}</>
}
