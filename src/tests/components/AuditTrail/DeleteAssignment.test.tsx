import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import mockAuditTrailEntry from '../../__mocks__/auditTrailEntry.mock'
import DeleteAssignment from '@components/AuditTrail/AuditTrailContentCases/DeleteAssignment'

// Mock dependencies
jest.mock('@components/Icons/UserIcon/UserIcon', () => jest.fn(() => <div data-testid="UserIcon" />))
jest.mock('@components/Icons/TaskIcon/TaskIcon', () => jest.fn(() => <div data-testid="TaskIcon" />))
jest.mock('@components/Icons/ScheduleIcon/ScheduleIcon', () => jest.fn(() => <div data-testid="ScheduleIcon" />))

jest.mock('@utils/getLocaleDateFormat', () => jest.fn((date) => `${date}`))

describe('DeleteAssignment Component', () => {
  const mockEntry = mockAuditTrailEntry

  test('renders correctly when isCollapsed is false', () => {
    render(<DeleteAssignment entry={mockEntry} isOpen={false} isCollapsed={false} />)

    expect(screen.getByTestId('UserIcon')).toBeInTheDocument()
    expect(screen.getByTestId('TaskIcon')).toBeInTheDocument()
    expect(screen.getByTestId('ScheduleIcon')).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.resourceName)).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.taskName)).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.projectName)).toBeInTheDocument()
    expect(
      screen.getByText(`${mockEntry.details.oldAssignment.startDate} - ${mockEntry.details.oldAssignment.dueDate}`)
    ).toBeInTheDocument()
  })

  test('renders correctly when isCollapsed is true and isOpen is false', () => {
    render(<DeleteAssignment entry={mockEntry} isOpen={false} isCollapsed={true} />)

    expect(screen.getByTestId('UserIcon')).toBeInTheDocument()
    expect(screen.getByTestId('TaskIcon')).toBeInTheDocument()
    expect(screen.getByTestId('ScheduleIcon')).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.resourceName)).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.taskName)).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.projectName)).toBeInTheDocument()
    expect(
      screen.getByText(`${mockEntry.details.oldAssignment.startDate} - ${mockEntry.details.oldAssignment.dueDate}`)
    ).toBeInTheDocument()
  })

  test('renders correctly when isCollapsed is true and isOpen is true', () => {
    render(<DeleteAssignment entry={mockEntry} isOpen={true} isCollapsed={true} />)

    expect(screen.getByText('Removed allocation for')).toBeInTheDocument()
  })
})
