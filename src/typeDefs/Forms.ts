import { type Project } from '@typeDefs/Project'
import { type Task } from './Task'
import { type SearchResourcePlaceholder } from './SearchResourcePlaceholder'
import { type NonWorkingDate } from './NonWorkingDate'

export interface AllocationDetailsFormData {
  hoursPerDay: number | undefined
  duration: [Date, Date]
  assignTo: SearchResourcePlaceholder | null
  nonWorkingDates?: NonWorkingDate[]
}

export interface AllocationDetailsFormErrors {
  hoursPerDay: string
  duration: string
  assignTo: string
}

export interface AllocateResourceFormData extends AllocationDetailsFormData {
  project?: Project | null
  task?: Task | null
}

export interface AllocateResourceFormErrors extends AllocationDetailsFormErrors {
  project: string
  task: string
}
