import { type IconProps } from '../Icon.props'

const UserIcon = ({ height, width, rotation, fillColor }: IconProps) => {
  return (
    <svg
      style={{ transform: `rotate(${rotation}deg)` }}
      width={height}
      height={width}
      viewBox="0 0 12 14"
      fill={fillColor}
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="return">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6 8.5C3.51471 8.5 1.5 10.5147 1.5 13C1.5 13.4142 1.16421 13.75 0.75 13.75C0.335786 13.75 0 13.4142 0 13C0 9.68629 2.68629 7 6 7C9.31371 7 12 9.68629 12 13C12 13.4142 11.6642 13.75 11.25 13.75C10.8358 13.75 10.5 13.4142 10.5 13C10.5 10.5147 8.48529 8.5 6 8.5Z"
          fill="#202020"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6 1.75C5.17146 1.75 4.5 2.42146 4.5 3.25C4.5 4.07854 5.17146 4.75 6 4.75C6.82854 4.75 7.5 4.07854 7.5 3.25C7.5 2.42146 6.82854 1.75 6 1.75ZM3 3.25C3 1.59304 4.34304 0.25 6 0.25C7.65696 0.25 9 1.59304 9 3.25C9 4.90696 7.65696 6.25 6 6.25C4.34304 6.25 3 4.90696 3 3.25Z"
          fill="#202020"
        />
      </g>
    </svg>
  )
}

export default UserIcon
