import { generateGridStyles, injectStyles } from '../../utils/gridstackColumnStylesGenerator'

describe('generateGridStyles', () => {
  it('should generate correct styles for 14 columns', () => {
    const styles = generateGridStyles(14)
    expect(styles).toContain('.gs-14 > .grid-stack-item { width: 7.142857142857143%; }')
    expect(styles).toContain(".gs-14 > .grid-stack-item[gs-x='1'] { left: 7.142857142857143%; }")
    expect(styles).toContain(".gs-14 > .grid-stack-item[gs-w='1'] { width: 7.142857142857143%; }")
  })

  it('should generate correct styles for 28 columns', () => {
    const styles = generateGridStyles(28)
    expect(styles).toContain('.gs-28 > .grid-stack-item { width: 3.5714285714285716%; }')
    expect(styles).toContain(".gs-28 > .grid-stack-item[gs-x='1'] { left: 3.5714285714285716%; }")
    expect(styles).toContain(".gs-28 > .grid-stack-item[gs-w='1'] { width: 3.5714285714285716%; }")
  })
})

describe('injectStyles', () => {
  beforeEach(() => {
    document.body.innerHTML = ''
  })

  it('should inject styles into the document body', () => {
    const styles = '.test-class { color: red; }'
    const id = 'test-styles'
    injectStyles(styles, id)

    const styleElement = document.getElementById(id) as HTMLStyleElement
    expect(styleElement).not.toBeNull()
    expect(styleElement).toHaveTextContent(styles)
  })

  it('should update existing style element if it already exists', () => {
    const styles1 = '.test-class { color: red; }'
    const styles2 = '.test-class { color: blue; }'
    const id = 'test-styles'
    injectStyles(styles1, id)
    injectStyles(styles2, id)

    const styleElement = document.getElementById(id) as HTMLStyleElement
    expect(styleElement).not.toBeNull()
    expect(styleElement).toHaveTextContent(styles2)
  })
})
