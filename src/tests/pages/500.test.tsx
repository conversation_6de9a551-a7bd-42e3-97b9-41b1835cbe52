import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import Custom500 from '../../pages/500' // Import the custom500 component correctly
import { ErrorFallback } from '@components/ErrorBoundary/ErrorFallback'

// Mock ErrorFallback component
jest.mock('@components/ErrorBoundary/ErrorFallback', () => {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  return { ErrorFallback: jest.fn(() => <div>ErrorFallback Component</div>) }
})

describe('custom500 Component', () => {
  test('renders ErrorFallback component with correct props', () => {
    render(<Custom500 />)

    expect(screen.getByText('ErrorFallback Component')).toBeInTheDocument()
    expect(ErrorFallback).toHaveBeenCalledWith(
      expect.objectContaining({
        title: 'Error: 500 Server Error',
        subtitle:
          'We could not load the RM Calendar properly. Please try again by reloading the page or contact the system administrator.'
      }),
      {}
    )
  })
})
