import { Html, Head, Main, NextScript } from 'next/document'

const isDev = process.env.NODE_ENV === 'development'

const Document = () => {
  return (
    <Html lang="en">
      <Head />
      {/* eslint-disable */}
      <link href={isDev ? '/gridstack.min.css' : '/rm/gridstack.min.css'} rel="stylesheet" />
      <script async src={isDev ? '/gridstack-all.js' : '/rm/gridstack-all.js'}></script>
      {/* eslint-enable */}
      <body style={{ overflow: 'hidden' }}>
        <Main />
        <NextScript />
      </body>
    </Html>
  )
}

export default Document
