import isToday from '../../utils/isTodayDate'
import dayjs from 'dayjs'

describe('isToday function', () => {
  it("should return true for today's date", () => {
    const today = dayjs().format('YYYY-MM-DD')
    expect(isToday(today)).toBe(true)
  })

  it("should return false for yesterday's date", () => {
    const yesterday = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
    expect(isToday(yesterday)).toBe(false)
  })

  it("should return false for tomorrow's date", () => {
    const tomorrow = dayjs().add(1, 'day').format('YYYY-MM-DD')
    expect(isToday(tomorrow)).toBe(false)
  })

  it('should return false for a random past date', () => {
    const pastDate = '2000-01-01'
    expect(isToday(pastDate)).toBe(false)
  })

  it('should return false for a random future date', () => {
    const futureDate = '2050-12-31'
    expect(isToday(futureDate)).toBe(false)
  })
})
