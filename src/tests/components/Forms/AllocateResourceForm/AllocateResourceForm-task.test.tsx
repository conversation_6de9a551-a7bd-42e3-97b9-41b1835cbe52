// import { render, screen, fireEvent, within } from '@testing-library/react'

// import AllocateResourceForm from '@components/Forms/AllocateResourceForm/AllocateResourceForm'
import apolloClientInit from '@gql/client/apolloClientInit'
// import { ApolloProvider } from '@apollo/client'
import { mockProjectsByProjectManagerQuery } from '../../../__mocks__/projects-query.mock'
import { mockTaskByProjectIdQuery } from '../../../__mocks__/tasks-query.mock'
import { userProfileVar } from '@gql/client/localState'
import { UserProfile } from '@constants/userProfile'

import { GET_RESOURCE_AND_PLACEHOLDER } from '@gql/queries/searchResourcePlaceholder'

// const mockProps = {
//   open: true,
//   onClose: jest.fn()
// }

describe('AllocateResourceForm Component - datepicker field', () => {
  beforeAll(() => {
    apolloClientInit.writeQuery(mockProjectsByProjectManagerQuery)
    apolloClientInit.writeQuery(mockTaskByProjectIdQuery)
    apolloClientInit.writeQuery({
      query: GET_RESOURCE_AND_PLACEHOLDER,
      variables: {
        params: {
          userId: '10082922',
          searchName: ''
        }
      },
      data: {
        getResourceAndPlaceholder: []
      }
    })
    userProfileVar(UserProfile.PROJECT_MANAGER)
  })

  test('returns true for a true value', () => {
    expect(true).toBe(true)
  })

  // test('shows task field disabled if no project selected', () => {
  //   render(
  //     <ApolloProvider client={apolloClientInit}>
  //       <AllocateResourceForm {...mockProps} />
  //     </ApolloProvider>
  //   )
  //   const taskField = screen.getByTestId('task')
  //   // eslint-disable-next-line testing-library/no-node-access
  //   expect(taskField.getElementsByClassName('.Mui-disabled')).toBeTruthy()
  // })

  // test('shows task field enabled and see details if project selected', async () => {
  //   render(
  //     <ApolloProvider client={apolloClientInit}>
  //       <AllocateResourceForm {...mockProps} />
  //     </ApolloProvider>
  //   )

  //   const projectField = screen.getByTestId('project')
  //   projectField.focus()
  //   within(projectField).getByLabelText('Open').click()

  //   const options = await screen.findAllByRole('option')
  //   expect(options.length).toBeGreaterThan(0)

  //   fireEvent.click(options[0]) // Select the first option

  //   const taskField = screen.getByTestId('task')
  //   expect(taskField).not.toHaveClass('Mui-disabled')

  //   const seeDetails = screen.getByText('See details')
  //   expect(seeDetails).toBeInTheDocument()
  // })

  // test('shows error message when no task is selected', async () => {
  //   render(
  //     <ApolloProvider client={apolloClientInit}>
  //       <AllocateResourceForm {...mockProps} />
  //     </ApolloProvider>
  //   )
  //   const projectField = screen.getByTestId('project')
  //   projectField.focus()
  //   within(projectField).getByLabelText('Open').click()

  //   const options = await screen.findAllByRole('option')
  //   fireEvent.click(options[0]) // Select the first option

  //   const taskField = screen.getByTestId('task')
  //   fireEvent.blur(taskField)
  //   await expect(screen.findByText('Task is required')).toBeInTheDocument()
  // }, 10000)
})
