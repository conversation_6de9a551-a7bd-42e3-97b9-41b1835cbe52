// GraphQL imports
import { activeDatesVar, numOfWeeksVar } from '@gql/client/localState/index'

// UI library imports
import { FormControl, Select, MenuItem, palette } from 'lion-ui'

// Constants imports
import { CalendarViewOptions } from '@constants/calendarViewOptions'

// Utils imports
import getDateRange from '@utils/getDateRange'
import { useReactiveVar } from '@apollo/client'

const ViewSwitch = () => {
  const numOfWeeks = useReactiveVar(numOfWeeksVar)
  const updateCalendarView = (selectedView: CalendarViewOptions) => {
    const previousActiveDates = activeDatesVar()
    numOfWeeksVar(selectedView)
    localStorage.setItem('numOfWeeks', selectedView.toString())
    activeDatesVar(getDateRange(numOfWeeksVar(), previousActiveDates.startDate))
  }

  return (
    <FormControl fullWidth>
      <Select
        onChange={(e) => {
          updateCalendarView(e.target.value as CalendarViewOptions)
        }}
        aria-label="Select View"
        value={numOfWeeks}
        sx={{
          height: '40px',
          backgroundColor: palette.common.white
        }}
        data-testid="Calendar.Header.Navigation.ViewSwitch.Selector"
      >
        <MenuItem data-testid={`Calendar.Header.Navigation.ViewSwitch.Two-Weeks`} value={CalendarViewOptions.TWO_WEEKS}>
          2 Weeks
        </MenuItem>
        <MenuItem
          data-testid={`Calendar.Header.Navigation.ViewSwitch.Four-Weeks`}
          value={CalendarViewOptions.FOUR_WEEKS}
        >
          4 Weeks
        </MenuItem>
        {/* <MenuItem data-testid={`Calendar.Header.Navigation.ViewSwitch.One-Day`} value={CalendarViewOptions.ONE_DAY}>
          1 day
        </MenuItem> */}
      </Select>
    </FormControl>
  )
}

export default ViewSwitch
