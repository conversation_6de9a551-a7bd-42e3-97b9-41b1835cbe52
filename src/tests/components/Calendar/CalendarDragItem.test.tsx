import { fireEvent, render, screen, waitFor } from '@testing-library/react'
import CalendarDragItem from '../../../components/Calendar/CalendarDragItem/CalendarDragItem'
import { type Assignment } from '@typeDefs/Assignments'
import { mockAssignment } from '../../__mocks__/assignments.mock'
import { UserProfile } from '@constants/userProfile'
import { userProfileVar } from '@gql/client/localState'
import { ApolloClient, InMemoryCache } from '@apollo/client'
import { UPDATE_ASSIGNMENT_MUTATION } from '@gql/mutations/assignment.mutation'
import { MockedProvider } from '@apollo/client/testing'

let apolloClientInit: ApolloClient<unknown>
const cache = new InMemoryCache()

const mocks = [
  {
    request: {
      query: UPDATE_ASSIGNMENT_MUTATION,
      variables: {
        assignment: {
          externalId: '123',
          hoursPerDay: 8,
          startDate: '2024-10-31',
          dueDate: '2024-12-30',
          userId: undefined,
          userLoggedInExternalId: '10082922',
          isSplit: false
        }
      }
    },
    result: {
      data: {
        updateAssignment: true
      }
    }
  }
]

describe('CalendarDragItem Component', () => {
  const mockedAssignment: Assignment = mockAssignment[0]
  const defaultProps = {
    assignment: mockedAssignment,
    resourceType: 'resource',
    isLoading: false,
    isLocked: false,
    openModal: jest.fn(),
    currTileWidth: 2,
    currTileHeight: 20
  }

  // Helper render function
  const renderWithApollo = (props = defaultProps) =>
    render(
      <MockedProvider mocks={mocks} cache={cache} addTypename={false}>
        <CalendarDragItem {...props} />
      </MockedProvider>
    )

  beforeEach(() => {
    apolloClientInit = new ApolloClient({
      cache
    })
    apolloClientInit.writeQuery({
      query: UPDATE_ASSIGNMENT_MUTATION,
      variables: {
        assignment: {
          externalId: '123',
          hoursPerDay: 8,
          startDate: '2024-10-31',
          dueDate: '2024-12-30',
          userId: undefined,
          userLoggedInExternalId: '10082922',
          isSplit: false
        }
      },
      data: {
        updateAssignment: true
      }
    })
  })

  it('renders without crashing', () => {
    renderWithApollo()
    const calendarItem = screen.getByTestId('calendar-drag-item')
    expect(calendarItem).toBeInTheDocument()
  })

  it('renders with correct dimensions', () => {
    renderWithApollo()
    const calendarItem = screen.getByTestId('calendar-drag-item')
    expect(calendarItem).toHaveAttribute(`gs-w`, `${mockedAssignment.width}`)
    expect(calendarItem).toHaveAttribute(`gs-h`, `${mockedAssignment.height * 4}`)
  })

  it('renders with correct position', () => {
    renderWithApollo()
    const calendarItem = screen.getByTestId('calendar-drag-item')
    expect(calendarItem).toHaveAttribute(`gs-x`, `${mockedAssignment.x}`)
    expect(calendarItem).toHaveAttribute(`gs-y`, `${mockedAssignment.y}`)
  })

  xit('applies no-move and no-resize class when sticky is true', () => {
    renderWithApollo()
    const calendarItem = screen.getByTestId('calendar-drag-item')
    expect(calendarItem).toHaveAttribute(`gs-no-move`, 'true')
    expect(calendarItem).toHaveAttribute(`gs-no-resize`, 'true')
  })

  it('opens context menu on right click', async () => {
    userProfileVar(UserProfile.PROJECT_MANAGER)
    renderWithApollo()
    const calendarItem = screen.getByTestId('calendar-drag-item-content')
    fireEvent.contextMenu(calendarItem)

    expect(screen.getByText('Remove allocation')).toBeInTheDocument()
  })

  it('calls remove allocation popup when context menu item is clicked', async () => {
    userProfileVar(UserProfile.PROJECT_MANAGER)
    renderWithApollo()
    const calendarItem = screen.getByTestId('calendar-drag-item-content')
    fireEvent.contextMenu(calendarItem)
    fireEvent.click(screen.getByText('Remove allocation'))
    await waitFor(() => {
      expect(screen.getByText('Are you sure you want to remove this allocation?')).toBeInTheDocument()
    })
  })

  it('does not render context menu for light user', () => {
    userProfileVar(UserProfile.LIGHT_USER)
    renderWithApollo()
    const calendarItem = screen.getByTestId('calendar-drag-item')
    fireEvent.contextMenu(calendarItem)

    expect(screen.queryByText('Remove allocation')).not.toBeInTheDocument()
  })

  it('does not render loader when isLoading is false', () => {
    renderWithApollo()
    const loader = screen.getByTestId('drag-item-loader')
    expect(loader).toHaveStyle('display: none')
  })

  it('disables tile when assignmentState is not rejected and user is light user', () => {
    userProfileVar(UserProfile.LIGHT_USER)
    const mockedAssignment = { ...mockAssignment[0], taskState: 'approved' }
    renderWithApollo({ ...defaultProps, assignment: mockedAssignment })
    const calendarItem = screen.getByTestId('calendar-drag-item')
    expect(calendarItem).toHaveAttribute('gs-no-move', 'true')
    expect(calendarItem).toHaveAttribute('gs-no-resize', 'true')
  })

  it('disables tile when taskState is external', () => {
    const mockedAssignment = { ...mockAssignment[0], taskState: 'external' }
    renderWithApollo({ ...defaultProps, assignment: mockedAssignment })
    const calendarItem = screen.getByTestId('calendar-drag-item')
    expect(calendarItem).toHaveAttribute('gs-no-move', 'true')
    expect(calendarItem).toHaveAttribute('gs-no-resize', 'true')
  })

  it('disables tile when taskState is completed', () => {
    const mockedAssignment = { ...mockAssignment[0], taskState: 'completed' }
    renderWithApollo({ ...defaultProps, assignment: mockedAssignment })
    const calendarItem = screen.getByTestId('calendar-drag-item')
    expect(calendarItem).toHaveAttribute('gs-no-move', 'true')
    expect(calendarItem).toHaveAttribute('gs-no-resize', 'true')
  })

  it('disables tile when blockedByTimesheet is true', () => {
    const mockedAssignment = { ...mockAssignment[0], blockedByTimesheet: true }
    renderWithApollo({ ...defaultProps, assignment: mockedAssignment })
    const calendarItem = screen.getByTestId('calendar-drag-item')
    expect(calendarItem).toHaveAttribute('gs-no-move', 'true')
    expect(calendarItem).toHaveAttribute('gs-no-resize', 'true')
  })
})
