import { useState } from 'react'
import Image from 'next/image'

import ClearIcon from '@mui/icons-material/Clear'

import { Chip, Paper, Box, ClickAwayListener, palette, Button,Zoom } from 'lion-ui'

import { useReactiveVar } from '@apollo/client'
import { activeFiltersVar, selectedFiltersVar } from '@gql/client/localState'

import { getAllActiveFilters, removeFilter, removeAllFilters } from '@utils/filterSearch'
import { type LabelItem } from '@typeDefs/LabelItem'
import RMTooltip from '@components/Calendar/Tooltip/Tooltip'
import ToolTipGlobalFilter from '@components/Calendar/ToolTipGlobalFilter/ToolTipGlobalFilter'

const ChipList = () => {
  const maxShownFilters = 3
  const allFilters = useReactiveVar(activeFiltersVar)
  const activeFiltersList = getAllActiveFilters(allFilters)
  const filters = activeFiltersList

  const [open, setOpen] = useState(false)

  const handleTooltipClose = () => {
    setOpen(false)
  }

  const handleTooltipOpen = () => {
    setOpen(true)
  }

  const handleRemoveFilter = (filter: LabelItem) => {
    activeFiltersVar(removeFilter(allFilters, filter))
    const activeFilters = activeFiltersVar()
    const cleanData = Object.entries(activeFilters).reduce<Record<string, string[]>>((acc, [key, value]) => {
      const ids = value.map((item) => item.id)
      acc[key] = ids
      return acc
    }, {})
    selectedFiltersVar(cleanData)
  }
  const handleRemoveAllFilters = () => {
    activeFiltersVar(removeAllFilters())
    selectedFiltersVar({})
  }

  const shortLabel = (label: string) => {
    if (label?.length > 10) {
      return `${label.slice(0, 9)}...`
    } else {
      return label
    }
  }

  return (
    <Paper
      sx={{
        display: 'flex',
        justifyContent: 'left',
        alignItems: 'center',
        flexWrap: 'nowrap',
        listStyle: 'none',
        p: 0,
        pl: 1,
        m: 0,
        height: '37px',
        boxShadow: 'none'
      }}
      component="div"
    >
      {filters.slice(0, maxShownFilters).map((filter) => (
            <RMTooltip
             key={`${filter.id}-${filter.category}`} // Add key prop here
             title={<ToolTipGlobalFilter filter={filter} />} // Pass filter to tooltip content
             placement="bottom"
             arrow
             enterDelay={1200}
             enterNextDelay={700}
             leaveDelay={200}
             TransitionComponent={Zoom}
             slotProps={{
               popper: {
                 modifiers: [
                   {
                     name: 'offset',
                     options: {
                       offset: [0,-1]
                     },
                   },
                 ],
               },
             }}
           >

        <Chip
          key={`${filter.id}-${filter.category}`}
          sx={{
            zIndex: '11',
            marginRight: 1,
            height: '1.5rem'
          }}
          label={shortLabel(filter.name)}
          icon={<Image src={filter.icon?.src ?? ''} width="18" height="18" alt="icon" />}
          deleteIcon={
            <ClearIcon sx={{ color: `${palette.grey['900']} !important`, fontSize: '0.875rem !important' }}></ClearIcon>
      }
          onDelete={() => {
            handleRemoveFilter(filter)
          }}
        />
      </RMTooltip>
      ))}

      <Box sx={{ display: 'flex', alignItems: 'center', nowrap: 'nowrap' }}>
        {filters.length > maxShownFilters && (
          <ClickAwayListener onClickAway={handleTooltipClose}>
            <Box sx={{ position: 'relative' }}>
              {open && (
                <Box
                  sx={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    boxShadow: '0px 4px 30px 0px rgba(0, 0, 0, 0.2)',
                    padding: '12px',
                    borderRadius: '8px',
                    top: '42px',
                    left: '0px',
                    background: 'white',
                    position: 'absolute',
                    zIndex: '9001',
                    width: 'max-content',
                    maxWidth: '446px',
                    flexDirection: 'column',
                    gap: '0.5rem'
                  }}
                >
                  {filters.slice(maxShownFilters).map((filter) => {
                    return (
                      <div key={filter.id}>
                        <Chip
                          sx={{ zIndex: '11', marginRight: 1, marginLeft: 1, height: '1.5rem' }}
                          label={filter.name}
                          icon={<Image src={filter.icon?.src ?? ''} width="18" height="18" alt="icon" />}
                          deleteIcon={<ClearIcon sx={{ color: `${palette.grey['900']} !important` }}></ClearIcon>}
                          onDelete={() => {
                            handleRemoveFilter(filter)
                          }}
                        />
                      </div>
                    )
                  })}
                </Box>
              )}

              <Button
                sx={{
                  padding: '0.125rem 0.53125rem',
                  minWidth: 0,
                  width: '2rem',
                  height: '1.75rem',
                  backgroundColor: palette['50-Accent-1'].main,
                  // eslint-disable-next-line @typescript-eslint/naming-convention
                  ':hover': {
                    backgroundColor: palette['50-Accent-1'].dark
                  }
                }}
                variant="text"
                color="info"
                onClick={handleTooltipOpen}
                aria-label="See other filters"
              >{`+${filters.length - maxShownFilters}`}</Button>
            </Box>
          </ClickAwayListener>
        )}

        {filters.length > 1 && (
          <Button
            variant="outlined"
            aria-label="remove all filters"
            sx={{ padding: 0, minWidth: 0, width: '1.75rem', height: '1.75rem', zIndex: '11', marginLeft: 2 }}
            onClick={() => {
              handleRemoveAllFilters()
            }}
          >
            <ClearIcon sx={{ color: `${palette.grey['900']} !important`, fontSize: '0.875rem' }} />
          </Button>
        )}
      </Box>
    </Paper>
  )
}

export default ChipList
