import { type Assignment } from '@typeDefs/Assignments'

export const mockAssignment: Assignment[] = [
  {
    id: 'assignment-1',
    startDate: '2024-07-27',
    dueDate: '2024-07-27',
    hoursPerDay: 2,
    state: 'active',
    userId: 'resource-1',
    projectId: 'project-1',
    taskId: 'task-1',
    activeNonWorkingDays: [],
    width: 1,
    x: 12,
    y: 0,
    height: 2,
    totalDays: 10,
    taskName: 'Test Task 1',
    projectName: 'Test Project 1',
    externalLink: 'https://www.google.com',
    altairJobNumber: '123456',
    isPlaceholder: false,
    assignmentState: 'active',
    projectManagerName: '<PERSON>',
    taskState: 'Active',
    assignmentNotes: 'Assignment notes content',
    taskNotes: 'Task notes content',
    taskStartDate: '2024-09-23T08:00:00+00:00',
    taskDueDate: '2024-11-15T16:00:00+00:00',
    projectAgencyCode: 'agency-1',
    projectBrandCode: 'brand-1',
    projectIntegrationId: 'project-integration-id-1',
    userIntegrationId: 'user-integration-id-1',
    agencyCode: 'agency-1',
    agencyName: 'Agency 1',
    costCenterCode: 'cost-center-1',
    costCenterName: 'Cost Center 1',
    locationName: 'Location 1',
    blockedByTimesheet: false,
    assignmentIcaStatus: 'Needs Approval'
  },
  {
    id: 'assignment-2',
    startDate: '2024-07-16',
    dueDate: '2024-07-18',
    hoursPerDay: 8,
    state: 'active',
    userId: 'resource-1',
    projectId: 'project-2',
    taskId: 'task-2',
    activeNonWorkingDays: [],
    width: 3,
    x: 1,
    y: 0,
    height: 8,
    totalDays: 10,
    taskName: 'Test Task 2',
    projectName: 'Test Project 2',
    externalLink: 'https://www.google.com',
    altairJobNumber: '1234566',
    isPlaceholder: false,
    assignmentState: 'active',
    projectManagerName: 'John Doe',
    taskState: 'Active',
    assignmentNotes: 'Assignment notes content',
    taskNotes: 'Task notes content',
    taskStartDate: '2024-09-23T08:00:00+00:00',
    taskDueDate: '2024-11-15T16:00:00+00:00',
    projectAgencyCode: 'agency-2',
    projectBrandCode: 'brand-2',
    projectIntegrationId: 'project-integration-id-1',
    userIntegrationId: 'user-integration-id-1',
    agencyCode: 'agency-1',
    agencyName: 'Agency 1',
    costCenterCode: 'cost-center-1',
    costCenterName: 'Cost Center 1',
    locationName: 'Location 1',
    blockedByTimesheet: false,
    assignmentIcaStatus: 'Needs Approval'
  }
]
