// React imports
import { useCallback } from 'react'

// Lion UI imports
import { Box, Button, Chip, Typography } from 'lion-ui'
import CloseIcon from '@mui/icons-material/Close'

// Components imports
import getCalendarItemStyle from '@components/Calendar/CalendarDragItem/CalendarDragItem.style'

// Types imports
import { type FormHeaderProps } from './FormHeader.props'
import { type TileStatus } from '@typeDefs/Tile'

const FormHeader = ({
  title,
  projectName,
  taskName,
  state,
  stateStyle,
  closeIconLabel = 'Close Icon Modal',
  onClose
}: FormHeaderProps) => {
  const { backgroundColor, color } = getCalendarItemStyle(stateStyle.toLowerCase() as TileStatus)

  const handleCloseButton = useCallback(() => {
    onClose()
  }, [onClose])

  return (
    <Box sx={{ padding: '1.45rem' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography variant="h2">{title}</Typography>
        <Button onClick={handleCloseButton} variant="text" aria-label={closeIconLabel} sx={{ minWidth: '0px' }}>
          <CloseIcon />
        </Button>
      </Box>
      <Box sx={{ fontSize: '0.85rem', display: 'flex', alignItems: 'center', maxWidth: '42.5rem' }}>
        <Box
          sx={{
            maxWidth: '28.5rem',
            fontWeight: 600,
            textOverflow: 'ellipsis',
            overflow: 'hidden',
            whiteSpace: 'nowrap'
          }}
        >
          <span>{projectName}</span>・ <span>{taskName}</span>
        </Box>
        &nbsp;&nbsp;
        <Chip
          sx={{
            backgroundColor,
            color,
            borderRadius: 1,
            padding: 0,
            height: '1.6rem'
          }}
          label={<Typography variant="OverlineS">{state}</Typography>}
        />
      </Box>
    </Box>
  )
}

export default FormHeader
