// React and UI components
import { memo } from 'react'
import { Box, palette, Typography } from 'lion-ui'

// Type imports
import { type CalendarUtilizationCellProps } from './CalendarUtilizationCell.props'

const CalendarUtilizationCell = ({
  hours,
  numOfDaysInView,
  nonWorkingDay,
  minimumAgencyHoursPerDay
}: CalendarUtilizationCellProps) => {
  const capacityPerDay = nonWorkingDay ? 0 : minimumAgencyHoursPerDay
  const overCapacity = hours >= capacityPerDay
  const overCapacityTime = capacityPerDay - hours
  const parsedOverCapacity = parseFloat(overCapacityTime.toFixed(2))
  const parsedHours = parseFloat((capacityPerDay - hours).toFixed(2))

  const borderColor = palette.secondary.main
  const errorColor = palette['Error-Main'].main
  const successColor = palette['Success-Main'].main
  const successSurfaceColor = palette['Success-Surface'].main
  const accentColor = palette['500-Accent-3'].main

  const containerStyles = {
    borderWidth: '1px 0 0 1px',
    borderStyle: 'solid',
    borderColor,
    width: `calc(100% / ${numOfDaysInView})`,
    height: '100%',
    display: 'flex',
    alignItems: 'end',
    backgroundColor: nonWorkingDay ? palette.grey['50'] : palette.common.white
  }

  const textStyles = {
    textAlign: 'end',
    paddingRight: 1,
    color: overCapacity || nonWorkingDay ? errorColor : successColor
  }

  const barContainerStyles = {
    position: 'relative',
    bottom: 0,
    width: '100%',
    height: '4px',
    background: successSurfaceColor
  }

  const barStyles = {
    width: `${Math.min((hours * 100) / capacityPerDay, 100)}%`,
    height: '100%',
    background: overCapacity || nonWorkingDay ? accentColor : successColor
  }

  return (
    <Box data-testid="calendar-cell-container" sx={containerStyles}>
      {hours > 0 && (
        <Box sx={{ width: '100%' }}>
          <Box sx={textStyles} data-testid="calendar-cell-text">
            <Typography variant="BodyXSRegular">
              {overCapacity ? `${String(parsedOverCapacity)}h` : `${String(parsedHours).slice(0, 5)}h`}
            </Typography>
          </Box>
          <Box sx={barContainerStyles}>
            <Box data-testid="calendar-cell-bar" sx={barStyles} />
          </Box>
        </Box>
      )}
    </Box>
  )
}

export default memo(CalendarUtilizationCell)
