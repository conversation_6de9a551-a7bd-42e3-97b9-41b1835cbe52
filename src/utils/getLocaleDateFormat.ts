import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'

dayjs.extend(utc)
dayjs.extend(timezone)

const getLocaleDateFormat = (dateString: string): string => {
  const baseDate = dayjs.tz(dateString)

  const userLocale = navigator.language.toLowerCase()

  if (userLocale.startsWith('en')) {
    return baseDate.format('MM/DD/YYYY')
  } else {
    return baseDate.format('DD/MM/YYYY')
  }
}

export default getLocaleDateFormat
