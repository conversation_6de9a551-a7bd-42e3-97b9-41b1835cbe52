import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { MockedProvider, type MockedResponse } from '@apollo/client/testing'
import Header from '@components/Header/Header/Header'
import { userProfileVar } from '@gql/client/localState'
import { UserProfile } from '@constants/userProfile'
import { PROJECTS_BY_RESOURCE_MANAGER_QUERY, PROJECTS_BY_PROJECT_MANAGER_QUERY } from '@gql/queries/projects.query'
import { RESOURCES_QUERY } from '@gql/queries/resources.query'
import USERS_GROUPS_QUERY from '@gql/queries/usersGroups.query'
import { GET_RESOURCE_AND_PLACEHOLDER } from '@gql/queries/searchResourcePlaceholder'
import { PLACEHOLDERS_QUERY } from '@gql/queries/placeholders.query'
import BRANDS_QUERY from '@gql/queries/brands.query'
import { TASKS_BY_RM_GROUPED_BY_PROJECT_QUERY } from '@gql/queries/tasks.query'

// Mock cross-fetch to prevent actual network calls from HttpLink
// eslint-disable-next-line @typescript-eslint/naming-convention
jest.mock('cross-fetch', () => ({
  // eslint-disable-next-line @typescript-eslint/naming-convention
  __esModule: true,
  default: jest.fn(
    async () =>
      await Promise.resolve({
        ok: true,
        status: 200,
        json: async () => await Promise.resolve({ data: {} }),
        text: async () => await Promise.resolve(JSON.stringify({ data: {} })),
        headers: new Map()
      })
  )
}))

jest.mock('@apollo/client', () => ({
  ...jest.requireActual('@apollo/client'),
  useReactiveVar: jest.fn((reactiveVar) => jest.fn())
}))

let currentShowAuditTrailState = false
const mockShowAuditTrailReactiveVarState = jest.fn((newState?: boolean) => {
  if (typeof newState !== 'undefined') {
    currentShowAuditTrailState = newState
  }
  return currentShowAuditTrailState
})

let currentUserInfoState = {
  altairNumber: 'test-id-123',
  businessRole: [UserProfile.RESOURCE_MANAGER],
  firstName: 'Test',
  lastName: 'User',
  email: '<EMAIL>',
  id: 'test-uuid',
  userGroups: [],
  permissions: []
}

// Define a type for UserInfo based on its structure
type UserInfo = typeof currentUserInfoState
const mockUserInfoReactiveVarState = jest.fn((newState?: UserInfo) => {
  if (typeof newState !== 'undefined') {
    currentUserInfoState = newState
  }
  return currentUserInfoState
})

let currentBrandIdsState: string[] = []
const mockBrandIdsReactiveVarState = jest.fn((newState?: string[]) => {
  if (typeof newState !== 'undefined') {
    currentBrandIdsState = newState
  }
  return currentBrandIdsState
})

let currentActiveDatesState: { startDate: string; endDate: string } | null = {
  startDate: '2024-07-01',
  endDate: '2024-07-14'
}
const mockActiveDatesReactiveVarState = jest.fn((newState?: { startDate: string; endDate: string } | null) => {
  if (typeof newState !== 'undefined') {
    currentActiveDatesState = newState
  }
  return currentActiveDatesState
})

jest.mock('@gql/client/localState', () => ({
  numOfWeeksVar: jest.fn(() => 2),
  activeSortVar: jest.fn(() => ({ field: 'name', order: 'asc' })),
  selectedFiltersVar: jest.fn(() => ({})),
  placeholderSettingsSaveVar: jest.fn(() => false),
  renderKeyVar: jest.fn(() => 0),
  placeholderSettingsPayloadVar: jest.fn(() => ({
    businessUnit: [],
    location: [],
    costCenter: [],
    jobRole: []
  })),
  activeFiltersVar: jest.fn(() => ({
    projects: [],
    resources: [],
    brands: [],
    usersGroups: [],
    tasks: [],
    taskStates: [],
    placeholders: []
  })),
  showAuditTrailVar: jest.fn((...args: [boolean?]) => {
    if (args.length > 0) {
      return mockShowAuditTrailReactiveVarState(args[0])
    }
    return mockShowAuditTrailReactiveVarState()
  }),
  placeholderAssignmentVar: jest.fn(),

  userProfileVar: (() => {
    let currentProfile: UserProfile = 'RESOURCE_MANAGER' as UserProfile
    return jest.fn((newValue?: UserProfile) => {
      if (typeof newValue !== 'undefined') {
        currentProfile = newValue
        return
      }
      return currentProfile
    })
  })(),

  userInfoVar: jest.fn((...args: [UserInfo?]) => {
    if (args.length > 0) {
      return mockUserInfoReactiveVarState(args[0])
    }
    return mockUserInfoReactiveVarState()
  }),
  brandIdsVar: jest.fn((...args: [string[]?]) => {
    if (args.length > 0) {
      return mockBrandIdsReactiveVarState(args[0])
    }
    return mockBrandIdsReactiveVarState()
  }),

  activeDatesVar: jest.fn((...args: [({ startDate: string; endDate: string } | null)?]) => {
    if (args.length > 0) {
      return mockActiveDatesReactiveVarState(args[0])
    }
    return mockActiveDatesReactiveVarState()
  })
}))

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const allMocks: ReadonlyArray<MockedResponse<any>> = [
  // Using <any> as MockedResponse is generic and data/variable shapes vary widely.
  {
    request: {
      query: PROJECTS_BY_PROJECT_MANAGER_QUERY,
      variables: { workCode: 'test-id-123' }
    },
    result: {
      data: {
        getProjectsByProjectManagerWorkCode: []
      }
    }
  },
  {
    request: {
      query: GET_RESOURCE_AND_PLACEHOLDER,
      variables: {
        params: { userId: 'test-id-123', searchName: '' }
      }
    },
    result: {
      data: {
        getResourceAndPlaceholder: []
      }
    }
  },
  {
    request: {
      query: USERS_GROUPS_QUERY,
      variables: {
        params: { resourceManagerId: 'test-id-123', pageNumber: 1, pageSize: 10 }
      }
    },
    result: {
      data: {
        usersGroups: [
          {
            id: 'group-1-id',
            name: 'Test Group 1',
            userGroupId: 'test-group-1',
            createdOn: '2023-01-01T00:00:00Z',
            lastModifiedOn: '2023-01-01T00:00:00Z',
            // eslint-disable-next-line @typescript-eslint/naming-convention
            __typename: 'UsersGroup'
          }
        ]
      }
    }
  },
  {
    request: {
      query: PROJECTS_BY_RESOURCE_MANAGER_QUERY,
      variables: { workCode: 'test-id-123' }
    },
    result: {
      data: { getProjectsByResourceManagerId: [] }
    }
  },
  {
    request: {
      query: RESOURCES_QUERY,
      variables: {
        params: {
          userId: 'test-id-123',
          pageNumber: 1,
          pageSize: 50,
          sort: { field: 'name', order: 'asc' },
          searchName: null,
          startDate: '',
          endDate: ''
        }
      }
    },
    result: {
      data: {
        resources: {
          items: [],
          totalCount: 0,
          totalPages: 0,
          pageNumber: 1,
          hasNextPage: false,
          hasPreviousPage: false,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          __typename: 'ResourceResponse'
        }
      }
    }
  },
  {
    request: {
      query: PLACEHOLDERS_QUERY,
      variables: {
        params: {
          userId: 'test-id-123',
          pageNumber: 1,
          pageSize: 1000,
          sort: { field: 'name', order: 'asc' }
        }
      }
    },
    result: {
      data: {
        placeholders: {
          items: [],
          totalCount: 0,
          totalPages: 0,
          pageNumber: 1,
          hasNextPage: false,
          hasPreviousPage: false,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          __typename: 'PlaceholderResponse'
        }
      }
    }
  },
  {
    request: {
      query: BRANDS_QUERY,
      variables: { params: [] }
    },
    result: {
      data: { getBrands: [] }
    }
  },
  {
    request: {
      query: TASKS_BY_RM_GROUPED_BY_PROJECT_QUERY,
      variables: { workCode: 'test-id-123', projectIds: [] }
    },
    result: {
      data: { getRMTasksByGroupOfProjects: [] }
    }
  }
]

jest.mock('@utils/filterSearch', () => ({
  getAllActiveFilters: jest.fn(() => [])
}))

xdescribe('Header Component', () => {
  beforeEach(() => {
    userProfileVar(UserProfile.RESOURCE_MANAGER)
    currentShowAuditTrailState = false
    currentUserInfoState = {
      altairNumber: 'test-id-123',
      businessRole: [UserProfile.RESOURCE_MANAGER],
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      id: 'test-uuid',
      userGroups: [],
      permissions: []
    }
    currentBrandIdsState = ['1', '2']
    currentActiveDatesState = { startDate: '2024-07-01', endDate: '2024-07-14' }
  })

  it('renders the "Allocate resource" button', () => {
    render(
      <MockedProvider mocks={allMocks} addTypename={false}>
        <Header />
      </MockedProvider>
    )
    const allocateButton = screen.getByTestId('allocate-resource-button')
    expect(allocateButton).toBeInTheDocument()
  })

  it('shows the Allocate Resource modal when button is clicked', async () => {
    render(
      <MockedProvider mocks={allMocks} addTypename={false}>
        <Header />
      </MockedProvider>
    )
    const allocateButton = await screen.findByTestId('allocate-resource-button')
    fireEvent.click(allocateButton)

    const modal = await screen.findByRole('dialog')
    expect(modal).toBeInTheDocument()
  })

  it.each([
    [UserProfile.RESOURCE_MANAGER, true],
    [UserProfile.PROJECT_MANAGER, true],
    [UserProfile.LIGHT_USER, false]
  ])('renders the "Allocate resource" button based on user profile %s', async (profile, shouldShowButton) => {
    userProfileVar(profile as UserProfile)
    render(
      <MockedProvider mocks={allMocks} addTypename={false}>
        <Header />
      </MockedProvider>
    )
    if (shouldShowButton) {
      await waitFor(() => {
        expect(screen.getByTestId('allocate-resource-button')).toBeInTheDocument()
      })
    } else {
      await waitFor(() => {
        expect(screen.queryByTestId('allocate-resource-button')).not.toBeInTheDocument()
      })
    }
  })

  it('renders the ViewSwitch component', () => {
    render(
      <MockedProvider mocks={allMocks} addTypename={false}>
        <Header />
      </MockedProvider>
    )
    const viewSwitch = screen.getByTestId('Calendar.Header.Navigation.ViewSwitch.Selector')
    expect(viewSwitch).toBeInTheDocument()
    expect(viewSwitch).toHaveTextContent('2 Weeks')
  })
})
