// React imports
import { memo, useState } from 'react'

// UI library imports
import { Box, Button, Grid, palette } from 'lion-ui'

// Icon imports
import AddIcon from '@mui/icons-material/Add'

// Component imports
import DateNavigation from '@components/Header/DateNavigation/DateNavigation'
import DatePicker from '@components/Header/DatePicker/DatePicker'
import FilterSearch from '@components/Header/FilterSearch/FilterSearch'
import AllocateResourceForm from '@components/Forms/AllocateResourceForm/AllocateResourceForm'

// Constants imports
import { UserProfile } from '@constants/userProfile'

// GraphQL imports
import { userProfileVar } from '@gql/client/localState/index'
import { useReactiveVar } from '@apollo/client'
import ViewSwitch from '@components/Header/ViewSwitch/ViewSwitch'
import ProfileSwitch from '../ProfileSwitch/ProfileSwitch'

const Header = () => {
  const [showNewAllocationForm, setShowNewAllocationForm] = useState(false)

  const userProfile = useReactiveVar(userProfileVar)

  return (
    <Grid
      container
      sx={{
        justifyContent: 'space-between',
        padding: 3,
        backgroundColor: palette.grey['50'],
        alignItems: 'center'
      }}
    >
      <Grid sx={{ width: '15%' }}>
        {useReactiveVar(userProfileVar) !== UserProfile.LIGHT_USER && (
          <Button
            sx={{
              width: '95%',
              height: '40px'
            }}
            startIcon={<AddIcon />}
            onClick={() => {
              setShowNewAllocationForm(true)
            }}
            variant="contained"
            data-testid="allocate-resource-button"
          >
            Allocate resource
          </Button>
        )}
      </Grid>
      {showNewAllocationForm && (
        <AllocateResourceForm open={showNewAllocationForm} onClose={setShowNewAllocationForm} />
      )}

      <Grid sx={{ width: '53%' }}>
        {useReactiveVar(userProfileVar) !== UserProfile.LIGHT_USER && (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              p: 1
            }}
          >
            <FilterSearch />
          </Box>
        )}
      </Grid>

      <Grid sx={{ width: '32%', display: 'flex', justifyContent: 'center', gap: 1 }}>
        {/* Hidden as the range is not in scope at the moment */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center'
          }}
        >
          <ViewSwitch />
        </Box>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center'
          }}
        >
          <DatePicker />
        </Box>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            minWidth: '166px'
          }}
        >
          <DateNavigation />
        </Box>
        <ProfileSwitch />
        {userProfile !== UserProfile.LIGHT_USER && (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center'
            }}
          ></Box>
        )}
      </Grid>
    </Grid>
  )
}

export default memo(Header)
