import { render, screen } from '@testing-library/react'
import { ScrollProvider, useScroll } from '../../context/scrollContext'

// Helper component to test context values
const TestComponent = () => {
  const { isScrolling, setIsScrolling } = useScroll()
  return (
    <div>
      <span data-testid="isScrolling" style={{ display: 'block', height: '500px' }}>
        {isScrolling.toString()}
      </span>
      <button
        onClick={() => {
          window.scrollBy({ top: 350 })
          setIsScrolling(true)
        }}
      >
        Set Scrolling
      </button>
    </div>
  )
}

describe('ScrollContext', () => {
  test('provides default values', () => {
    render(
      <ScrollProvider>
        <TestComponent />
      </ScrollProvider>
    )
    expect(screen.getByTestId('isScrolling')).toHaveTextContent('false')
  })

  test('updates context values', () => {
    render(
      <ScrollProvider>
        <TestComponent />
      </ScrollProvider>
    )

    setTimeout(() => {
      expect(screen.getByTestId('isScrolling')).toHaveTextContent('true')
    }, 0)
  })
})
