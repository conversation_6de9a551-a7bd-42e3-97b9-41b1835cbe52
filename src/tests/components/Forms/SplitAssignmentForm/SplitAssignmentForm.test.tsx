import '@testing-library/jest-dom/extend-expect'
import { Mo<PERSON>Provider } from '@apollo/client/testing'
import SplitAssignmentForm from '@components/Forms/SplitAssignmentForm/SplitAssignmentForm'
import { showSplitAssignmentFormVar } from '@gql/client/localState'
import dayjs from 'dayjs'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { type Assignment } from '@typeDefs/Assignments'

const mockAssignment: Assignment = {
  id: '1',
  startDate: dayjs().format('YYYY-MM-DD'),
  dueDate: dayjs().add(3, 'days').format('YYYY-MM-DD'),
  hoursPerDay: 8,
  state: 'Active',
  userId: 'user123',
  projectId: 'project123',
  taskId: 'task123',
  activeNonWorkingDays: ['2023-01-05', '2023-01-06'],
  x: 10,
  y: 20,
  width: 100,
  height: 50,
  totalDays: 4,
  taskName: 'Task Name',
  projectName: 'Project Name',
  externalLink: 'http://example.com',
  altairJobNumber: 'ALT123',
  isPlaceholder: false,
  assignmentState: 'Approved',
  projectManagerName: 'John Doe',
  taskState: 'Active',
  taskNotes: 'Task notes',
  assignmentNotes: 'Assignment notes',
  taskStartDate: '2024-09-23T08:00:00+00:00',
  taskDueDate: '2024-11-15T16:00:00+00:00',
  projectAgencyCode: 'agency-2',
  projectBrandCode: 'brand-2',
  assignmentIcaStatus: 'Needs Approval',
  projectAgencyName: 'Agency 2',
  projectIntegrationId: 'project-integration-id-1',
  userIntegrationId: 'user-integration-id-1',
  agencyCode: 'agency-1',
  agencyName: 'Agency 1',
  costCenterCode: 'cost-center-1',
  costCenterName: 'Cost Center 1',
  locationName: 'Location 1',
  blockedByTimesheet: false
}

describe('Split Assignment Component', () => {
  beforeEach(() => {
    showSplitAssignmentFormVar(mockAssignment)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  test('renders nothing when assignment is undefined', () => {
    showSplitAssignmentFormVar(undefined)
    const { container } = render(
      <MockedProvider addTypename={false}>
        <SplitAssignmentForm />
      </MockedProvider>
    )
    expect(container).toBeEmptyDOMElement()
  })

  test('renders nothing when assignment is undefined', () => {
    showSplitAssignmentFormVar(undefined)
    const { container } = render(
      <MockedProvider addTypename={false}>
        <SplitAssignmentForm />
      </MockedProvider>
    )
    expect(container).toBeEmptyDOMElement()
  })

  test('renders SplitAssignmentForm when assignment is provided', () => {
    render(
      <MockedProvider addTypename={false}>
        <SplitAssignmentForm />
      </MockedProvider>
    )
    expect(screen.getByText('Split Task')).toBeInTheDocument()
    expect(screen.getByText(`${mockAssignment.projectName}`)).toBeInTheDocument()
    expect(screen.getByText(`${mockAssignment.taskName}`)).toBeInTheDocument()
    expect(screen.getByText(`${mockAssignment.assignmentState}`)).toBeInTheDocument()
  })

  test('handles clear button', () => {
    render(
      <MockedProvider addTypename={false}>
        <SplitAssignmentForm />
      </MockedProvider>
    )
    const dateElement = screen.getByText(`${dayjs().format('ddd')} ${dayjs().format('DD')}`)
    fireEvent.click(dateElement)
    const clearButton = screen.getByText('Clear')
    fireEvent.click(clearButton)
    expect(dateElement).not.toHaveStyle('border-right: 1px solid')
  })

  test('renders date elements', () => {
    render(
      <MockedProvider addTypename={false}>
        <SplitAssignmentForm />
      </MockedProvider>
    )
    const dateElement = screen.getByText(`${dayjs().format('ddd')} ${dayjs().format('DD')}`)
    expect(dateElement).toBeInTheDocument()
  })

  test('dates range is populated correctly based on assignment start and due dates', () => {
    render(
      <MockedProvider addTypename={false}>
        <SplitAssignmentForm />
      </MockedProvider>
    )
    const dateCells = screen.getAllByText(/^\w{3} \d{2}$/)
    expect(dateCells).toHaveLength(mockAssignment.totalDays)
    expect(dateCells[0]).toHaveTextContent(`${dayjs().format('ddd')} ${dayjs().format('DD')}`)
    expect(dateCells[dateCells.length - 1]).toHaveTextContent(
      `${dayjs()
        .add(dateCells.length - 1, 'day')
        .format('ddd')} ${dayjs()
        .add(dateCells.length - 1, 'day')
        .format('DD')}`
    )
  })

  test('handles date hover state', async () => {
    render(
      <MockedProvider addTypename={false}>
        <SplitAssignmentForm />
      </MockedProvider>
    )
    const dateElement = screen.getByText(`${dayjs().format('ddd')} ${dayjs().format('DD')}`)
    fireEvent.mouseOver(dateElement)
    await waitFor(() => {
      expect(dateElement).not.toHaveStyle('background-color: #E6E6E6;')
    })
  })

  test('handles date selection', () => {
    render(
      <MockedProvider addTypename={false}>
        <SplitAssignmentForm />
      </MockedProvider>
    )
    const dateElement = screen.getByText(`${dayjs().format('ddd')} ${dayjs().format('DD')}`)
    fireEvent.click(dateElement)
    expect(dateElement).toHaveStyle('border-right: 1px solid #1077D6;')
  })

  test('handles confirm button', async () => {
    render(
      <MockedProvider addTypename={false}>
        <SplitAssignmentForm />
      </MockedProvider>
    )
    const dateElement = screen.getByText(`${dayjs().format('ddd')} ${dayjs().format('DD')}`)
    fireEvent.click(dateElement)
    const confirmButton = screen.getByText('Confirm Split')
    fireEvent.click(confirmButton)
    await waitFor(() => {
      expect(showSplitAssignmentFormVar()).not.toBeUndefined()
    })
  })

  test('handles close button', () => {
    render(
      <MockedProvider addTypename={false}>
        <SplitAssignmentForm />
      </MockedProvider>
    )

    const closeButton = screen.getByLabelText('Close Icon Modal')
    fireEvent.click(closeButton)
    expect(showSplitAssignmentFormVar()).toBeUndefined()
  })

  test('does not display loading overlay when isLoading is false', () => {
    render(
      <MockedProvider addTypename={false}>
        <SplitAssignmentForm />
      </MockedProvider>
    )

    const overlay = screen.getByTestId('split-assignment-overlay')
    expect(overlay).toHaveStyle('display: none')
  })

  it('should not display CircularProgress when isLoading is false', () => {
    render(
      <MockedProvider addTypename={false}>
        <SplitAssignmentForm />
      </MockedProvider>
    )

    // Ensure CircularProgress is not in the document
    const circularProgress = screen.queryByRole('progressbar')
    expect(circularProgress).not.toBeInTheDocument()
  })

  it('should display CircularProgress when isLoading is true', async () => {
    render(
      <MockedProvider addTypename={false}>
        <SplitAssignmentForm />
      </MockedProvider>
    )
    /* eslint-disable */
    const confirmButton = screen.getByRole('button', { name: /Confirm Split/i })
    const dateElement = screen.getByText(`${dayjs().format('ddd')} ${dayjs().format('DD')}`)
    fireEvent.click(dateElement)
    userEvent.click(confirmButton)

    let circularProgress
    setTimeout(() => {
      circularProgress = screen.getByTestId('split-assignment-loading')
      expect(circularProgress).toBeInTheDocument()
    }, 1000)
    /* eslint-enable */
  })
})
