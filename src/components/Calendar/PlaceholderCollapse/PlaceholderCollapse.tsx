/* eslint-disable @typescript-eslint/naming-convention */
import styled from '@emotion/styled'
import { Accordion, AccordionDetails, AccordionSummary, Box, palette, Typography } from 'lion-ui'
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown'
import { isExpandedVar } from '@gql/client/localState'
import type React from 'react'
import { memo } from 'react'
import { useReactiveVar } from '@apollo/client'

const StyledAccordion = styled(Accordion)(() => ({
  '&:before': {
    display: 'none'
  },
  '.Mui-expanded': {
    minHeight: '35px !important'
  },
  '& .MuiAccordionSummary-content': {
    margin: '0 !important'
  }
}))

interface PlaceholderCollapseProps {
  children: React.ReactNode
  length: number | undefined
}

const PlaceholderCollapse = ({ children, length }: PlaceholderCollapseProps) => {
  const isExpanded = useReactiveVar(isExpandedVar)
  const handleExpansion = () => {
    isExpandedVar(!isExpanded)
  }

  return (
    <Box sx={{ display: 'flex', width: '100%' }}>
      <StyledAccordion
        sx={{
          display: 'flex',
          flexDirection: 'column-reverse',
          justifyContent: 'center',
          width: '100%'
        }}
        expanded={isExpanded}
        onChange={handleExpansion}
        id="placeholder-accordion"
      >
        <AccordionSummary
          aria-controls="panel1a-content"
          id="placeholder-header"
          sx={{ display: 'flex', alignItems: 'flex-start', backgroundColor: palette.grey['200'], minHeight: '35px' }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%', height: '25px' }}>
            <Box
              sx={{
                width: '30vw',
                display: 'flex',
                justifyContent: 'center',
                background: palette.grey['100'],
                borderRadius: '0 0 30px 30px'
              }}
            >
              <KeyboardArrowDownIcon sx={{ marginRight: 1, transform: `rotate(${isExpanded ? '180' : '0'}deg)` }} />
              <Typography fontWeight={'bold'}>
                <span>
                  {isExpanded ? 'Hide' : 'Show'} placeholders ({length ?? 0})
                </span>
              </Typography>
            </Box>
          </Box>
        </AccordionSummary>
        <AccordionDetails id="placeholder-area" sx={{ padding: '0' }}>
          {children}
        </AccordionDetails>
      </StyledAccordion>
    </Box>
  )
}

export default memo(PlaceholderCollapse)
