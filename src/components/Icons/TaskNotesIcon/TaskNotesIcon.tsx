import { type IconProps } from '../Icon.props'

const TaskNotesIcon = ({ height, width, isActive }: IconProps) => {
  return (
    <svg width={width} height={height} viewBox="0 0 20 22" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M3.1579 21.1498C2.46078 21.1498 1.87689 20.9144 1.40622 20.4437C0.935537 19.9731 0.700195 19.3892 0.700195 18.6921V5.05751C0.700195 4.36039 0.935537 3.7765 1.40622 3.30583C1.87689 2.83515 2.46078 2.5998 3.1579 2.5998H7.00789C7.23738 2.02802 7.62135 1.54341 8.1598 1.14598C8.69826 0.748531 9.31332 0.549805 10.005 0.549805C10.6966 0.549805 11.3117 0.748531 11.8501 1.14598C12.3886 1.54341 12.7694 2.02802 12.9924 2.5998H16.8424C17.5396 2.5998 18.1235 2.83515 18.5941 3.30583C19.0648 3.7765 19.3001 4.36039 19.3001 5.05751V18.6921C19.3001 19.3892 19.0648 19.9731 18.5941 20.4437C18.1235 20.9144 17.5396 21.1498 16.8424 21.1498H3.1579ZM3.1579 18.9998H16.8424C16.9194 18.9998 16.9899 18.9677 17.054 18.9036C17.1181 18.8395 17.1502 18.769 17.1502 18.6921V5.05751C17.1502 4.98057 17.1181 4.91005 17.054 4.84593C16.9899 4.78183 16.9194 4.74978 16.8424 4.74978H3.1579C3.08096 4.74978 3.01044 4.78183 2.94632 4.84593C2.88222 4.91005 2.85017 4.98057 2.85017 5.05751V18.6921C2.85017 18.769 2.88222 18.8395 2.94632 18.9036C3.01044 18.9677 3.08096 18.9998 3.1579 18.9998ZM5.92517 17.0594H11.0252C11.321 17.0594 11.5741 16.9541 11.7845 16.7437C11.9949 16.5332 12.1001 16.28 12.1001 15.9841C12.1001 15.6881 11.9949 15.435 11.7845 15.2248C11.5741 15.0145 11.321 14.9094 11.0252 14.9094H5.92517C5.62934 14.9094 5.37622 15.0146 5.16582 15.2251C4.9554 15.4356 4.8502 15.6888 4.8502 15.9847C4.8502 16.2806 4.9554 16.5337 5.16582 16.744C5.37622 16.9542 5.62934 17.0594 5.92517 17.0594ZM5.92517 12.9498H14.0752C14.371 12.9498 14.6241 12.8445 14.8345 12.6341C15.0449 12.4236 15.1501 12.1704 15.1501 11.8745C15.1501 11.5785 15.0449 11.3254 14.8345 11.1152C14.6241 10.9049 14.371 10.7998 14.0752 10.7998H5.92517C5.62934 10.7998 5.37622 10.905 5.16582 11.1155C4.9554 11.326 4.8502 11.5792 4.8502 11.8751C4.8502 12.171 4.9554 12.4241 5.16582 12.6344C5.37622 12.8446 5.62934 12.9498 5.92517 12.9498ZM5.92517 8.84016H14.0752C14.371 8.84016 14.6241 8.73491 14.8345 8.52443C15.0449 8.31396 15.1501 8.06076 15.1501 7.76483C15.1501 7.46891 15.0449 7.21583 14.8345 7.00558C14.6241 6.79531 14.371 6.69018 14.0752 6.69018H5.92517C5.62934 6.69018 5.37622 6.79542 5.16582 7.00591C4.9554 7.21639 4.8502 7.46959 4.8502 7.76551C4.8502 8.06144 4.9554 8.31453 5.16582 8.52478C5.37622 8.73503 5.62934 8.84016 5.92517 8.84016ZM10.0002 3.94593C10.2168 3.94593 10.396 3.8751 10.5377 3.73343C10.6793 3.59176 10.7502 3.4126 10.7502 3.19593C10.7502 2.97926 10.6793 2.8001 10.5377 2.65843C10.396 2.51676 10.2168 2.44593 10.0002 2.44593C9.7835 2.44593 9.60434 2.51676 9.46267 2.65843C9.321 2.8001 9.25017 2.97926 9.25017 3.19593C9.25017 3.4126 9.321 3.59176 9.46267 3.73343C9.60434 3.8751 9.7835 3.94593 10.0002 3.94593Z"
        fill={isActive ? '#202020' : '#808080'}
      />
    </svg>
  )
}

export default TaskNotesIcon
