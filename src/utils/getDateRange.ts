import dayjs from 'dayjs'

const getDateRange = (numOfWeeks: number, baseDate: string): { startDate: string; endDate: string } => {
  const baseDay = dayjs(baseDate)

  const currentDayIndex = baseDay.day()
  // set the start date to Monday's date
  const daysToLastMonday = currentDayIndex === 0 ? 6 : currentDayIndex - 1
  const startDate = baseDay.subtract(daysToLastMonday, 'day')

  // Calculate the end date by adding the number of weeks (minus one week since we start counting from the current week)
  // and then adjusting to the Sunday of the last week
  const endDate = startDate.add((numOfWeeks - 1) * 7, 'day').add(6, 'day')

  return { startDate: startDate.format('YYYY-MM-DD'), endDate: endDate.format('YYYY-MM-DD') }
}

export default getDateRange
