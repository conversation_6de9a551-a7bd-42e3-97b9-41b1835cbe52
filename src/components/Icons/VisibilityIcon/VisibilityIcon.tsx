import { type IconProps } from '../Icon.props'

const VisibilityIcon = ({ height, width, rotation, fillColor }: IconProps) => {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.28605 11.7273C7.02615 15.015 9.60137 16.455 12 16.455C14.3986 16.455 16.9738 15.015 19.7139 11.7273C16.9739 8.44005 14.3987 7.00024 12 7.00024C9.60133 7.00024 7.02612 8.44005 4.28605 11.7273ZM12 5.00024C15.456 5.00024 18.7135 7.19873 21.7863 11.1094C22.0712 11.472 22.0712 11.9824 21.7863 12.345C18.7135 16.2562 15.4561 18.455 12 18.455C8.54391 18.455 5.28645 16.2562 2.21366 12.345C1.92877 11.9824 1.92878 11.472 2.2137 11.1094C5.28652 7.19873 8.54397 5.00024 12 5.00024ZM12 10.7272C11.4483 10.7272 11 11.1755 11 11.7272C11 12.2789 11.4483 12.7272 12 12.7272C12.5517 12.7272 13 12.2789 13 11.7272C13 11.1755 12.5517 10.7272 12 10.7272ZM9 11.7272C9 10.0709 10.3437 8.72717 12 8.72717C13.6563 8.72717 15 10.0709 15 11.7272C15 13.3835 13.6563 14.7272 12 14.7272C10.3437 14.7272 9 13.3835 9 11.7272Z"
        fill="#202020"
      />
    </svg>
  )
}

export default VisibilityIcon
