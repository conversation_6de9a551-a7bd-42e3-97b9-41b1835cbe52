import { render, screen, fireEvent } from '@testing-library/react'
import CalendarSorting from '../../../components/Calendar/CalendarSorting/CalendarSorting'
import { sortOptions } from '@constants/sortOptions'
import { activeSortVar } from '@gql/client/localState'

describe('CalendarSorting', () => {
  beforeEach(() => {
    activeSortVar(sortOptions[0])
  })

  it('renders without crashing', () => {
    render(<CalendarSorting />)
    expect(screen.getByText('Sort A to Z')).toBeInTheDocument()
  })

  it('displays sort options', () => {
    render(<CalendarSorting />)
    fireEvent.mouseDown(screen.getByRole('combobox'))
    sortOptions.forEach((option) => {
      expect(screen.getAllByText(option.displayName).length).toBeGreaterThan(0)
    })
  })

  it('calls activeSortVar when an option is selected', () => {
    render(<CalendarSorting />)
    fireEvent.mouseDown(screen.getByRole('combobox'))
    fireEvent.click(screen.getByText(sortOptions[1].displayName))
    expect(activeSortVar()).toEqual(sortOptions[1])
  })

  it('displays the selected sort option', () => {
    render(<CalendarSorting />)
    fireEvent.mouseDown(screen.getByRole('combobox'))
    fireEvent.click(screen.getByText(sortOptions[1].displayName))
    expect(screen.getByRole('combobox')).toHaveTextContent(sortOptions[1].displayName)
  })
})
