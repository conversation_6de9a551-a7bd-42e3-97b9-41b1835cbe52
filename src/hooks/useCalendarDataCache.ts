// GQL and Apollo imports
import { useQuery, useReactiveVar } from '@apollo/client'

import { activeDatesVar } from '@gql/client/localState'
import CALENDAR_QUERY from '@gql/queries/calendar.query'

// Types import
import { type CalendarDayData } from '@typeDefs/Calendar'

const useCalendarDataCache = (): { calendarDays: CalendarDayData[] } => {
  const activeDates = useReactiveVar(activeDatesVar)

  const { data: calendarData } = useQuery(CALENDAR_QUERY, {
    variables: { params: activeDates }
  })

  const calendarDays: CalendarDayData[] = calendarData?.getCalendarData?.flatMap(
    (data: { days: CalendarDayData[] }) => data.days
  )

  return { calendarDays }
}

export default useCalendarDataCache
