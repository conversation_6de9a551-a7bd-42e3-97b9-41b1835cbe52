import { type IconProps } from '../Icon.props'

const TSPlusIcon = ({ height, width }: IconProps) => {
  return (
    <svg width={width} height={height} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="32" height="32" rx="16" fill="#DBEAF9" />
      <path
        d="M16.5166 15.8201L18.7695 18.0437C18.9007 18.2029 18.9663 18.3849 18.9663 18.5897C18.9663 18.7945 18.8968 18.9633 18.7577 19.096C18.6047 19.2428 18.4279 19.3182 18.2271 19.3222C18.0264 19.3262 17.8604 19.2468 17.7292 19.0841L15.2939 16.6537C15.2244 16.5845 15.1648 16.4961 15.1153 16.3885C15.0657 16.2808 15.0409 16.1665 15.0409 16.0454V12.5149C15.0409 12.3138 15.1097 12.1409 15.2474 11.9962C15.3851 11.8515 15.5627 11.7791 15.7803 11.7791C15.998 11.7791 16.1751 11.847 16.3117 11.9828C16.4483 12.1186 16.5166 12.297 16.5166 12.518V15.8201ZM15.8967 24C14.8098 24 13.7876 23.7945 12.8301 23.3834C11.8727 22.9724 11.0334 22.3982 10.3124 21.6609C9.5913 20.9235 9.02564 20.0712 8.61539 19.104C8.20513 18.1367 8 17.1046 8 16.0076C8 14.9013 8.2071 13.8642 8.62129 12.8966C9.03547 11.929 9.60113 11.0785 10.3183 10.3451C11.0354 9.61172 11.8769 9.03752 12.8428 8.62251C13.8088 8.2075 14.8371 8 15.9276 8C16.1189 8 16.305 8.00637 16.4858 8.01912C16.6667 8.03186 16.847 8.04965 17.0269 8.07249C17.2825 8.10857 17.4965 8.20807 17.669 8.371C17.8416 8.53392 17.9278 8.73466 17.9278 8.97323C17.9278 9.22975 17.8416 9.44881 17.669 9.63043C17.4965 9.81205 17.2825 9.89278 17.0269 9.8726C16.8452 9.84447 16.6635 9.81985 16.4818 9.79874C16.3001 9.77764 16.1121 9.76709 15.9178 9.76709C14.2059 9.76709 12.7475 10.3737 11.5425 11.5869C10.3375 12.8001 9.73506 14.2704 9.73506 15.9978C9.73506 17.7251 10.3337 19.1982 11.5311 20.4168C12.7284 21.6355 14.1826 22.2448 15.8935 22.2448C17.6143 22.2448 19.0744 21.6362 20.2737 20.4191C21.4731 19.2019 22.0727 17.7308 22.0727 16.006C22.0727 15.7645 22.1561 15.5541 22.3228 15.3748C22.4896 15.1955 22.6956 15.1059 22.9408 15.1059C23.1861 15.1059 23.3938 15.1955 23.5641 15.3748C23.7344 15.5541 23.8196 15.7645 23.8196 16.006C23.8196 17.1059 23.6144 18.139 23.2042 19.1053C22.7939 20.0717 22.2263 20.9235 21.5013 21.6609C20.7763 22.3982 19.9327 22.9724 18.9704 23.3834C18.0082 23.7945 16.9836 24 15.8967 24Z"
        fill="#1077D6"
      />
      <path
        d="M19.226 9.93448H21.1945V7.98768C21.1945 7.76641 21.2699 7.58115 21.4206 7.43189C21.5713 7.28264 21.7515 7.20801 21.9611 7.20801C22.1707 7.20801 22.3496 7.28177 22.4977 7.42928C22.6458 7.5768 22.7198 7.76365 22.7198 7.98984V9.93448H24.7058C24.9228 9.93448 25.104 10.007 25.2496 10.152C25.3952 10.297 25.468 10.4829 25.468 10.7096C25.468 10.9249 25.3922 11.1058 25.2407 11.2523C25.0892 11.3988 24.9123 11.4721 24.7101 11.4721H22.7198V13.4623C22.7198 13.666 22.6457 13.8412 22.4976 13.9879C22.3494 14.1346 22.1718 14.208 21.9646 14.208C21.754 14.208 21.573 14.1346 21.4216 13.9879C21.2702 13.8412 21.1945 13.666 21.1945 13.4623V11.4721H19.2303C18.9992 11.4721 18.8107 11.4009 18.665 11.2586C18.5192 11.1163 18.4463 10.9317 18.4463 10.705C18.4463 10.4852 18.5209 10.3018 18.6702 10.1549C18.8194 10.0079 19.0047 9.93448 19.226 9.93448Z"
        fill="#1077D6"
      />
    </svg>
  )
}

export default TSPlusIcon
