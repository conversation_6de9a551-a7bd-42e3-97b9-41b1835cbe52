import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import FormTabs from '@components/Forms/AllocationDetailsForm/FormTabs/FormTabs'
import { userProfileVar } from '@gql/client/localState'
import { UserProfile } from '@constants/userProfile'

// Mock icons
jest.mock('@components/Icons/AssignmentNotesIcon/AssignmentNotesIcon', () =>
  jest.fn(() => <div>AssignmentNotesIcon</div>)
)
jest.mock('@components/Icons/TaskNotesIcon/TaskNotesIcon', () => jest.fn(() => <div>TaskNotesIcon</div>))
jest.mock('@components/Icons/ReadMoreIcon/ReadMoreIcon', () => jest.fn(() => <div>ReadMoreIcon</div>))

describe('FormTabs Component', () => {
  const defaultProps = {
    taskNotes: 'Task notes content',
    assignmentNotes: 'Assignment notes content',
    openNotesView: jest.fn(),
    isDisplayingNotes: false
  }

  test('renders FormTabs component', () => {
    userProfileVar(UserProfile.PROJECT_MANAGER)
    render(<FormTabs {...defaultProps} />)
    expect(screen.getByText('Assignment Notes')).toBeInTheDocument()
    expect(screen.getByText('Task Notes')).toBeInTheDocument()
  })

  test('renders assignment notes content correctly', () => {
    render(<FormTabs {...defaultProps} />)
    expect(screen.getByText('Assignment notes content')).toBeInTheDocument()
  })

  test('renders task notes content correctly', () => {
    render(<FormTabs {...defaultProps} />)
    fireEvent.click(screen.getByText('Task Notes'))
    expect(screen.getByText('Task notes content')).toBeInTheDocument()
  })

  test('displays "Read More" button and triggers openNotesView function', () => {
    render(<FormTabs {...defaultProps} />)
    const readMoreButton = screen.getByText('Read More')
    expect(readMoreButton).toBeInTheDocument()
    fireEvent.click(readMoreButton)
    expect(defaultProps.openNotesView).toHaveBeenCalledWith(true)
  })

  test('disables tabs correctly based on props', () => {
    render(<FormTabs {...defaultProps} assignmentNotes={''} />)
    const assignmentNotesTab = screen.getByTestId('assignment-notes-tab')
    const taskNotesTab = screen.getByTestId('task-notes-tab')
    expect(assignmentNotesTab).toBeDisabled()
    expect(taskNotesTab).toBeEnabled()
  })

  test('does not display "Read More" button when isDisplayingNotes is true', () => {
    render(<FormTabs {...defaultProps} isDisplayingNotes={true} />)
    expect(screen.queryByText('Read More')).not.toBeInTheDocument()
  })

  test('displays "There are no notes available for this task" when assignmentNotes is empty string', () => {
    render(<FormTabs {...defaultProps} assignmentNotes={''} taskNotes={''} />)
    expect(screen.getByText('There are no notes available for this task')).toBeInTheDocument()
  })

  test('displays "There are no notes available for this task" when taskNotes is empty string', () => {
    render(<FormTabs {...defaultProps} assignmentNotes={''} taskNotes={''} />)
    fireEvent.click(screen.getByText('Task Notes'))
    expect(screen.getByText('There are no notes available for this task')).toBeInTheDocument()
  })
})
