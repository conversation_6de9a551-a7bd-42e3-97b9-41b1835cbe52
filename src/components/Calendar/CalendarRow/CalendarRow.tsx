// React imports
import { memo } from 'react'

// UI library imports
import { Grid, palette } from 'lion-ui'

// Component imports
import CalendarDrag from '@components/Calendar/CalendarDrag/CalendarDrag'
import ResourceBox from '@components/Calendar/ResourceBox/ResourceBox'
import type { CalendarRowProps } from './CalendarRow.props'
import type React from 'react'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const CalendarRow = ({ index, resource, assignments, type }: CalendarRowProps): React.JSX.Element => {
  return (
    <Grid container sx={{ display: 'flex', flexWrap: 'nowrap', borderBottom: `1px solid ${palette.secondary.main}` }}>
      <Grid sx={{ width: '20%', minWidth: '18.125rem' }}>
        <ResourceBox resource={resource} assignments={assignments} type={type} />
      </Grid>
      <Grid sx={{ width: '80%' }}>
        <CalendarDrag id={`${index}`} key={index} assignments={assignments} resource={resource} type={type} />
      </Grid>
    </Grid>
  )
}

export default memo(CalendarRow)
