import dayjs from 'dayjs'

import updateActiveDates from '@utils/updateActiveDates'
import { activeDatesVar, numOfWeeksVar } from '@gql/client/localState'

import getDateRange from '@utils/getDateRange'
import { CalendarViewOptions } from '@constants/calendarViewOptions'

const today = dayjs().format('YYYY-MM-DD')
const numOfWeeks = 2

describe('updateActiveDates', () => {
  const resetState = (): void => {
    numOfWeeksVar(CalendarViewOptions.TWO_WEEKS)
    const { startDate, endDate } = getDateRange(numOfWeeks, today)
    activeDatesVar({ startDate, endDate })
  }

  beforeEach(() => {
    resetState()
  })

  it('updates active dates to the next week', () => {
    const { startDate: oldStartDate, endDate: oldEndDate } = activeDatesVar()
    updateActiveDates('next', 1)

    expect(activeDatesVar().startDate).toBe(dayjs(oldStartDate).add(1, 'week').format('YYYY-MM-DD'))
    expect(activeDatesVar().endDate).toBe(dayjs(oldEndDate).add(1, 'week').format('YYYY-MM-DD'))
  })

  it('updates active dates to the previous week', () => {
    const { startDate: oldStartDate, endDate: oldEndDate } = activeDatesVar()
    updateActiveDates('prev', 1)

    expect(activeDatesVar().startDate).toBe(dayjs(oldStartDate).subtract(1, 'week').format('YYYY-MM-DD'))
    expect(activeDatesVar().endDate).toBe(dayjs(oldEndDate).subtract(1, 'week').format('YYYY-MM-DD'))
  })

  it('updates active dates to the current week', () => {
    const { startDate: oldStartDate, endDate: oldEndDate } = activeDatesVar()
    updateActiveDates('', 0)

    expect(activeDatesVar().startDate).toBe(oldStartDate)
    expect(activeDatesVar().endDate).toBe(oldEndDate)
  })

  it('updates active dates to the current day', () => {
    numOfWeeksVar(CalendarViewOptions.ONE_DAY)
    updateActiveDates('', 0)
    const today = dayjs().format('YYYY-MM-DD')

    expect(activeDatesVar().startDate).toBe(today)
    expect(activeDatesVar().endDate).toBe(today)
  })

  it('updates active dates to the next day', () => {
    numOfWeeksVar(CalendarViewOptions.ONE_DAY)
    const { startDate: oldStartDate } = activeDatesVar()
    updateActiveDates('next', 0)

    expect(activeDatesVar().startDate).toBe(dayjs(oldStartDate).add(1, 'day').format('YYYY-MM-DD'))
    expect(activeDatesVar().endDate).toBe(dayjs(oldStartDate).add(1, 'day').format('YYYY-MM-DD'))
  })

  it('updates active dates to the previous day', () => {
    numOfWeeksVar(CalendarViewOptions.ONE_DAY)
    const { startDate: oldStartDate } = activeDatesVar()
    updateActiveDates('prev', 0)

    expect(activeDatesVar().startDate).toBe(dayjs(oldStartDate).subtract(1, 'day').format('YYYY-MM-DD'))
    expect(activeDatesVar().endDate).toBe(dayjs(oldStartDate).subtract(1, 'day').format('YYYY-MM-DD'))
  })
})
