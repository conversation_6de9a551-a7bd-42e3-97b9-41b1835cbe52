// ErrorFallback.test.tsx

import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import { ErrorFallback } from '@components/ErrorBoundary/ErrorFallback'

describe('ErrorFallback Component', () => {
  const title = 'Error Occurred'
  const subtitle = 'Something went wrong'

  test('renders title, image, and subtitle', () => {
    render(<ErrorFallback title={title} subtitle={subtitle} />)

    expect(screen.getByText(title)).toBeInTheDocument()
    expect(screen.getByAltText('A broken robot depicting an error in the application')).toBeInTheDocument()
    expect(screen.getByText(subtitle)).toBeInTheDocument()
  })

  test('renders action button when action prop is provided', () => {
    const action = jest.fn()
    render(<ErrorFallback title={title} subtitle={subtitle} action={action} />)

    const button = screen.getByRole('button', { name: /try again\?/i })
    expect(button).toBeInTheDocument()

    fireEvent.click(button)
    expect(action).toHaveBeenCalledTimes(1)
  })

  test('does not render action button when action prop is not provided', () => {
    render(<ErrorFallback title={title} subtitle={subtitle} />)

    const button = screen.queryByRole('button', { name: /try again\?/i })
    expect(button).not.toBeInTheDocument()
  })
})
