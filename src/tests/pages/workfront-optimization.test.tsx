import { render } from '@testing-library/react'
import { ApolloProvider } from '@apollo/client'
import WorkfrontRegister from '../../pages/Workfront/Register'
import WorkfrontPlugin from '../../pages/Workfront/Plugin'
import apolloClientWorkfront from '../../gql/client/apolloClientWorkfront'
import { isWorkfrontComponent } from '../../utils/workfront'

// Mock Adobe UIX Guest
jest.mock('@adobe/uix-guest', () => ({
  register: jest.fn().mockResolvedValue({}),
  attach: jest.fn().mockResolvedValue({
    sharedContext: {
      get: jest.fn().mockReturnValue({
        imsToken: 'mock-token',
        ID: 'mock-user-id'
      })
    }
  })
}))

// Mock router
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    pathname: '/Workfront/Register'
  })
}))

describe('Workfront Performance Optimization', () => {
  describe('isWorkfrontComponent utility', () => {
    it('should identify WorkfrontRegister as a Workfront component', () => {
      expect(isWorkfrontComponent(WorkfrontRegister)).toBe(true)
    })

    it('should identify WorkfrontPlugin as a Workfront component', () => {
      expect(isWorkfrontComponent(WorkfrontPlugin)).toBe(true)
    })

    it('should not identify regular components as Workfront components', () => {
      const RegularComponent = () => <div>Regular</div>
      expect(isWorkfrontComponent(RegularComponent)).toBe(false)
    })
  })

  describe('Workfront Register Component', () => {
    it('should render without authentication providers', () => {
      const { container } = render(
        <ApolloProvider client={apolloClientWorkfront}>
          <WorkfrontRegister />
        </ApolloProvider>
      )

      expect(container).toBeInTheDocument()
    })
  })

  describe('Apollo Client Workfront', () => {
    it('should be configured without authentication', () => {
      expect(apolloClientWorkfront).toBeDefined()
      expect(apolloClientWorkfront.cache).toBeDefined()

      // The Workfront Apollo client should not have auth link
      // This is verified by the fact that it doesn't import getAccessToken
      expect(apolloClientWorkfront.link).toBeDefined()
    })
  })
})
