import getLocaleDateFormat from '@utils/getLocaleDateFormat'
import dayjs from 'dayjs'

describe('getLocaleDateFormat', () => {
  const originalNavigator = { ...navigator }

  afterEach(() => {
    // Restore original navigator object after each test
    Object.assign(navigator, originalNavigator)
  })

  it('should return date in MM/DD/YYYY format for en-US locale', () => {
    Object.defineProperty(navigator, 'language', {
      value: 'en-US',
      configurable: true
    })
    const date = '2023-10-05'
    const expected = dayjs(date).format('MM/DD/YYYY')
    expect(getLocaleDateFormat(date)).toBe(expected)
  })

  it('should return date in DD/MM/YYYY format for non en-US locale', () => {
    Object.defineProperty(navigator, 'language', {
      value: 'fr-FR',
      configurable: true
    })
    const date = '2023-10-05'
    const expected = dayjs(date).format('DD/MM/YYYY')
    expect(getLocaleDateFormat(date)).toBe(expected)
  })

  it('should handle different date strings correctly', () => {
    Object.defineProperty(navigator, 'language', {
      value: 'en-US',
      configurable: true
    })
    const date = '2023-01-15'
    const expected = dayjs(date).format('MM/DD/YYYY')
    expect(getLocaleDateFormat(date)).toBe(expected)

    Object.defineProperty(navigator, 'language', {
      value: 'de-DE',
      configurable: true
    })
    const expectedDE = dayjs(date).format('DD/MM/YYYY')
    expect(getLocaleDateFormat(date)).toBe(expectedDE)
  })
})
