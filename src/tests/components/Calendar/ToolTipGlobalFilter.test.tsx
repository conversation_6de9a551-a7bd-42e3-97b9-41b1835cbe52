import { render, screen } from '@testing-library/react'
import ToolTipGlobalFilter from '@components/Calendar/ToolTipGlobalFilter/ToolTipGlobalFilter'
import type { LabelItem } from '@typeDefs/LabelItem'

describe('ToolTipGlobalFilter Component', () => {
  it('renders the filter name correctly', () => {
    const mockFilter: LabelItem = {
      id: '1',
      name: 'Test Filter',
      category: 'Test Category',
      icon: { src: '/test-icon.png' },
    }

    render(<ToolTipGlobalFilter filter={mockFilter} />)

    expect(screen.getByText('Test Filter')).toBeInTheDocument()
  })

  it('applies the correct styles', () => {
    const mockFilter: LabelItem = {
      id: '1',
      name: 'Styled Filter',
      category: 'Test Category',
      icon: { src: '/test-icon.png' },
    }

    render(<ToolTipGlobalFilter filter={mockFilter} />)

    const typographyElement = screen.getByText('Styled Filter')
    expect(typographyElement).toHaveStyle('font-size: 0.75rem')
  })
})