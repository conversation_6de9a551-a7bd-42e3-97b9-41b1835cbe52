// lion ui import
import { Box, Typography } from 'lion-ui'

// Component imports
import type React from 'react'
import type { LabelItem } from '@typeDefs/LabelItem'

const ToolTipGlobalFilter: React.FC<{ filter: LabelItem }> = ({ filter }) => {
  return (    
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <Typography sx={{ fontSize: '0.75rem' }}> {filter.name}</Typography>
      </Box>
    )
  }
  
  export default ToolTipGlobalFilter