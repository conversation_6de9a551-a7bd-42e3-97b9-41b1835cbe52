import { userProfileVar } from '@gql/client/localState/userProfile'
import { numOfWeeksVar } from '@gql/client/localState/numOfWeeks'
import { activeDatesVar } from '@gql/client/localState/activeDates'

export const typePolicies = {
  activeProfile: {
    fields: {
      activeProfile: {
        read() {
          return userProfileVar()
        }
      }
    }
  },
  numOfWeeks: {
    fields: {
      numOfWeeks: {
        read() {
          return numOfWeeksVar()
        }
      }
    }
  },
  activeDates: {
    fields: {
      activeDates: {
        read() {
          return activeDatesVar()
        }
      }
    }
  }
}
