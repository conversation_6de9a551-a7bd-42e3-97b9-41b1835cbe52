import { useState, useEffect, useCallback } from 'react'

/**
 * Custom hook to debounce filtering of a list.
 *
 * @param list - The list to filter.
 * @param filterFunc - The filter function to apply.
 * @param delay - The debounce delay in milliseconds.
 * @returns The filtered list.
 */
export function useDebounce<T>(list: T[], filterFunc: (item: T) => boolean, delay = 1000): T[] {
  const [filteredItems, setFilteredItems] = useState<T[]>(list)

  const debouncedFilter = useCallback(() => {
    const handler = setTimeout(() => {
      setFilteredItems(list.filter(filterFunc))
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [list, filterFunc, delay])

  useEffect(() => {
    return debouncedFilter()
  }, [debouncedFilter])

  return filteredItems
}
