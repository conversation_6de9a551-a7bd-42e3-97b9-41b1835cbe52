import { ListItemIcon, ListItemText, Menu, MenuItem, palette } from 'lion-ui'
import { type TileContextMenuProps } from './TileContextMenu.props'
import { showSplitAssignmentFormVar } from '@gql/client/localState/SplitAssignmentForm'

import ContentCut from '@mui/icons-material/ContentCut'
import DeleteIcon from '@components/Icons/DeleteIcon/DeleteIcon'

const TileContextMenu: React.FC<TileContextMenuProps> = ({
  anchorPosition,
  handleClose,
  assignment,
  onOpenRemoveModal
}) => {
  const handleClickOpenRemovePopup = () => {
    handleClose()
    onOpenRemoveModal()
  }

  const handleOpenSplitForm = () => {
    showSplitAssignmentFormVar(assignment)
    handleClose()
  }

  return (
    <Menu
      open={Boolean(anchorPosition)}
      onClose={handleClose}
      anchorReference="anchorPosition"
      anchorPosition={anchorPosition ? { top: anchorPosition.mouseY, left: anchorPosition.mouseX } : undefined}
      disablePortal
    >
      {assignment.width >= 2 && (
        <MenuItem onClick={handleOpenSplitForm} sx={{ color: palette.grey['900'], margin: 1, borderRadius: '8px' }}>
          <ListItemIcon sx={{ color: palette.grey['900'] }}>
            <ContentCut fontSize="small" />
          </ListItemIcon>
          <ListItemText sx={{ color: palette.grey['900'] }}>Split</ListItemText>
        </MenuItem>
      )}

      <MenuItem
        onClick={handleClickOpenRemovePopup}
        sx={{ color: palette.grey['900'], margin: 1, borderRadius: '8px' }}
      >
        <ListItemIcon sx={{ color: palette.grey['900'] }}>
          <DeleteIcon fillColor={palette.grey['900']} height="20" width="20" />
        </ListItemIcon>
        <ListItemText sx={{ color: palette.grey['900'] }}>Remove allocation</ListItemText>
      </MenuItem>
    </Menu>
  )
}

export default TileContextMenu
