// dayjs library imports
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'

import { type Assignment } from '@typeDefs/Assignments'
import { type CalendarDayData } from '@typeDefs/Calendar'

dayjs.extend(isBetween)

const calculatesHoursByDay = (calendarDay: CalendarDayData, assignmentsMap: Map<string, Assignment[]>): number => {
  const targetDate = dayjs(calendarDay.date).format('YYYY-MM-DD')
  const assignmentsOnDay = assignmentsMap.get(targetDate) ?? []

  const totalHours = assignmentsOnDay.reduce((sum, assignment) => {
    return sum + assignment.hoursPerDay
  }, 0)

  return totalHours
}

const getAssignmentsMappedByDate = (assignments: Assignment[]): Map<string, Assignment[]> => {
  const assignmentsMap = new Map<string, Assignment[]>()

  assignments.forEach((assignment) => {
    const startDate = dayjs(assignment.startDate)
    const dueDate = dayjs(assignment.dueDate)
    for (let date = startDate; date.isBefore(dueDate) || date.isSame(dueDate); date = date.add(1, 'day')) {
      const formattedDate = date.format('YYYY-MM-DD')
      if (!assignmentsMap.has(formattedDate)) {
        assignmentsMap.set(formattedDate, [])
      }
      const assignments: Assignment[] = assignmentsMap.get(formattedDate) ?? []
      assignmentsMap.set(formattedDate, [...assignments, assignment])
    }
  })
  return assignmentsMap
}

export { calculatesHoursByDay, getAssignmentsMappedByDate }
