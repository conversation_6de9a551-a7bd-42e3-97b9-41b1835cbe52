import { render, screen } from '@testing-library/react'
import { useContext } from 'react'
import RenderKeyContext, { RenderKeyContextProvider } from '../../context/renderKeyContext'

// Helper component to test context values
const TestComponent = () => {
  const { renderKey, setRenderKey } = useContext(RenderKeyContext)
  return (
    <div>
      <span data-testid="renderKey">{renderKey}</span>
      <button onClick={() => { setRenderKey(Date.now()) }}>Update RenderKey</button>
    </div>
  )
}

describe('RenderKeyContext', () => {
  test('provides default values', () => {
    render(
      <RenderKeyContextProvider>
        <TestComponent />
      </RenderKeyContextProvider>
    )
    const renderKeyElement = screen.getByTestId('renderKey')
    expect(renderKeyElement).toBeInTheDocument()
    expect(renderKeyElement.textContent).toBeTruthy()
  })

  test('updates renderKey value', () => {
    render(
      <RenderKeyContextProvider>
        <TestComponent />
      </RenderKeyContextProvider>
    )
    const renderKeyElement = screen.getByTestId('renderKey')
    const initialRenderKey: string | RegExp = renderKeyElement.textContent ?? ''
    const button = screen.getByText('Update RenderKey')
    button.click()
    setTimeout(() => {
      expect(renderKeyElement).not.toHaveTextContent(initialRenderKey)
    }, 0)
  })
})
