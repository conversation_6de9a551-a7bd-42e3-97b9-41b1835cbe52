import { render } from '@testing-library/react'
import ExtensionRegistration from '../../../pages/Workfront/Register'

// Mock the workfront utilities
jest.mock('@utils/workfront', () => ({
  workfrontConfig: {
    id: 'test-id',
    mainMenuId: 'test-main-menu-id',
    uixId: 'test-uix-id',
    uixMainMenuId: 'test-uix-main-menu-id',
    instanceId: 'test-instance-id',
    isInIframe: true,
    menuLabel: 'Test Menu Label',
    url: 'http://test-url.com'
  }
}))

jest.mock('@utils/workfront-debug', () => ({
  debugWorkfrontConfig: jest.fn()
}))

// Mock the Adobe UIX guest module
const mockRegister = jest.fn()
jest.mock('@adobe/uix-guest', () => ({
  register: mockRegister
}))

// Mock the icon
jest.mock('../../../../public/icons', () => ({
  // eslint-disable-next-line @typescript-eslint/naming-convention
  RmCalendarIcon: 'mock-icon'
}))

// Mock console methods
const consoleSpy = {
  log: jest.spyOn(console, 'log'),
  error: jest.spyOn(console, 'error')
}

describe('ExtensionRegistration', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    consoleSpy.log.mockClear()
    consoleSpy.error.mockClear()
    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: {
        hostname: 'test-hostname',
        href: 'http://test-hostname/register'
      },
      writable: true
    })
  })

  it('renders without crashing', () => {
    render(<ExtensionRegistration />)
  })

  it('calls debugWorkfrontConfig on mount', async () => {
    const { debugWorkfrontConfig } = await import('@utils/workfront-debug')
    
    render(<ExtensionRegistration />)
    
    // Wait for useEffect to run
    await new Promise(resolve => setTimeout(resolve, 0))
    
    expect(debugWorkfrontConfig).toHaveBeenCalled()
  })

  it('logs initialization config', async () => {
    render(<ExtensionRegistration />)

    // Wait for useEffect to run
    await new Promise(resolve => setTimeout(resolve, 100))

    expect(consoleSpy.log).toHaveBeenCalledWith(
      'Extension Registration - Initializing with config:',
      expect.objectContaining({
        id: 'test-id',
        mainMenuId: 'test-main-menu-id',
        uixId: 'test-uix-id',
        uixMainMenuId: 'test-uix-main-menu-id',
        instanceId: 'test-instance-id',
        isInIframe: true,
        hostname: 'test-hostname',
        url: 'http://test-hostname/register'
      })
    )
  })

  it('calls Adobe UIX register with correct configuration', async () => {
    mockRegister.mockResolvedValue(undefined)
    
    render(<ExtensionRegistration />)
    
    // Wait for useEffect and async operations to complete
    await new Promise(resolve => setTimeout(resolve, 100))
    
    expect(mockRegister).toHaveBeenCalledWith({
      methods: {
        id: 'test-uix-id',
        mainMenu: {
          getItems: expect.any(Function)
        }
      }
    })
  })

  it('returns correct menu items from getItems function', async () => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let capturedGetItems: (() => any[]) | undefined

    // eslint-disable-next-line @typescript-eslint/promise-function-async
    mockRegister.mockImplementation((config) => {
      capturedGetItems = config.methods.mainMenu.getItems
      return Promise.resolve()
    })
    
    render(<ExtensionRegistration />)
    
    // Wait for useEffect and async operations to complete
    await new Promise(resolve => setTimeout(resolve, 100))
    
    expect(capturedGetItems).toBeDefined()
    
    if (capturedGetItems) {
      const menuItems = capturedGetItems()
      
      expect(menuItems).toEqual([
        {
          id: 'test-uix-main-menu-id',
          label: 'Test Menu Label',
          icon: 'mock-icon',
          url: 'http://test-url.com'
        }
      ])
    }
  })

  it('logs success message after successful registration', async () => {
    mockRegister.mockResolvedValue(undefined)

    render(<ExtensionRegistration />)

    // Wait for useEffect and async operations to complete
    await new Promise(resolve => setTimeout(resolve, 200))

    expect(consoleSpy.log).toHaveBeenCalledWith(
      'Extension Registration - Successfully registered with UIX ID:',
      'test-uix-id'
    )
    expect(consoleSpy.log).toHaveBeenCalledWith(
      'Extension Registration - URLs will use original ID:',
      'test-id'
    )
    expect(consoleSpy.log).toHaveBeenCalledWith(
      'Extension Registration - Successfully registered'
    )
  })

  it('handles registration errors gracefully', async () => {
    const mockError = new Error('Registration failed')
    mockRegister.mockRejectedValue(mockError)
    
    render(<ExtensionRegistration />)
    
    // Wait for useEffect and async operations to complete
    await new Promise(resolve => setTimeout(resolve, 100))
    
    expect(consoleSpy.error).toHaveBeenCalledWith(
      'Extension Registration - Error during registration:',
      mockError
    )
    expect(consoleSpy.error).toHaveBeenCalledWith(
      'Extension Registration - Failed with config:',
      expect.objectContaining({
        id: 'test-id',
        uixId: 'test-uix-id'
      })
    )
  })

  it('handles init function errors', async () => {
    const mockError = new Error('Registration failed')
    mockRegister.mockRejectedValue(mockError)

    render(<ExtensionRegistration />)

    // Wait for useEffect and async operations to complete
    await new Promise(resolve => setTimeout(resolve, 200))

    expect(consoleSpy.error).toHaveBeenCalledWith(
      'Extension Registration - Error during registration:',
      mockError
    )
  })

  it('renders empty div', () => {
    const { container } = render(<ExtensionRegistration />)

    // eslint-disable-next-line testing-library/no-node-access
    expect(container.firstChild).toBeInstanceOf(HTMLDivElement)
    // eslint-disable-next-line testing-library/no-node-access
    expect(container.firstChild?.textContent).toBe('')
  })
})
