import { render, screen } from '@testing-library/react'
import CalendarUtilizationCell from '../../../components/Calendar/CalendarUtilizationCell/CalendarUtilizationCell'

describe('CalendarUtilizationCell', () => {
  it('should render without crashing', () => {
    const { container } = render(
      <CalendarUtilizationCell hours={8} numOfDaysInView={5} nonWorkingDay={false} minimumAgencyHoursPerDay={8} />
    )
    expect(container).toBeInTheDocument()
  })

  it('should display the correct number of hours', () => {
    render(<CalendarUtilizationCell hours={8} numOfDaysInView={5} nonWorkingDay={false} minimumAgencyHoursPerDay={8} />)
    expect(screen.getByText('0h')).toBeInTheDocument()
  })

  it('should handle non-working days correctly', () => {
    const { container } = render(
      <CalendarUtilizationCell hours={8} numOfDaysInView={5} nonWorkingDay={true} minimumAgencyHoursPerDay={0} />
    )
    expect(container).toBeInTheDocument()

    const cellContainer = screen.getByTestId('calendar-cell-container')
    const containerStyles = getComputedStyle(cellContainer)

    // It should have the grey background color
    expect(containerStyles.backgroundColor).toBe('rgb(249, 249, 249)')

    // It should have the correct text color
    const text = screen.getByTestId('calendar-cell-text')
    expect(getComputedStyle(text).color).toBe('rgb(215, 50, 45)')
  })

  it('should calculate utilization correctly', () => {
    render(<CalendarUtilizationCell hours={4} numOfDaysInView={5} nonWorkingDay={false} minimumAgencyHoursPerDay={8} />)
    const bar = screen.getByTestId('calendar-cell-bar')
    const barStyles = getComputedStyle(bar)
    expect(barStyles.width).toBe('50%')
  })
})
