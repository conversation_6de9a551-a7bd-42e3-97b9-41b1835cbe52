import '@testing-library/jest-dom'
import { render, screen } from '@testing-library/react'
import CalendarHeader from '@components/Calendar/CalendarHeader/CalendarHeader'
import { type CalendarData, type CalendarDayData } from '@typeDefs/Calendar'

describe('CalendarHeader', () => {
  const mockCalendarData: CalendarData[] = [
    {
      month: 'January',
      days: [
        { date: '2023-01-01', dayNumber: 1, dayShortName: 'Sun' },
        { date: '2023-01-02', dayNumber: 2, dayShortName: 'Mon' }
      ] as CalendarDayData[]
    }
  ]

  it('renders the correct number of days', () => {
    render(<CalendarHeader calendarData={mockCalendarData} />)
    const days = screen.getAllByTestId('Calendar.Header.Navigation.Day.Item')
    expect(days).toHaveLength(2)
  })
})
