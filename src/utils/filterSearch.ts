import { availableFilters } from '@constants/filters'
import { type ActiveFilters } from '@typeDefs/ActiveFilters'
import { type LabelItem } from '@typeDefs/LabelItem'

export const getAllActiveFilters = (activeFilters: ActiveFilters): LabelItem[] => {
  return Object.values(activeFilters).flat()
}

export const removeFilter = (activeFilters: ActiveFilters, filter: LabelItem): ActiveFilters => {
  const { category, id } = filter

  if (category) {
    return {
      ...activeFilters,
      [category]: activeFilters[category].filter((item) => item.id !== id)
    }
  }

  return activeFilters
}

export const removeAllFilters = (): ActiveFilters => {
  return {
    projects: [],
    resources: [],
    brands: [],
    usersGroups: [],
    tasks: [],
    taskStates: [],
    placeholders: []
  }
}

export const getIconByCategory = (category: string): { src: string } => {
  return availableFilters.find((filter) => filter.id === category)?.icon
}
