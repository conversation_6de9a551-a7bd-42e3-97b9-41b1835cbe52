import { type IconProps } from '../Icon.props'

const ReadMoreIcon = ({ height, width }: IconProps) => {
  return (
    <svg width={width} height={height} viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.85704 4.81819C3.68378 7.01001 5.40059 7.97 6.99967 7.97C8.59876 7.97 10.3156 7.01001 12.1423 4.81819C10.3156 2.6267 8.59879 1.66683 6.99967 1.66683C5.40056 1.66683 3.68375 2.6267 1.85704 4.81819ZM6.99967 0.333496C9.3037 0.333496 11.4753 1.79915 13.5239 4.40627C13.7138 4.648 13.7138 4.98828 13.5239 5.23002C11.4754 7.83746 9.30374 9.30333 6.99967 9.30333C4.69561 9.30333 2.52398 7.83746 0.475446 5.23002C0.285519 4.98828 0.28553 4.648 0.475472 4.40627C2.52402 1.79915 4.69565 0.333496 6.99967 0.333496ZM6.99967 4.15145C6.63186 4.15145 6.33301 4.4503 6.33301 4.81812C6.33301 5.18593 6.63186 5.48478 6.99967 5.48478C7.36748 5.48478 7.66634 5.18593 7.66634 4.81812C7.66634 4.4503 7.36748 4.15145 6.99967 4.15145ZM4.99967 4.81812C4.99967 3.71393 5.89548 2.81812 6.99967 2.81812C8.10386 2.81812 8.99967 3.71393 8.99967 4.81812C8.99967 5.92231 8.10386 6.81812 6.99967 6.81812C5.89548 6.81812 4.99967 5.92231 4.99967 4.81812Z"
        fill="#202020"
      />
    </svg>
  )
}

export default ReadMoreIcon
