import { render, screen } from '@testing-library/react'

import ResourceBoxUtilization from '@components/Calendar/ResourceBoxUtilization/ResourceBoxUtilization'

import { mockAssignment } from '../../__mocks__/assignments.mock'
import { activeDatesVar } from '@gql/client/localState'
import { ApolloProvider } from '@apollo/client'
import apolloClientInit from '@gql/client/apolloClientInit'
import CALENDAR_QUERY from '@gql/queries/calendar.query'
import { palette } from 'lion-ui'

describe('ResourceBoxUtilization', () => {
  beforeEach(() => {
    activeDatesVar({ startDate: '2024-07-15', endDate: '2024-07-28' })
    apolloClientInit.writeQuery({
      query: CALENDAR_QUERY,
      variables: {
        params: activeDatesVar()
      },
      data: {
        getCalendarData: [
          {
            month: 'July',
            days: [
              {
                dayShortName: 'Mo',
                dayMiddleName: 'Mon',
                dayNumber: 15,
                date: '2024-07-15',
                isWeekend: false
              },
              {
                dayShortName: 'Tu',
                dayMiddleName: 'Tue',
                dayNumber: 16,
                date: '2024-07-16',
                isWeekend: false
              },
              {
                dayShortName: 'We',
                dayMiddleName: 'Wed',
                dayNumber: 17,
                date: '2024-07-17',
                isWeekend: false
              },
              {
                dayShortName: 'Th',
                dayMiddleName: 'Thu',
                dayNumber: 18,
                date: '2024-07-18',
                isWeekend: false
              },
              {
                dayShortName: 'Fr',
                dayMiddleName: 'Fri',
                dayNumber: 19,
                date: '2024-07-19',
                isWeekend: false
              },
              {
                dayShortName: 'Sa',
                dayMiddleName: 'Sat',
                dayNumber: 20,
                date: '2024-07-20',
                isWeekend: true
              },
              {
                dayShortName: 'Su',
                dayMiddleName: 'Sun',
                dayNumber: 21,
                date: '2024-07-21',
                isWeekend: true
              },
              {
                dayShortName: 'Mo',
                dayMiddleName: 'Mon',
                dayNumber: 22,
                date: '2024-07-22',
                isWeekend: false
              },
              {
                dayShortName: 'Tu',
                dayMiddleName: 'Tue',
                dayNumber: 23,
                date: '2024-07-23',
                isWeekend: false
              },
              {
                dayShortName: 'We',
                dayMiddleName: 'Wed',
                dayNumber: 24,
                date: '2024-07-24',
                isWeekend: false
              },
              {
                dayShortName: 'Th',
                dayMiddleName: 'Thu',
                dayNumber: 25,
                date: '2024-07-25',
                isWeekend: false
              },
              {
                dayShortName: 'Fr',
                dayMiddleName: 'Fri',
                dayNumber: 26,
                date: '2024-07-26',
                isWeekend: false
              },
              {
                dayShortName: 'Sa',
                dayMiddleName: 'Sat',
                dayNumber: 27,
                date: '2024-07-27',
                isWeekend: true
              },
              {
                dayShortName: 'Su',
                dayMiddleName: 'Sun',
                dayNumber: 28,
                date: '2024-07-28',
                isWeekend: true
              }
            ]
          }
        ]
      }
    })
  })

  it('should render without crashing', () => {
    render(
      <ApolloProvider client={apolloClientInit}>
        <ResourceBoxUtilization totalCapacity={50} assignments={mockAssignment} />{' '}
      </ApolloProvider>
    )
    const element = screen.getByTestId('resource-box-utilization')
    expect(element).toBeInTheDocument()
  })

  it('should display the correct utilization ', () => {
    const totalCapacity = 80
    const totalCapacityAssigned = 26

    render(
      <ApolloProvider client={apolloClientInit}>
        <ResourceBoxUtilization totalCapacity={totalCapacity} assignments={mockAssignment} />
      </ApolloProvider>
    )

    expect(screen.getByText(`${totalCapacity - totalCapacityAssigned}H`)).toBeInTheDocument()
  })

  it('should apply success styles when available capacity is positive', () => {
    render(
      <ApolloProvider client={apolloClientInit}>
        <ResourceBoxUtilization totalCapacity={50} assignments={mockAssignment} />
      </ApolloProvider>
    )
    const element = screen.getByTestId('resource-box-utilization')
    expect(element).toHaveStyle(`background-color: ${palette['Success-Surface'].main}`)
    expect(element).toHaveStyle(`color: ${palette['Success-Dark'].dark}`)
  })

  it('should apply error styles when available capacity is negative', () => {
    render(
      <ApolloProvider client={apolloClientInit}>
        <ResourceBoxUtilization totalCapacity={10} assignments={mockAssignment} />
      </ApolloProvider>
    )
    const element = screen.getByTestId('resource-box-utilization')
    expect(element).toHaveStyle(`background-color: ${palette['Error-Surface'].main}`)
    expect(element).toHaveStyle(`color: ${palette['Error-Dark'].dark}`)
  })
})
