name: RM Web App UI

trigger:
  branches:
    include:
    - development
    - qa
parameters:
- name: businessName
  displayName: 'Business name'
  type: string
  default: 'roarv2'
- name: applicationName
  displayName: 'Application name'
  type: string
  default: 'RM'
- name: env
  displayName: 'Environment name'
  type: string
  default: 'dev1'

variables:
  artifactName: 'drop'
  appName: 'Publicis.rOar.RM.UI'
  ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
    resolvedEnv: 'qa1'
  ${{ elseif eq(variables['Build.SourceBranchName'], 'development') }}:
    resolvedEnv: 'dev1'
  ${{ else }}:
    resolvedEnv: ${{ parameters.env }}
  webAppName: 'ui-${{parameters.businessName}}-${{parameters.applicationName}}-${{variables.resolvedEnv}}'
  group: rm-ui-${{ variables.resolvedEnv }}-environment-vars
  zipFile: '$(Pipeline.Workspace)/${{ variables.artifactName}}/${{ variables.appName }}.zip'
  containerRegistryName: 'crroarv2rm${{ variables.resolvedEnv }}'
  containerRegistry: '${{ variables.containerRegistryName }}.azurecr.io'
  prodSubscriptionName: 'SSC-rOar-v2-PRD-MG'
  imageRepository: 'ui/roarv2/rm-ui'
  ${{ if eq(variables.resolvedEnv, 'dev1') }}:
    subscription: 'svc-rOar-v2-DEV'
    NEXT_PUBLIC_BFF_URL: 'https://bff-roarv2-rm-dev1.azurewebsites.net'
    WF_SERVER_URL: 'https://roar.sb03.workfront.com'
  ${{ elseif eq(variables.resolvedEnv, 'qa1') }}:
    subscription: 'svc-rOar-v2-DEV'
    NEXT_PUBLIC_BFF_URL: 'https://bff-roarv2-rm-qa1.azurewebsites.net'
    WF_SERVER_URL: 'https://roar.sb03.workfront.com'
  ${{ elseif eq(variables.resolvedEnv, 'uat1') }}:
    subscription: 'SSC-rOar-v2-UAT-MG'
    NEXT_PUBLIC_BFF_URL: 'https://roar-uat-api.marcel.ai/rm'
    WF_SERVER_URL: 'https://roar.sb06.workfront.com'
  ${{ elseif eq(variables.resolvedEnv, 'prod1') }}:
    subscription: 'SSC-rOar-v2-PRD-MG' 

stages:
- stage: build
  jobs:
  - job: build
    displayName: 'Build'
    steps:
    - task: npmAuthenticate@0
      inputs:
        workingFile: '$(Build.SourcesDirectory)/.npmrc'
    - task: NodeTool@0
      inputs:
        versionSource: 'spec'
        versionSpec: '20.x'
        checkLatest: false
    - task: Npm@1
      displayName: 'npm install'
      inputs:
        command: 'install'
    - task: Npm@1
      displayName: 'npm run build'
      inputs:
        command: 'custom'
        customCommand: 'run build'
    - task: Npm@1
      displayName: 'Run Jest tests'
      condition: eq(variables['resolvedEnv'], 'dev1')
      inputs:
        command: 'custom'
        customCommand: 'run test -- --coverage'
    - task: PublishTestResults@2
      displayName: 'Publish Test Results'
      condition: eq(variables['resolvedEnv'], 'dev1')
      inputs:
        testResultsFiles: '**/test-results.xml'
        testRunTitle: 'Jest Tests'
    - task: PublishCodeCoverageResults@2
      displayName: 'Publish Code Coverage Results'
      condition: eq(variables['resolvedEnv'], 'dev1')
      inputs:
        codeCoverageTool: 'Cobertura'
        summaryFileLocation: '$(Build.SourcesDirectory)/coverage/cobertura-coverage.xml'
        reportDirectory: '$(Build.SourcesDirectory)/coverage'

    - task: SonarQubePrepare@7
      inputs:
        SonarQube: 'SonarQube-v2-RM-UI'
        scannerMode: 'cli'
        configMode: 'manual'
        cliProjectKey: 'rOarv2.RM-UI'
        cliProjectName: 'rOarv2-RM-UI'
        cliSources: '.'
        extraProperties: |
          sonar.javascript.lcov.reportPaths=$(Build.SourcesDirectory)/coverage/lcov.info
          sonar.exclusions=public/gridstack-all.js,public/gridstack.min.css,jest.config.ts,src/constants/sortOptions.ts,src/gql/client/localState/**,src/constants/*.ts,setupTests.ts,next.config.js,**/__mocks__/**,**/*.mock.ts,**/*.stories.ts,**/test.ts,**/polyfills.ts,**/mockServiceWorker.js,**/mocks/**,**/environments/**,**/main.ts,**/constants/**,**/test-*.ts,src/tests/**/*.ts,src/tests/**/*.tsx,src/tests/**/*.js,src/pages/api/auth/[...nextauth].ts,src/gql/client/apolloClientInit.ts,src/pages/_app.tsx,src/pages/_document.tsx,src/pages/index.tsx,src/middleware.ts,src/gql/client/**,src/components/Icons/**
    - task: SonarQubeAnalyze@7
      inputs:
        jdkversion: 'JAVA_HOME_17_X64'
    - task: SonarQubePublish@7
      inputs:
        pollingTimeoutSec: '300'

- stage: build_docker
  displayName: 'Build docker'
  dependsOn: build
  condition: succeeded()
  jobs:
  - job: build_docker
    displayName: 'Build Docker image'
    steps:
    - task: npmAuthenticate@0
      inputs:
        workingFile: '$(Build.SourcesDirectory)/.npmrc'
    - task: DockerInstaller@0
      displayName: 'Install Docker'
      inputs:
        dockerVersion: '17.09.0-ce'
    - task: Docker@2
      displayName: 'Build Docker Image'
      inputs:
        command: 'build'
        Dockerfile: '$(Build.SourcesDirectory)/Dockerfile'
        repository: '${{ variables.containerRegistry }}/${{ variables.imageRepository }}'
        buildContext: '$(Build.SourcesDirectory)'
        tags: |
          $(Build.BuildId)
          latest
        arguments: |
          --build-arg NEXT_PUBLIC_BFF_URL=${{ variables.NEXT_PUBLIC_BFF_URL }}
          --build-arg WF_SERVER_URL=${{ variables.WF_SERVER_URL }}
    - task: Docker@2
      condition: succeeded()
      displayName: 'Push Docker Image'
      inputs:
        containerRegistry: '${{ variables.containerRegistryName }}'
        repository: '${{ variables.imageRepository }}'
        command: 'push'
        Dockerfile: '$(Build.SourcesDirectory)/Dockerfile'
        tags: |
          $(Build.BuildId)
          latest       

- stage: deploy
  displayName: 'Deploy to ${{ variables.resolvedEnv }}'
  dependsOn: build_docker
  condition: succeeded()
  jobs:
  - deployment: deploy
    displayName: 'Deploy Web App to ${{ variables.resolvedEnv }}'
    environment: 'roarv2-rm-ui-${{ variables.resolvedEnv }}'
    strategy:
      runOnce:
        deploy:
          steps:
          - template: 'app-deploy.yml'
            parameters:
              appName: ${{ variables.webAppName }}
              azureSubscription: ${{ variables.subscription }}
              containerRegistry: ${{ variables.containerRegistry }}
              imageRepository: ${{ variables.imageRepository }}
              tag: $(Build.BuildId)