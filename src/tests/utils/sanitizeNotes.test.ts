import { sanitizeNotes, sanitizeNotesPreview } from '../../utils/sanitizeNotes'

describe('sanitizeNotes', () => {
  it('should return an empty array when input is empty', () => {
    const result = sanitizeNotes('')
    expect(result).toEqual([])
  })

  it('should replace <br> tags with newline characters and remove other HTML tags', () => {
    const input = 'Hello<br>World<br/>This is a <b>test</b>'
    const expectedOutput = ['Hello', 'World', 'This is a test']
    const result = sanitizeNotes(input)
    expect(result).toEqual(expectedOutput)
  })

  it('should handle input with no HTML tags correctly', () => {
    const input = 'Hello\nWorld\nThis is a test'
    const expectedOutput = ['Hello', 'World', 'This is a test']
    const result = sanitizeNotes(input)
    expect(result).toEqual(expectedOutput)
  })

  it('should handle input with only HTML tags correctly', () => {
    const input = '<b>Hello</b><br><i>World</i>'
    const expectedOutput = ['Hello', 'World']
    const result = sanitizeNotes(input)
    expect(result).toEqual(expectedOutput)
  })
})

describe('sanitizeNotesPreview', () => {
  it('should return an empty string when input is empty', () => {
    const result = sanitizeNotesPreview('')
    expect(result).toBe('')
  })

  it('should replace <br> tags with spaces and remove other HTML tags', () => {
    const input = 'Hello<br>World<br/>This is a <b>test</b>'
    const expectedOutput = 'Hello World This is a test'
    const result = sanitizeNotesPreview(input)
    expect(result).toBe(expectedOutput)
  })

  it('should handle input with no HTML tags correctly', () => {
    const input = 'Hello World This is a test'
    const expectedOutput = 'Hello World This is a test'
    const result = sanitizeNotesPreview(input)
    expect(result).toBe(expectedOutput)
  })

  it('should handle input with only HTML tags correctly', () => {
    const input = '<b>Hello</b><br><i>World</i>'
    const expectedOutput = 'Hello World'
    const result = sanitizeNotesPreview(input)
    expect(result).toBe(expectedOutput)
  })
})
