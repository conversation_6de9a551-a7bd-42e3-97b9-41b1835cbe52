import { render } from '@testing-library/react'
import { Apollo<PERSON><PERSON>, ApolloProvider, InMemoryCache } from '@apollo/client'
import { activeDatesVar, activeSortVar, numOfWeeksVar, userProfileVar } from '@gql/client/localState'
import { CalendarViewOptions } from '@constants/calendarViewOptions'
import { UserProfile } from '@constants/userProfile'
import { mockProjectsByProjectManagerQuery } from '../../__mocks__/projects-query.mock'
import { mockAssignmentQuery } from '../../__mocks__/assignments-query.mock'
import { mockCalendarQuery } from '../../__mocks__/calendar-query.mock'
import { mockPlaceHoldersQuery } from '../../__mocks__/placeholders-query.mock'
import { mockResourcesQuery } from '../../__mocks__/resources-query.mock'
import CalendarDrag from '@components/Calendar/CalendarDrag/CalendarDrag'
import { mockAssignment } from '../../__mocks__/assignments.mock'
import { type Resource } from '@typeDefs/Resource'
import CALENDAR_QUERY from '@gql/queries/calendar.query'

jest.mock('gridstack', () => ({
  // eslint-disable-next-line @typescript-eslint/naming-convention
  GridStack: {
    initAll: jest.fn()
  }
}))

jest.mock('gridstack', () => {
  const originalModule = jest.requireActual('gridstack')
  return {
    ...originalModule,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    GridStack: {
      ...originalModule.GridStack,
      initAll: jest.fn()
    }
  }
})

// eslint-disable-next-line @typescript-eslint/no-var-requires
const gridStackSpy = jest.spyOn(require('gridstack').GridStack, 'initAll')
gridStackSpy.mockImplementation(() => {
  console.log('GridStack initAll mocked implementation')
})

const mockResource: Resource = {
  id: 'resource1',
  name: 'John Doe',
  location: 'New York',
  position: 'Senior Developer',
  jobTitle: 'Software Engineer',
  profitCenter: 'IT Department',
  altairNumber: 'ALT456',
  totalCapacity: 160,
  minimumAgencyHoursPerDay: 8,
  workCode: 'DEV123',
  agencyName: 'PXP',
  agencyCode: '1112',
  timeOffDetails: [],
  holidays: [],
  requiresAssignApproval: true
}

let apolloClientInit: ApolloClient<unknown>

xdescribe('CalendarDrag', () => {
  beforeEach(() => {
    jest.spyOn(Storage.prototype, 'getItem').mockImplementation((key) => {
      switch (key) {
        case 'profile':
          return JSON.stringify(UserProfile.PROJECT_MANAGER)
        default:
          return JSON.stringify(UserProfile.PROJECT_MANAGER)
      }
    })

    apolloClientInit = new ApolloClient({
      cache: new InMemoryCache()
    })

    userProfileVar(UserProfile.PROJECT_MANAGER)
    activeDatesVar({ startDate: '2023-01-01', endDate: '2023-01-07' })
    activeSortVar({ field: 'name', order: 'asc', displayName: 'Sort A to Z' })
    numOfWeeksVar(CalendarViewOptions.TWO_WEEKS)
    apolloClientInit.writeQuery(mockProjectsByProjectManagerQuery)
    apolloClientInit.writeQuery(mockAssignmentQuery)
    apolloClientInit.writeQuery(mockCalendarQuery)
    apolloClientInit.writeQuery(mockPlaceHoldersQuery)
    apolloClientInit.writeQuery(mockResourcesQuery)
    apolloClientInit.writeQuery({
      query: CALENDAR_QUERY,
      variables: {
        params: activeDatesVar()
      },
      data: {
        getCalendarData: [
          {
            month: 'July',
            days: [
              {
                dayShortName: 'Mon',
                dayNumber: 15,
                date: '2024-07-15',
                isWeekend: false
              },
              {
                dayShortName: 'Tue',
                dayNumber: 16,
                date: '2024-07-16',
                isWeekend: false
              },
              {
                dayShortName: 'Wed',
                dayNumber: 17,
                date: '2024-07-17',
                isWeekend: false
              },
              {
                dayShortName: 'Thu',
                dayNumber: 18,
                date: '2024-07-18',
                isWeekend: false
              },
              {
                dayShortName: 'Fri',
                dayNumber: 19,
                date: '2024-07-19',
                isWeekend: false
              },
              {
                dayShortName: 'Sat',
                dayNumber: 20,
                date: '2024-07-20',
                isWeekend: true
              },
              {
                dayShortName: 'Sun',
                dayNumber: 21,
                date: '2024-07-21',
                isWeekend: true
              },
              {
                dayShortName: 'Mon',
                dayNumber: 22,
                date: '2024-07-22',
                isWeekend: false
              },
              {
                dayShortName: 'Tue',
                dayNumber: 23,
                date: '2024-07-23',
                isWeekend: false
              },
              {
                dayShortName: 'Wed',
                dayNumber: 24,
                date: '2024-07-24',
                isWeekend: false
              },
              {
                dayShortName: 'Thu',
                dayNumber: 25,
                date: '2024-07-25',
                isWeekend: false
              },
              {
                dayShortName: 'Fri',
                dayNumber: 26,
                date: '2024-07-26',
                isWeekend: false
              },
              {
                dayShortName: 'Sat',
                dayNumber: 27,
                date: '2024-07-27',
                isWeekend: true
              },
              {
                dayShortName: 'Sun',
                dayNumber: 28,
                date: '2024-07-28',
                isWeekend: true
              }
            ]
          }
        ]
      }
    })
  })

  test('renders without crashing', () => {
    render(
      <ApolloProvider client={apolloClientInit}>
        <CalendarDrag id="test-id" assignments={mockAssignment} resource={mockResource} type="resource" />
      </ApolloProvider>
    )
    // expect(screen.getByText('Loading calendar header...')).toBeInTheDocument()
  })
})
