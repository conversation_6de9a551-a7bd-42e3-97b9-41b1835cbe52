import { render, screen, fireEvent } from '@testing-library/react'
import ScrollOnIntersect from '@components/Calendar/ScrollOnIntersect/ScrollOnIntersect'
import { ScrollProvider } from '../../../context/scrollContext'

describe('ScrollOnIntersect', () => {
  it('should render without crashing', () => {
    render(
      <ScrollProvider>
        <ScrollOnIntersect direction="up" />
      </ScrollProvider>
    )
    const divElement = screen.getByRole('none')
    expect(divElement).toBeInTheDocument()
  })

  it('should scroll up when mouse enters the area', () => {
    window.scrollBy = jest.fn()
    render(
      <ScrollProvider>
        <ScrollOnIntersect direction="up" />
      </ScrollProvider>
    )
    const divElement = screen.getByRole('none')
    fireEvent.mouseMove(divElement)
    expect(window.scrollBy).toHaveBeenCalledWith(0, -8)
  })

  it('should scroll down when mouse enters the area', () => {
    window.scrollBy = jest.fn()
    render(
      <ScrollProvider>
        <ScrollOnIntersect direction="down" />
      </ScrollProvider>
    )
    const divElement = screen.getByRole('none')
    fireEvent.mouseMove(divElement)
    expect(window.scrollBy).toHaveBeenCalledWith(0, 8)
  })

  it('should apply correct styles based on props', () => {
    render(
      <ScrollProvider>
        <ScrollOnIntersect direction="up" zoneHeight={150} showPlaceholder={true} />
      </ScrollProvider>
    )
    const divElement = screen.getByRole('none')
    expect(divElement).toHaveStyle('height: 150px')
    expect(divElement).toHaveStyle('top: -100%')
  })
})
