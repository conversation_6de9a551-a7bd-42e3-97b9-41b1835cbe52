export interface AuditTrailEntry {
  id: string
  date: string
  time: string
  action: string
  details: {
    oldAssignment: AuditTrailAssignmentDetails
    newAssignment: AuditTrailAssignmentDetails
  }
}

export interface AuditTrailAssignmentDetails {
  externalId: string
  hoursPerDay: number
  startDate: string
  dueDate: string
  assignmentStatus: string
  resourceName: string
  resourceId: string
  taskName: string
  projectName: string
  isPlaceholder: boolean
}
