import VisibilityIcon from '@components/Icons/VisibilityIcon/VisibilityIcon'
import { Box, Chip, palette } from 'lion-ui'
import { type PlaceholdersSettingsBtnProps } from './PlaceholdersSettingsBtn.props'

const PlaceholdersSettingsBtn: React.FC<PlaceholdersSettingsBtnProps> = ({ toggleExpandPlaceholders }) => {
  return (
    <Box>
      <Chip
        sx={{
          margin: '0.25rem',
          padding: '1.2rem 0.5rem',
          fontSize: '0.85rem',
          fontStyle: 'normal',
          fontWeight: '400',
          lineHeight: '1.25rem',
          borderRadius: '2rem',
          backgroundColor: palette['50-Accent-2'].main
        }}
        label="Cross agency placeholders search."
        icon={<VisibilityIcon />}
        onClick={toggleExpandPlaceholders}
      />
    </Box>
  )
}

export default PlaceholdersSettingsBtn
