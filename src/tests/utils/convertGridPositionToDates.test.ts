import convertGridPositionToDates from '@utils/convertGridPositionToDates'
import { activeDatesVar } from '@gql/client/localState'

// Mock the activeDatesVar function
jest.mock('@gql/client/localState', () => ({
  activeDatesVar: jest.fn()
}))

describe('convertGridPositionToDates', () => {
  beforeEach(() => {
    // Set a fixed start date for testing
    ;(activeDatesVar as unknown as jest.Mock).mockReturnValue({ startDate: '2023-01-01' })
  })

  it('should convert grid position to correct dates', () => {
    const result = convertGridPositionToDates(0, 1)
    expect(result).toEqual({ startDate: '2023-01-01', endDate: '2023-01-01' })
  })

  it('should handle width greater than 1', () => {
    const result = convertGridPositionToDates(0, 3)
    expect(result).toEqual({ startDate: '2023-01-01', endDate: '2023-01-03' })
  })

  it('should handle xPosition greater than 0', () => {
    const result = convertGridPositionToDates(2, 1)
    expect(result).toEqual({ startDate: '2023-01-03', endDate: '2023-01-03' })
  })

  it('should handle both xPosition and width greater than 1', () => {
    const result = convertGridPositionToDates(2, 3)
    expect(result).toEqual({ startDate: '2023-01-03', endDate: '2023-01-05' })
  })

  it('should handle negative xPosition', () => {
    const result = convertGridPositionToDates(-1, 1)
    expect(result).toEqual({ startDate: '2022-12-31', endDate: '2022-12-31' })
  })

  it('should handle zero width', () => {
    const result = convertGridPositionToDates(0, 0)
    expect(result).toEqual({ startDate: '2023-01-01', endDate: '2022-12-31' })
  })
})
