// React imports
import { type FC, memo } from 'react'

// Type imports
import { type CalendarHeaderProps } from './CalendarHeader.props'
import type { CalendarData, CalendarDayData } from '@typeDefs/Calendar'

// UI component imports
import { Box } from 'lion-ui'
import CalendarDay from '@components/Calendar/CalendarDay/CalendarDay'

const CalendarHeader: FC<CalendarHeaderProps> = ({ calendarData }) => {
  return (
    <Box
      sx={{
        fontSize: '0.75rem',
        display: 'flex'
      }}
    >
      {calendarData?.map((calendarMonth: CalendarData, parentIndex: number) =>
        calendarMonth.days.map((day: CalendarDayData, index: number) => (
          <CalendarDay
            key={day.date}
            day={day}
            index={index}
            month={calendarMonth.month}
            isNewMonth={parentIndex > 0}
          />
        ))
      )}
    </Box>
  )
}

export default memo(CalendarHeader)
