import {
  employeeCodeVar,
  businessRolesVar,
  businessRoleVar,
  usernameVar,
  wflionLoginIDVar,
  userInfoVar
} from '@gql/client/localState'

/**
 * Extracts employeeCode from URL query parameters and hijacks the activeUser.altairNumber
 * This ensures ALL GraphQL queries automatically use the URL employeeCode without individual modifications
 * @param url - The URL to parse (optional, defaults to current window location)
 */
export const initializeEmployeeCodeFromUrl = (url?: string): void => {
  if (typeof window === 'undefined') return

  try {
    const targetUrl = url ?? window.location.href
    const urlObj = new URL(targetUrl)
    const employeeCode = urlObj.searchParams.get('employeeCode')

    if (employeeCode) {
      console.log('Employee code found in URL parameters:', employeeCode)
      employeeCodeVar(employeeCode)

      // Hijack the activeUser.altairNumber to use employeeCode from URL
      // This makes ALL queries automatically use the URL parameter
      const currentUser = userInfoVar()
      if (currentUser) {
        userInfoVar({
          ...currentUser,
          altairNumber: employeeCode
        })
        console.log('Hijacked activeUser.altairNumber with employeeCode:', employeeCode)
      }
    }
  } catch (error) {
    console.warn('Failed to parse URL for employeeCode:', error)
  }
}

export const addAuth = (payload: {
  employeeCode?: string
  businessRole: string | string[]
  username: string
  forceEmployeeCode?: boolean
}): void => {
  const currentEmployeeCode = employeeCodeVar()
  if (!currentEmployeeCode || payload.forceEmployeeCode) {
    if (payload.employeeCode) {
      employeeCodeVar(payload.employeeCode)
    }
  }

  const roles = Array.isArray(payload.businessRole) ? payload.businessRole : [payload.businessRole].filter(Boolean)
  businessRolesVar(roles)

  usernameVar(payload.username)
}

export const addBusinessRole = (payload: { businessRole: string }): void => {
  businessRoleVar(payload.businessRole)
}

export const addEmployeeCode = (payload: { employeeCode: string }): void => {
  employeeCodeVar(payload.employeeCode)
}

export const addWflionLoginID = (payload: { wflionLoginID: string }): void => {
  wflionLoginIDVar(payload.wflionLoginID)
}

export const getAuthState = (): {
  employeeCode: string
  businessRoles: string[]
  businessRole: string
  username: string
  wflionLoginID: string
} => ({
  employeeCode: employeeCodeVar(),
  businessRoles: businessRolesVar(),
  businessRole: businessRoleVar(),
  username: usernameVar(),
  wflionLoginID: wflionLoginIDVar()
})
