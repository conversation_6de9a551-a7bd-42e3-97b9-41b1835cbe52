import '@testing-library/jest-dom'
import { loadEnvConfig } from '@next/env'
import { configure } from '@testing-library/react'

configure({
  testIdAttribute: 'data-testid'
})

loadEnvConfig(__dirname, true, { info: () => null, error: () => null })

console.error = jest.fn()

// Mock SVGs
jest.mock('*.svg', () => ({
  esModule: true,
  default: {
    src: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOSAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIi8+',
    width: 19,
    height: 18
  }
}))

// Mock auth module
jest.mock('@auth/msalUtil', () => ({
  getAccessToken: jest.fn().mockResolvedValue('mock-token'),
  msalInstance: {
    getAllAccounts: jest.fn().mockReturnValue([{ accountIdentifier: 'mock-account' }]),
    setActiveAccount: jest.fn(),
    loginRedirect: jest.fn(),
    loginPopup: jest.fn(),
    logout: jest.fn()
  }
}))

jest.mock('gridstack', () => ({
  gridStack: {
    initAll: () => {},
    init: () => {},
    destroy: () => {}
  },
  ddGridStack: class DDGridStack {
    init(): void {}
    destroy(): void {}
  }
}))

// Mock the gridstack-engine
jest.mock('gridstack/dist/gridstack-engine', () => ({
  gridStackEngine: () => ({})
}))
