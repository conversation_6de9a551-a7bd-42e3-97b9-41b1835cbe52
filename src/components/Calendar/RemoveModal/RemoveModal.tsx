import type React from 'react'
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  CircularProgress,
  Box,
  Divider,
  palette,
  Typography
} from 'lion-ui'

interface RemoveRemoveModalProps {
  open: boolean
  message: string
  closeText: string
  removeText: string
  onRemove: () => void
  onClose: () => void
  isLoading?: boolean
}

const RemoveModal: React.FC<RemoveRemoveModalProps> = ({
  open,
  message,
  closeText,
  removeText,
  onRemove,
  onClose,
  isLoading
}) => {
  const handleRemove = () => {
    onRemove()
  }

  const handleClose = () => {
    onClose()
  }

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      aria-labelledby="remove-allocation-dialog-title"
      aria-describedby="remove-allocation-dialog-description"
      maxWidth="xs"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '0.5rem',
          boxShadow: '0px 4px 24px 0px rgba(0, 0, 0, 0.10)'
        }
      }}
    >
      <DialogContent>
        <DialogContentText id="remove-allocation-dialog-description">
          <Typography variant="BodyMRegular">{message}</Typography>
        </DialogContentText>
      </DialogContent>
      <Divider />
      <DialogActions sx={{ justifyContent: 'center', gap: '1rem', padding: '1.25rem 1.5rem' }}>
        <Button
          sx={{
            borderColor: palette.secondary.main,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'flex-start'
          }}
          variant="outlined"
          onClick={(e) => {
            e.stopPropagation()
            handleClose()
          }}
          data-testid="close-remove-allocation-button"
        >
          {closeText}
        </Button>
        <Box sx={{ position: 'relative' }}>
          <Button
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'flex-start'
            }}
            onClick={(e) => {
              e.stopPropagation()
              handleRemove()
            }}
            color="error"
            disabled={isLoading}
            data-testid="confirm-remove-allocation-button"
          >
            {removeText}
          </Button>
          {isLoading && (
            <CircularProgress
              size={24}
              sx={{
                color: palette.primary.main,
                position: 'absolute',
                top: '50%',
                left: '50%',
                marginTop: '-12px',
                marginLeft: '-12px'
              }}
            />
          )}
        </Box>
      </DialogActions>
    </Dialog>
  )
}

export default RemoveModal
