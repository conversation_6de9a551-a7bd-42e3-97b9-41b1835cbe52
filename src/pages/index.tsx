import { useEffect, useState } from 'react'

// Apollo import
import { useReactiveVar } from '@apollo/client'
import {
  showAllocationDetailsFormVar,
  userProfileVar,
  showAuditTrailVar,
  resourceTypeVar
} from '@gql/client/localState'
import { showSplitAssignmentFormVar } from '@gql/client/localState/SplitAssignmentForm'

// Component imports
import CalendarBody from '@components/Calendar/CalendarBody/CalendarBody'
import AllocationDetailsForm from '@components/Forms/AllocationDetailsForm/AllocationDetailsForm'
import AllocationRequestForm from '@components/Forms/AllocationRequestForm/AllocationRequestForm'
import SplitAssignmentForm from '@components/Forms/SplitAssignmentForm/SplitAssignmentForm'

// UI library imports
import { Drawer } from 'lion-ui'

// Constants imports
import { AssignmentStatus } from '@constants/taskStatus'
import { UserProfile } from '@constants/userProfile'
import AuditTrailContent from '@components/AuditTrail/AuditTrailContent/AuditTrailContent'
import { getAccessToken } from '@auth/msalUtil'

const Index: React.FunctionComponent = () => {
  const [token, setToken] = useState<string | null>(null)

  const userProfile = useReactiveVar(userProfileVar)
  const showAllocationDetailsForm = useReactiveVar(showAllocationDetailsFormVar)
  const openAuditTrailVar = useReactiveVar(showAuditTrailVar)
  const showSplitAssignmentForm = useReactiveVar(showSplitAssignmentFormVar)
  const resourceType = useReactiveVar(resourceTypeVar)

  useEffect(() => {
    const fetchToken = async () => {
      const accessToken = await getAccessToken()
      if (accessToken) {
        setToken(accessToken)
      } else {
        setToken(null)
      }
    }

    void fetchToken()
  }, [])

  if (token === null) {
    return <div>Checking authentication...</div>
  }

  if (!token) {
    return <div>You are not signed in. Redirecting...</div>
  }

  return (
    <>
      <CalendarBody key={userProfile} />
      {showAllocationDetailsForm &&
        (userProfile !== UserProfile.LIGHT_USER &&
        showAllocationDetailsForm.assignmentState?.toLowerCase() === AssignmentStatus.REQUESTED &&
        resourceType !== 'placeholder' ? (
          <AllocationRequestForm />
        ) : (
          <AllocationDetailsForm open={true} />
        ))}
      {showSplitAssignmentForm && <SplitAssignmentForm />}
      {openAuditTrailVar && userProfile !== UserProfile.LIGHT_USER && (
        <Drawer open={openAuditTrailVar} anchor="right" variant="temporary" onClose={() => showAuditTrailVar(false)}>
          <AuditTrailContent onClose={() => showAuditTrailVar(false)} />
        </Drawer>
      )}
    </>
  )
}

export default Index
