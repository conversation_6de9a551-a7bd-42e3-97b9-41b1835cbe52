import { gplError } from '@utils/gplError'
import { type ApolloError } from '@apollo/client'
import { type ErrorNetwork } from '@typeDefs/common/ErrorNetwork'

describe('gplError', () => {
  let consoleErrorSpy: jest.SpyInstance

  beforeEach(() => {
    consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    consoleErrorSpy.mockRestore()
  })

  it('should update errorState message when networkError is present', () => {
    const errorState: ApolloError = {
      message: '',
      name: '',
      graphQLErrors: [],
      protocolErrors: [],
      clientErrors: [],
      networkError: null,
      cause: null,
      extraInfo: undefined
    }
    const error: ApolloError = {
      networkError: {
        result: {
          errors: [{ message: 'Network error 1' }, { message: 'Network error 2' }] as ErrorNetwork[]
        }
      }
    } as unknown as ApolloError

    gplError(error, errorState)
    expect(errorState.message).toBe('Network error 1, Network error 2')
  })

  it('should log error message when networkError is not present', () => {
    const errorState = undefined
    const error: ApolloError = {
      message: 'GraphQL error',
      name: '',
      graphQLErrors: [],
      protocolErrors: [],
      clientErrors: [],
      networkError: null,
      cause: null,
      extraInfo: undefined
    }

    gplError(error, errorState)
    expect(consoleErrorSpy).toHaveBeenCalledWith('GraphQL error:', 'GraphQL error')
  })
})
