/* eslint-disable @typescript-eslint/no-unsafe-argument */
import { render, fireEvent, screen } from '@testing-library/react'
import FilterList from '@components/Header/FilterList/FilterList'
import type { FilterListProps } from '@components/Header/FilterList/FilterList.props'
import { activeFiltersVar } from '@gql/client/localState'
import { MockedProvider } from '@apollo/client/testing'
import { useReactiveVar } from '@apollo/client'

jest.mock('@gql/client/localState', () => ({
  activeFiltersVar: jest.fn((params) => jest.fn())
}))

jest.mock('@apollo/client', () => ({
  ...jest.requireActual('@apollo/client'),
  useReactiveVar: jest.fn()
}))

describe('FilterList Component', () => {
  const mockItems: FilterListProps['items'] = [
    { id: '1', name: 'Item 1', category: 'testCategory', icon: undefined },
    { id: '2', name: 'Item 2', category: 'testCategory', icon: undefined },
    { id: '3', name: 'Item 3', category: 'testCategory', icon: undefined }
  ]
  const mockCategory = 'testCategory'
  const mockActiveFilters = { [mockCategory]: [] }

  beforeEach(() => {
    ;(useReactiveVar as jest.Mock).mockReturnValue(mockActiveFilters)
  })

  it('should display "No items to show" when items array is empty', () => {
    const { getByText } = render(
      <MockedProvider>
        <FilterList items={[]} category={mockCategory} />
      </MockedProvider>
    )
    expect(getByText('No items to show')).toBeInTheDocument()
  })

  it('should display the correct item names', () => {
    const { getByText } = render(
      <MockedProvider>
        <FilterList items={mockItems} category={mockCategory} />
      </MockedProvider>
    )
    mockItems.forEach((item) => {
      expect(getByText(item.name ?? '')).toBeInTheDocument()
    })
  })

  it('should check  items correctly', () => {
    const { getByLabelText } = render(
      <MockedProvider>
        <FilterList items={mockItems} category={mockCategory} />
      </MockedProvider>
    )
    const checkbox = getByLabelText(`${mockItems[0].name}`) as HTMLInputElement

    // Check item
    fireEvent.click(checkbox)
    expect(activeFiltersVar).toHaveBeenCalledWith({
      ...mockActiveFilters,
      [mockCategory]: [mockItems[0]]
    })
  })

  it('should change on item button click', async () => {
    ;(activeFiltersVar as unknown as jest.Mock).mockImplementation((params) => ({
      ...mockActiveFilters,
      [mockCategory]: [mockItems[0]]
    }))

    render(<FilterList items={mockItems} category={mockCategory} />)
    const button = screen.getAllByRole('button')[0]

    // Check item
    fireEvent.click(button)
    expect(activeFiltersVar).toHaveBeenCalledWith({
      ...mockActiveFilters,
      [mockCategory]: [mockItems[0]]
    })
  })

  it('should uncheck on item button click', async () => {
    ;(activeFiltersVar as unknown as jest.Mock).mockImplementation((params) => ({
      ...mockActiveFilters,
      [mockCategory]: [mockItems[0]]
    }))
    ;(useReactiveVar as unknown as jest.Mock).mockImplementation((params) => ({
      ...mockActiveFilters,
      [mockCategory]: [mockItems[0]]
    }))

    render(<FilterList items={mockItems} category={mockCategory} />)
    const button = screen.getAllByRole('button')[0]

    // Check item
    fireEvent.click(button)
    expect(activeFiltersVar).toHaveBeenCalledWith({
      ...mockActiveFilters,
      [mockCategory]: []
    })
  })
})
