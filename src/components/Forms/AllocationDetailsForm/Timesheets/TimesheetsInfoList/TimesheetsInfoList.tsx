import { Box, Divider } from 'lion-ui'
import { type TimesheetsInfoListProps } from './TimesheetsInfoList.props'
import TimesheetsInfo from '../TimesheetsInfo/TimesheetsInfo'

const TimesheetsInfoList = ({ timesheetsList }: TimesheetsInfoListProps) => {
  return (
    <Box>
      {timesheetsList.map((timesheet, index) => (
        <>
          <Box key={timesheet.timesheetDate} className="timesheet-info-item">
            <TimesheetsInfo timesheet={timesheet} />
            {index !== timesheetsList.length - 1 && <Divider />}
          </Box>
        </>
      ))}
    </Box>
  )
}

export default TimesheetsInfoList
