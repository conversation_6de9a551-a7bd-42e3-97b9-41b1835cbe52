import { render, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import { MockedProvider } from '@apollo/client/testing'
import { useReactiveVar } from '@apollo/client'
import { activeFiltersVar } from '@gql/client/localState'
import { SelectAllDropdown } from '@components/Header/FilterList/SelectAllDropdown/SelectAllDropdown'
import type { SelectAllDropdownProps } from '@components/Header/FilterList/SelectAllDropdown/SelectAllDropdown.props'

jest.mock('@gql/client/localState', () => ({
  activeFiltersVar: jest.fn((params) => jest.fn())
}))

jest.mock('@apollo/client', () => ({
  ...jest.requireActual('@apollo/client'),
  useReactiveVar: jest.fn()
}))

describe('SelectAllDropdown Component', () => {
  const mockItems: SelectAllDropdownProps['items'] = [
    { id: '1', name: 'Item 1', category: 'testCategory', icon: undefined },
    { id: '2', name: 'Item 2', category: 'testCategory', icon: undefined },
    { id: '3', name: 'Item 3', category: 'testCategory', icon: undefined }
  ]
  const mockCategory = 'testCategory'
  const mockActiveFilters = { [mockCategory]: [] }

  beforeEach(() => {
    ;(useReactiveVar as jest.Mock).mockReturnValue(mockActiveFilters)
  })

  it('should display the correct number of selected items', () => {
    const { getByText } = render(
      <MockedProvider>
        <SelectAllDropdown items={mockItems} category={mockCategory} />
      </MockedProvider>
    )
    expect(getByText('Select All')).toBeInTheDocument()
  })

  it('should select all items when "Select all" is clicked', () => {
    const { getByText } = render(
      <MockedProvider>
        <SelectAllDropdown items={mockItems} category={mockCategory} />
      </MockedProvider>
    )
    fireEvent.click(getByText('Select All'))
    fireEvent.click(getByText('Select all'))
    expect(activeFiltersVar).toHaveBeenCalledWith({
      ...mockActiveFilters,
      [mockCategory]: mockItems
    })
  })

  it('should unselect all items when "Unselect all" is clicked', () => {
    const { getByText } = render(
      <MockedProvider>
        <SelectAllDropdown items={mockItems} category={mockCategory} />
      </MockedProvider>
    )
    fireEvent.click(getByText('Select All'))
    fireEvent.click(getByText('Unselect all'))
    expect(activeFiltersVar).toHaveBeenCalledWith({
      ...mockActiveFilters,
      [mockCategory]: []
    })
  })

  it('should toggle checkbox state correctly', () => {
    const { getByRole } = render(
      <MockedProvider>
        <SelectAllDropdown items={mockItems} category={mockCategory} />
      </MockedProvider>
    )
    const checkbox = getByRole('checkbox') as HTMLInputElement

    // Check all items
    fireEvent.click(checkbox)
    expect(checkbox).toBeChecked()
    expect(activeFiltersVar).toHaveBeenCalledWith({
      ...mockActiveFilters,
      [mockCategory]: mockItems
    })

    // Uncheck all items
    fireEvent.click(checkbox)
    expect(checkbox).not.toBeChecked()
    expect(activeFiltersVar).toHaveBeenCalledWith({
      ...mockActiveFilters,
      [mockCategory]: []
    })
  })
})
