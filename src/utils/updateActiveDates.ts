// Utils imports
import getDateRange from '@utils/getDateRange'
import dayjs from 'dayjs'

// LocalState imports
import { activeDatesVar, numOfWeeksVar } from '@gql/client/localState'
import { CalendarViewOptions } from '@constants/calendarViewOptions'

const DATE_FORMAT = 'YYYY-MM-DD'

const getNextOrPreviousDay = (date: string, direction: 'next' | 'prev'): string => {
  return direction === 'next'
    ? dayjs(date).add(1, 'day').format(DATE_FORMAT)
    : dayjs(date).subtract(1, 'day').format(DATE_FORMAT)
}

const updateActiveDatesRange = (startDate: string, numOfWeeks: number): void => {
  const { startDate: newStartDate, endDate } = getDateRange(numOfWeeks, startDate)
  activeDatesVar({ startDate: newStartDate, endDate })
}

const updateActiveDates = (direction: 'next' | 'prev' | '', numOfWeeks?: number): void => {
  const activeDate = activeDatesVar().startDate
  const numOfWeeksToMove = numOfWeeks ?? 0

  if (numOfWeeksVar() === CalendarViewOptions.ONE_DAY) {
    if (direction === '') {
      const today = dayjs().format(DATE_FORMAT)
      activeDatesVar({ startDate: today, endDate: today })
      return
    }
    const newDate = getNextOrPreviousDay(activeDate, direction)
    activeDatesVar({ startDate: newDate, endDate: newDate })
    return
  }

  if (direction === 'next') {
    const newStartDate = dayjs(activeDate).add(numOfWeeksToMove, 'week').format(DATE_FORMAT)
    updateActiveDatesRange(newStartDate, numOfWeeksVar())
  } else if (direction === 'prev') {
    const newStartDate = dayjs(activeDate).subtract(numOfWeeksToMove, 'week').format(DATE_FORMAT)
    updateActiveDatesRange(newStartDate, numOfWeeksVar())
  } else {
    const today = dayjs().format(DATE_FORMAT)
    updateActiveDatesRange(today, numOfWeeksVar())
  }
}

export default updateActiveDates
