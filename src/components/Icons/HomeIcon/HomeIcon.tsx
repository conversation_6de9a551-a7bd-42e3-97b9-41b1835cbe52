import { type IconProps } from '../Icon.props'

const HomeIcon = ({ height, width }: IconProps) => {
  return (
    <svg width={height} height={width} viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="home">
        <path
          id="Vector"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6.487 2.69411L4 5.18111V9.5335H9V5.20711L6.487 2.69411ZM3 6.18111L2.35355 6.82755C2.15829 7.02282 1.84171 7.02282 1.64645 6.82755C1.45118 6.63229 1.45118 6.31571 1.64645 6.12045L6.12045 1.64645C6.22138 1.54551 6.35474 1.49675 6.487 1.50017C6.61926 1.49675 6.75262 1.54551 6.85355 1.64645L11.3276 6.12045C11.5228 6.31571 11.5228 6.63229 11.3276 6.82755C11.1323 7.02282 10.8157 7.02282 10.6204 6.82755L10 6.20711V10.0335C10 10.3096 9.77614 10.5335 9.5 10.5335H3.5C3.22386 10.5335 3 10.3096 3 10.0335V6.18111Z"
          fill="currentColor"
        />
      </g>
    </svg>
  )
}

export default HomeIcon
