import { gql } from '@apollo/client'

const placeholderFields = `
  hasNextPage
  hasPreviousPage
  totalCount
  totalPages
  pageNumber
  items {
    id
    name
    minimumAgencyHoursPerDay
  }
`

const PLACEHOLDERS_QUERY = gql`
  query Placeholders($params: PlaceholdersParams) {
    placeholders(params: $params) {
      ${placeholderFields}
    }
  }
`
const PLACEHOLDERS_FILTERS_QUERY = gql`
  query PlaceholderFilters($params: PlaceholderFiltersParams) {
    placeholderFilters(params: $params) {
      ${placeholderFields}
    }
  }
`

const PLACEHOLDERS_BY_USER_ID_QUERY = gql`
  query GetPlaceholderFilterValuesByUserId($params: PlaceholderFilterValueParams) {
    getPlaceholderFilterValuesByUserId(params: $params) {
      businessUnit {
        label
      }
      costCenter {
        label
      }
      location {
        label
      }
      jobrole {
        label
      }
    }
  }
`

const PLACEHOLDERS_BY_WORK_CODE = gql`
  query GetPlaceholderByWorkCode($workCode: String!) {
    getPlaceholderByWorkCode(workCode: $workCode) {
      workCode
      workCodeName
    }
  }
`

const PLACEHOLDERS_AGENCY_QUERY = gql`
  query GetOrgStructureByPlaceholderAssignments($params: GetOrgStructureByPlaceholderAssignmentsParams) {
    getOrgStructureByPlaceholderAssignments(params: $params) {
      agencies {
        agencyCode
        agencyName
      }
      costCenters {
        costCenterCode
        costCenterName
      }
      locations
    }
  }
`

export {
  PLACEHOLDERS_QUERY,
  PLACEHOLDERS_FILTERS_QUERY,
  PLACEHOLDERS_BY_USER_ID_QUERY,
  PLACEHOLDERS_BY_WORK_CODE,
  PLACEHOLDERS_AGENCY_QUERY
}
