/* eslint-disable @typescript-eslint/naming-convention */

// Mock window.crypto for MSAL
if (typeof window !== 'undefined') {
  window.crypto = {
    subtle: {
      decrypt: jest.fn(),
      deriveBits: jest.fn(),
      deriveKey: jest.fn(),
      digest: jest.fn(),
      encrypt: jest.fn(),
      generateKey: jest.fn(),
      sign: jest.fn(),
      verify: jest.fn(),
      wrapKey: jest.fn(),
      unwrapKey: jest.fn(),
      exportKey: jest.fn(),
      importKey: jest.fn()
    }
  } as unknown as Crypto
}

// Mock fetch for MSAL graph API calls
global.fetch = jest.fn(async () => ({
  json: async () => ({
    id: 'mock-user-id',
    displayName: 'Mock User',
    mail: '<EMAIL>'
  })
})) as jest.Mock

// Mock window.location for environment checks
Object.defineProperty(window, 'location', {
  value: {
    hostname: 'localhost',
    pathname: '/',
    search: '',
    hash: '',
    href: 'http://localhost:3000/',
    protocol: 'http:',
    host: 'localhost:3000',
    origin: 'http://localhost:3000'
  }
})

// Ensure file is treated as a module for --isolatedModules
export {}
