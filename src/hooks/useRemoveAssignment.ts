import { useMutation } from '@apollo/client'
import { showAlertVar } from '@gql/client/localState'
import { DELETE_ASSIGNMENT_MUTATION } from '@gql/mutations/assignment.mutation'
import { gplError } from '@utils/gplError'

const useRemoveAssignment = (
  assignmentId: string,
  userLoggedInExternalId: string,
  onCompleted: () => void
): { handleRemoveAllocation: () => Promise<void>; loadingDeleteAssignment: boolean } => {
  const [deleteAssignment, { loading: loadingDeleteAssignment }] = useMutation(DELETE_ASSIGNMENT_MUTATION, {
    update: (cache, { data: { deleteAssignmentById } }) => {
      if (deleteAssignmentById) {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        cache.evict({ id: cache.identify({ __typename: 'Assignment', id: assignmentId }) })
        cache.gc()
      }
    },
    onCompleted: (data) => {
      if (data.deleteAssignmentById) {
        onCompleted()
      }
    },
    onError: (error) => {
      showAlertVar({ message: error.message, show: true })
      gplError(error, error)
    }
  })

  const handleRemoveAllocation = async (): Promise<void> => {
    await deleteAssignment({ variables: { assignmentId, userLoggedInExternalId } })
  }

  return { handleRemoveAllocation, loadingDeleteAssignment }
}

export default useRemoveAssignment
