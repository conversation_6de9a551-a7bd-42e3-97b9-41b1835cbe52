import { activeDatesVar, resourcesIdsVar, placeholdersIdsVar, userInfoVar } from '@gql/client/localState'
import { type AssignmentsQueryParams } from '@typeDefs/AssignmentsQueryParams'

export function getAssignmentsQueryParams(): AssignmentsQueryParams {
  const activeUser = userInfoVar()

  return {
    startDate: activeDatesVar().startDate,
    endDate: activeDatesVar().endDate,
    workCode: activeUser?.altairNumber,
    userIds: [...resourcesIdsVar(), ...placeholdersIdsVar()],
    userLoggedInExternalId: activeUser?.altairNumber,
    isExpandedPlaceholderApplied: false
  }
}
