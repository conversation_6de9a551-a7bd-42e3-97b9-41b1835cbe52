// React imports
import { useCallback, memo, useState, useEffect } from 'react'

import {
  Autocomplete,
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  Divider,
  FormLabel,
  LionDateRangePicker,
  OutlinedInput,
  palette,
  TextField,
  Typography,
  CircularProgress,
  LinearProgress
} from 'lion-ui'

// GQL imports
import { useMutation, useQuery, useReactiveVar, useLazyQuery } from '@apollo/client'
import { GET_RESOURCE_AND_PLACEHOLDER } from '@gql/queries/searchResourcePlaceholder'
import { ASSIGNMENTS_QUERY } from '@gql/queries/assignments.query'
import {
  showAllocationDetailsFormVar,
  renderKeyVar,
  userProfileVar,
  userInfoVar,
  showAlertVar,
  generalLoadingStateVar,
  resourceTypeVar
} from '@gql/client/localState'
import TIMESHEETS_FOR_RESOURCE_QUERY from '@gql/queries/Timesheets.query'

// Components imports
import FormHeader from '@components/Forms/Common/FormHeader/FormHeader'
import SeeDetails from '@components/Forms/Common/SeeDetails/SeeDetails'
import FormTabs from './FormTabs/FormTabs'
import SimpleArrowIcon from '@components/Icons/SimpleArrowIcon/SimpleArrowIcon'
import RemovePopup from '@components/Calendar/RemovePopup/RemovePopup'

// Type imports
import { type AllocationDetailsFormProps } from './AllocationDetailsForm.props'
import { type AllocationDetailsFormErrors, type AllocationDetailsFormData } from '@typeDefs/Forms'
import { type SearchResourcePlaceholder } from '@typeDefs/SearchResourcePlaceholder'

// Utils imports
import { gplError } from '@utils/gplError'
import timezone from 'dayjs/plugin/timezone'
import {
  filterAndMapNonWorkingDates,
  generateWarningMessage,
  getWeekendDates,
  handleDateChange,
  handleHoursChange,
  mergeDistinctNonWorkingDates
} from '../Common/Handlers/FormHandlers'
import { getTileStatus, getAssignmentStatus } from '@components/Calendar/CalendarDragItem/CalendarDragItem.style'
import { UPDATE_ASSIGNMENT_MUTATION } from '@gql/mutations/assignment.mutation'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { UserProfile } from '@constants/userProfile'
import DeleteIcon from '@components/Icons/DeleteIcon/DeleteIcon'
import useRemoveAssignment from '@hooks/useRemoveAssignment'
import PlaceholderAdditionalDetails from '../Common/PlaceholderAdditionalDetails/PlaceholderAdditionalDetails'
import ResourceAdditionalDetails from '../Common/ResourceAdditionalDetails/ResourceAdditionalDetails'
import { type ResourceHolidayAndTimeOffDetails, type ResourceOrgStructure } from '@typeDefs/Resource'
import { type UpdateAssignmentParams } from '@typeDefs/Assignments'
import {
  HOLIDAYS_AND_TIMEOFFDETAILS_QUERY,
  RESOURCE_ORG_STRUCTURE_QUERY,
  ORG_STRUCTURE_AGENCY_COST_CENTER_QUERY
} from '@gql/queries/resources.query'
import { PROJECTS_BY_PROJECT_ID_QUERY } from '@gql/queries/projects.query'
import { NonWorkingDatesTypes } from '@constants/nonWorkingDatesTypes'
import { type Timesheet } from '@typeDefs/Timesheets'
import TimesheetsInfoCta from './Timesheets/TimesheetInfoCta/TimesheetsInfoCta'
import TimesheetsInfoList from './Timesheets/TimesheetsInfoList/TimesheetsInfoList'
import getLocaleDateFormat from '@utils/getLocaleDateFormat'
import { TaskStates } from '@constants/taskStatus'
dayjs.extend(utc)
dayjs.extend(timezone)

const AllocationDetailsForm = ({ open }: AllocationDetailsFormProps) => {
  const userProfile = useReactiveVar(userProfileVar)
  const activeUser = useReactiveVar(userInfoVar)
  const userInfo = useReactiveVar(userInfoVar)
  const isLightUser = userProfile === UserProfile.LIGHT_USER
  const assignment = useReactiveVar(showAllocationDetailsFormVar)

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [fetchProjectDetails, { error: projectOrgError }] = useLazyQuery(PROJECTS_BY_PROJECT_ID_QUERY, {
    onError: (error) => {
      gplError(error, projectOrgError)
    }
  })

  const [fetchOrgStructure, { error: orgStructureError }] = useLazyQuery(ORG_STRUCTURE_AGENCY_COST_CENTER_QUERY, {
    onError: (error) => {
      gplError(error, orgStructureError)
    }
  })

  const isFormDisabled =
    assignment?.assignmentState?.toLowerCase() !== 'rejected' &&
    (assignment?.taskState?.toLowerCase() === 'external' ||
      assignment?.taskState?.toLowerCase() === 'complete' ||
      assignment?.blockedByTimesheet)

  const externalLink = process.env.WF_SERVER_URL
  const [openNotesView, setOpenNotesView] = useState(false)
  const [openTimesheetsView, setOpenTimesheetsView] = useState(false)
  const [searchTermName, setSearchTermName] = useState('')
  const resourceType = useReactiveVar(resourceTypeVar)
  const initialFormState: AllocationDetailsFormData = {
    hoursPerDay: assignment?.hoursPerDay ?? 0,
    duration: [
      dayjs(getLocaleDateFormat(assignment?.startDate ?? '')).toDate(),
      dayjs(getLocaleDateFormat(assignment?.dueDate ?? '')).toDate()
    ],
    assignTo: null,
    nonWorkingDates: []
  }

  const [resourceOrgStructureData, setResourceOrgStructureData] = useState<ResourceOrgStructure | null | undefined>({
    agencyName: assignment?.agencyName ?? '',
    costCenterName: assignment?.costCenterName ?? '',
    locationName: assignment?.locationName ?? '',
    agencyCode: assignment?.agencyCode ?? '',
    costCenterCode: assignment?.costCenterCode ?? ''
  })

  const [timesheetsDataList, setTimesheetsDataList] = useState<Timesheet[] | null>(null)
  const initialFormErrors: AllocationDetailsFormErrors = {
    hoursPerDay: '',
    duration: '',
    assignTo: ''
  }

  const [allocationDetailsFormData, setAllocationDetailsFormData] =
    useState<AllocationDetailsFormData>(initialFormState)
  const [allocationDetailsFormErrors, setAllocationDetailsFormErrors] =
    useState<AllocationDetailsFormErrors>(initialFormErrors)

  const [isLoaded, setIsLoaded] = useState(false)
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)

  const [resourceOrgStructure, setResourceOrgStructure] = useState<
    | {
        data: ResourceOrgStructure
        loading: boolean
      }
    | null
    | undefined
  >(null)

  const handleClose = useCallback(() => {
    setAllocationDetailsFormData(initialFormState)
    setAllocationDetailsFormErrors(initialFormErrors)
    setIsLoaded(false)
    showAllocationDetailsFormVar(undefined)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const { handleRemoveAllocation, loadingDeleteAssignment } = useRemoveAssignment(
    assignment?.id ?? '',
    activeUser?.altairNumber,
    handleClose
  )

  const {
    data: assignToData,
    error: assignToDataError,
    loading: assignToDataLoading
  } = useQuery<{ getResourceAndPlaceholder: SearchResourcePlaceholder[] }>(GET_RESOURCE_AND_PLACEHOLDER, {
    variables: {
      params: {
        userId: activeUser?.altairNumber,
        searchName: searchTermName
      }
    },
    skip: isLightUser,
    onError: (error) => {
      gplError(error, assignToDataError)
    }
  })

  const { error: resourceOrgStructureError } = useQuery<{
    getResourceOrgStructure?: ResourceOrgStructure
  }>(RESOURCE_ORG_STRUCTURE_QUERY, {
    variables: {
      resourceId: activeUser?.altairNumber
    },
    onError: (error) => {
      gplError(error, resourceOrgStructureError)
    }
  })

  const [updateAssignmentMutation, { loading: loadingUpdateAssignment }] = useMutation(UPDATE_ASSIGNMENT_MUTATION, {
    refetchQueries: [ASSIGNMENTS_QUERY],
    awaitRefetchQueries: true,
    onCompleted: () => {
      renderKeyVar(Date.now())
      handleClose()
    },
    onError: (error) => {
      showAlertVar({ message: error.message, show: true })
      gplError(error, error)
    }
  })
  const timesheetsParams = {
    employeeCode: assignToData
      ? assignToData.getResourceAndPlaceholder.find((x) => x.id === assignment?.userId)?.id
      : assignment?.userId,
    jobId: assignment?.altairJobNumber,
    assignmentStartDate: assignment?.startDate,
    assignmentEndDate: assignment?.dueDate
  }

  const {
    data: timesheetsData,
    error: timesheetsError,
    loading: timesheetsDataLoading
  } = useQuery<{ getTimesheetsForResource: Timesheet[] }>(TIMESHEETS_FOR_RESOURCE_QUERY, {
    variables: {
      params: timesheetsParams
      // These params below are for testing. They can be deleted when we know how to test the endpoints data
      // params: {
      //   jobId: 'B1892-000060-00',
      //   employeeCode: '10002287',
      //   assignmentStartDate: '2024-12-01',
      //   assignmentEndDate: '2025-01-01'
      // }
    },
    skip: userProfile === UserProfile.LIGHT_USER,
    onError: (error) => {
      gplError(error, timesheetsError)
    },
    fetchPolicy: 'network-only'
  })

  const { error: resourceHolidayAndTimeOffDetailsError, loading: resourceHolidayAndTimeOffDetailsLoading } = useQuery<{
    getHolidaysAndTimeOffDetailsByResourceId: ResourceHolidayAndTimeOffDetails
  }>(HOLIDAYS_AND_TIMEOFFDETAILS_QUERY, {
    variables: {
      params: {
        resourceId: allocationDetailsFormData.assignTo?.id,
        startDate: dayjs(allocationDetailsFormData.duration[0]).format('YYYY-MM-DD'),
        endDate: dayjs(allocationDetailsFormData.duration[1]).format('YYYY-MM-DD')
      }
    },
    skip:
      !allocationDetailsFormData.assignTo?.id ||
      !allocationDetailsFormData.duration ||
      allocationDetailsFormData.assignTo?.isPlaceholder, // Skip query if data is missing
    onError: (error) => {
      gplError(error, resourceHolidayAndTimeOffDetailsError)
    },
    onCompleted: ({ getHolidaysAndTimeOffDetailsByResourceId }) => {
      const holidays = filterAndMapNonWorkingDates(
        (getHolidaysAndTimeOffDetailsByResourceId?.holidays ?? []).map((holiday) => ({
          date: holiday.holidayDate
        })),
        allocationDetailsFormData.duration[0],
        allocationDetailsFormData.duration[1],
        NonWorkingDatesTypes.HOLIDAY
      )

      const timeOffDetails = filterAndMapNonWorkingDates(
        (getHolidaysAndTimeOffDetailsByResourceId?.timeOffDetails ?? []).map((timeOff) => ({
          date: timeOff.absenceDate
        })),
        allocationDetailsFormData.duration[0],
        allocationDetailsFormData.duration[1],
        NonWorkingDatesTypes.ABSENCE
      )

      const distinctNonWorkingDates = mergeDistinctNonWorkingDates([...holidays, ...timeOffDetails])

      if (distinctNonWorkingDates.length) {
        setAllocationDetailsFormData((prev) => ({
          ...prev,
          nonWorkingDates: distinctNonWorkingDates
        }))
      }
    }
  })

  const handleClickOpenRemovePopup = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(anchorEl ? null : event.currentTarget)
  }

  const handleCloseRemovePopup = () => {
    setAnchorEl(null)
  }

  useEffect(() => {
    if (assignment) {
      setResourceOrgStructureData({
        agencyName: assignment?.agencyName ?? '',
        costCenterName: assignment?.costCenterName ?? '',
        locationName: assignment?.locationName ?? '',
        agencyCode: assignment?.agencyCode ?? '',
        costCenterCode: assignment?.costCenterCode ?? ''
      })
    }
  }, [assignment])

  useEffect(() => {
    if (assignToData && assignment && !isLoaded) {
      const assignTo = assignToData.getResourceAndPlaceholder.find((x) => x.id === assignment.userId) ?? null

      // Using setTimeout with a delay of 0 to ensure the state update happens in the next event loop cycle.
      // This helps to force the component to re-render with the updated state when the data is available asynchronously.
      setTimeout(() => {
        setAllocationDetailsFormData({
          hoursPerDay: assignment.hoursPerDay,
          duration: [dayjs(assignment?.startDate).toDate(), dayjs(assignment?.dueDate).toDate()],
          assignTo
        })
        setIsLoaded(true)
      }, 0)
    }
    if (isLightUser && assignment) {
      setAllocationDetailsFormData({
        hoursPerDay: assignment.hoursPerDay,
        duration: [dayjs.tz(assignment.startDate).toDate(), dayjs.tz(assignment.dueDate).toDate()],
        assignTo: {
          id: assignment.userId,
          name: userInfo.name,
          externalId: '',
          isPlaceholder: false,
          integrationId: assignment.userIntegrationId
        }
      })
      setIsLoaded(true)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [assignToData, assignment])

  useEffect(() => {
    if (timesheetsData !== undefined) {
      setTimesheetsDataList(timesheetsData.getTimesheetsForResource)
    } else {
      setTimesheetsDataList(null)
    }
  }, [timesheetsData])

  if (!assignment) {
    return null
  }

  if (!isLoaded) {
    return null
  }

  const tileAssignmentStatus = getAssignmentStatus(
    assignment.assignmentState ?? '',
    assignment.taskState ?? '',
    resourceType ?? ''
  )
  const tileStatus = getTileStatus(assignment.assignmentState ?? '', assignment.taskState ?? '', resourceType ?? '')

  const normalizeTileStatus = (status: string) => status.replace(/-/g, '').toLowerCase()

  const checkFormErrors = (): boolean => {
    return (
      Boolean(allocationDetailsFormErrors.assignTo) ||
      Boolean(allocationDetailsFormErrors.hoursPerDay) ||
      Boolean(allocationDetailsFormErrors.duration) ||
      !allocationDetailsFormData.assignTo
    )
  }

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    if (checkFormErrors() || !allocationDetailsFormData.assignTo) {
      return
    }

    generalLoadingStateVar(true)

    const weekends = getWeekendDates(allocationDetailsFormData.duration[0], allocationDetailsFormData.duration[1])

    const params: UpdateAssignmentParams = {
      externalId: assignment.id,
      hoursPerDay: allocationDetailsFormData.hoursPerDay ?? 0,
      startDate: dayjs.tz(allocationDetailsFormData.duration[0]).format('YYYY-MM-DD'),
      dueDate: dayjs.tz(allocationDetailsFormData.duration[1]).format('YYYY-MM-DD'),
      userId: allocationDetailsFormData.assignTo.id,
      updateAssignmentDate: dayjs().toISOString(),
      isSplit: false,
      userLoggedInExternalId: activeUser?.altairNumber,
      nonWorkingDates: mergeDistinctNonWorkingDates([
        ...(allocationDetailsFormData.nonWorkingDates ?? []),
        ...(weekends ?? [])
      ]),
      agencyName: resourceOrgStructure?.data?.agencyName,
      costCenterName: resourceOrgStructure?.data?.costCenterName,
      locationName: resourceOrgStructure?.data?.locationName,
      agencyCode: resourceOrgStructure?.data?.agencyCode,
      costCenterCode: resourceOrgStructure?.data?.costCenterCode
    }

    // If the assignTo is a placeholder or if the user is changing the assignment to a different user,
    // we need to update the org structure details
    if (allocationDetailsFormData.assignTo?.id !== assignment.userId || assignment.isPlaceholder) {
      params.agencyName = resourceOrgStructure?.data?.agencyName
      params.costCenterName = resourceOrgStructure?.data?.costCenterName
      params.locationName = resourceOrgStructure?.data?.locationName
      params.agencyCode = resourceOrgStructure?.data?.agencyCode
      params.costCenterCode = resourceOrgStructure?.data?.costCenterCode
    }

    updateAssignmentMutation({
      variables: {
        assignment: params
      },
      onCompleted: () => {
        generalLoadingStateVar(false)
      }
    }).catch((error) => {
      console.error(error)
      generalLoadingStateVar(false)
    })

    handleClose()
  }

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="lg">
      <span
        style={{
          display: loadingUpdateAssignment || loadingDeleteAssignment ? 'inline-block' : 'none',
          position: 'absolute',
          width: '100%',
          height: '100%',
          top: 0,
          right: 0,
          zIndex: 1,
          background: 'white',
          opacity: 0.3
        }}
      ></span>
      <FormHeader
        title="Allocation details"
        projectName={assignment.projectName}
        taskName={assignment.taskName}
        state={(TaskStates as Record<string, string>)[tileAssignmentStatus?.toLowerCase()] || tileAssignmentStatus}
        stateStyle={normalizeTileStatus(tileStatus || '')}
        onClose={handleClose}
      />
      <Divider />
      <DialogContent
        sx={{
          width: '42.5rem',
          display: 'flex',
          padding: '1.25rem 1.5rem',
          flexDirection: 'column',
          alignItems: 'flex-start',
          gap: '1rem',
          alignSelf: 'stretch'
        }}
      >
        {!openNotesView && !openTimesheetsView && (
          <Box width="100%">
            <Box
              sx={{
                display: 'flex',
                width: '100%',
                flexDirection: 'column',
                gap: '0.375rem;',
                alignItems: 'flex-start'
              }}
            >
              <Typography variant="BodyMBold">Project</Typography>
              <Box
                sx={{
                  display: 'flex',
                  padding: ' 0.25rem 0rem',
                  alignItems: 'center',
                  gap: '0.25rem',
                  flex: ' 1 0 0'
                }}
              >
                <Typography
                  component="a"
                  href={`${externalLink}/project/${assignment.projectIntegrationId}/overview`}
                  target="_blank"
                  rel="noopener noreferrer"
                  data-testid="AllocationDetailsFormProjectName"
                  variant="LinkMRegular"
                  style={{ color: 'black' }}
                >
                  {assignment.projectName}
                </Typography>
                <SeeDetails
                  externalLink={`${externalLink}/project/${assignment.projectIntegrationId}/overview`}
                  showText={false}
                />
              </Box>
            </Box>
            <Box
              sx={{
                display: 'flex',
                width: '100%',
                flexDirection: 'column',
                gap: '0.375rem;',
                alignItems: 'flex-start'
              }}
            >
              <Typography variant="BodyMBold">Task</Typography>
              <Box
                sx={{
                  display: 'flex',
                  padding: ' 0.25rem 0rem',
                  alignItems: 'center',
                  gap: '0.25rem',
                  flex: ' 1 0 0'
                }}
              >
                <Typography
                  component="a"
                  href={`${externalLink}/task/${assignment.taskId}/overview`}
                  target="_blank"
                  rel="noopener noreferrer"
                  variant="LinkMRegular"
                  data-testid="AllocationDetailsFormTaskName"
                  style={{ color: 'black' }}
                >
                  {assignment.taskName}
                </Typography>
                <SeeDetails externalLink={`${externalLink}/task/${assignment.taskId}/overview`} showText={false} />
              </Box>
              <Divider sx={{ width: '100%' }} />
            </Box>
            <Box sx={{ width: '100%' }}>
              <form data-testid="allocation-details-form" id="allocation-details-form" onSubmit={handleSubmit}>
                <Box
                  sx={{
                    display: 'flex',
                    flex: '1 1 0rem',
                    alignItems: 'flex-start',
                    justifyContent: 'space-between',
                    margin: '0 0',
                    width: '100%'
                  }}
                >
                  <Box sx={{ width: '47%' }}>
                    <FormLabel
                      className={'MuiFormLabel-asterisk'}
                      required
                      sx={{
                        display: 'block',
                        fontWeight: 'bold',
                        color: palette.primary.dark,
                        my: 3,
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        '& .MuiFormLabel-asterisk': {
                          color: palette['500-Accent-3'].main
                        }
                      }}
                      htmlFor="hoursPerDay"
                    >
                      Hours per Day
                    </FormLabel>
                    <OutlinedInput
                      id="hoursPerDay"
                      data-testid="hoursPerDay"
                      name="hoursPerDay"
                      type="number"
                      value={allocationDetailsFormData.hoursPerDay}
                      onChange={handleHoursChange(setAllocationDetailsFormData, setAllocationDetailsFormErrors)}
                      size="small"
                      placeholder="Must be more than 0"
                      sx={{ width: '100%' }}
                      error={Boolean(allocationDetailsFormErrors.hoursPerDay)}
                      disabled={Boolean(isLightUser) || isFormDisabled}
                    />
                    {allocationDetailsFormErrors.hoursPerDay && (
                      <Typography color="error">{allocationDetailsFormErrors.hoursPerDay}</Typography>
                    )}
                  </Box>
                  <Box sx={{ width: '49%' }}>
                    <Typography
                      variant="body1"
                      fontWeight="bolder"
                      component="label"
                      htmlFor="duration"
                      sx={{ display: 'block', my: 3 }}
                    >
                      Duration{' '}
                      <Typography fontWeight="bolder" component="span" color={palette['500-Accent-3'].main}>
                        *
                      </Typography>
                    </Typography>
                    <Box data-testid="dateRangePicker">
                      <fieldset
                        disabled={isLightUser}
                        style={{ border: 'none', padding: 0, margin: 0, position: 'relative' }}
                      >
                        <span
                          style={{
                            display: isLightUser || isFormDisabled ? 'inline-block' : 'none',
                            position: 'absolute',
                            width: '310px',
                            height: '45px',
                            top: 0,
                            right: 0,
                            zIndex: 1,
                            background: 'transparent'
                          }}
                        ></span>
                        <LionDateRangePicker
                          locale={navigator.language.toLowerCase()}
                          startDate={allocationDetailsFormData.duration[0]}
                          endDate={allocationDetailsFormData.duration[1]}
                          onDateChange={handleDateChange(setAllocationDetailsFormData, allocationDetailsFormData)}
                          monthsInView={2}
                          minDate={assignment.taskStartDate ? new Date(assignment.taskStartDate) : null}
                          maxDate={assignment.taskDueDate ? new Date(assignment.taskDueDate) : null}
                        />
                      </fieldset>
                      {resourceHolidayAndTimeOffDetailsLoading && (
                        <Box>
                          <LinearProgress />
                        </Box>
                      )}

                      {generateWarningMessage(allocationDetailsFormData) && (
                        <Box
                          sx={{
                            background: `${palette.info.light}33`,
                            marginTop: '5px',
                            borderRadius: '10px',
                            padding: '4px'
                          }}
                        >
                          <Typography fontWeight={'bold'} fontSize={'small'} color={palette.info.dark}>
                            Note:{' '}
                            <span style={{ color: palette.primary.dark, fontWeight: '500' }}>
                              The selected date range includes {generateWarningMessage(allocationDetailsFormData)}.
                              Please review carefully!
                            </span>
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </Box>
                </Box>
                <Box sx={{ margin: '1.5rem 0', width: '100%' }}>
                  <FormLabel
                    className={'MuiFormLabel-asterisk'}
                    required
                    sx={{
                      fontWeight: 'bolder',
                      color: palette.primary.dark,
                      my: 3,
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      '& .MuiFormLabel-asterisk': {
                        color: palette['500-Accent-3'].main
                      }
                    }}
                    htmlFor="assignTo"
                  >
                    Assign To
                  </FormLabel>
                  <Autocomplete
                    sx={{ marginTop: '0.5rem' }}
                    fullWidth
                    disabled={Boolean(isLightUser) || isFormDisabled}
                    disablePortal
                    id="assignTo"
                    data-testid="assignTo"
                    options={assignToData?.getResourceAndPlaceholder ?? []}
                    getOptionLabel={(option) => option?.name}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        error={allocationDetailsFormErrors.assignTo !== ''}
                        placeholder="Search for a resource..."
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <>
                              {assignToDataLoading ? (
                                <CircularProgress size={20} sx={{ color: '#999', marginRight: '8px' }} />
                              ) : null}
                              {params.InputProps.endAdornment}
                            </>
                          )
                        }}
                      />
                    )}
                    size="small"
                    isOptionEqualToValue={(option, value) => option.id === value.id}
                    onChange={(_e, value) => {
                      setResourceOrgStructure(undefined)
                      setResourceOrgStructureData(undefined)
                      setAllocationDetailsFormData((prev) => ({
                        prev,
                        ...allocationDetailsFormData,
                        assignTo: value,
                        nonWorkingDates: []
                      }))
                      if (value?.isPlaceholder) {
                        if (value?.id) {
                          fetchProjectDetails({ variables: { projectId: assignment.projectId } })
                            .then((response) => {
                              const projectData = response.data?.getProjectsByProjectId
                              if (projectData) {
                                fetchOrgStructure({
                                  variables: {
                                    params: {
                                      costCenterCode: projectData[0].costCenterCode,
                                      agencyCode: projectData[0].agencyCode
                                    }
                                  }
                                })
                                  .then((orgResponse) => {
                                    const orgData = orgResponse.data?.getOrgStructureByAgencyCodeCostCenterCode
                                    if (orgData) {
                                      setResourceOrgStructureData((prev) => ({
                                        ...prev,
                                        costCenterCode: projectData[0].costCenterCode ?? '',
                                        agencyCode: projectData[0].agencyCode ?? '',
                                        agencyName: orgData.agencyName ?? '',
                                        costCenterName: orgData.costCenterName ?? '',
                                        locationName: orgData.locationName ?? ''
                                      }))
                                    }
                                  })
                                  .catch((error) => {
                                    console.error('Error fetching organization structure:', error)
                                  })
                              }
                            })
                            .catch((error) => {
                              console.error('Error fetching project details:', error)
                            })
                        }
                      }
                    }}
                    onInputChange={(_event, value, reason) => {
                      if (reason === 'clear') {
                        setSearchTermName('')
                        setAllocationDetailsFormData((prev) => ({
                          prev,
                          ...allocationDetailsFormData,
                          assignTo: null,
                          nonWorkingDates: []
                        }))
                      } else if (reason === 'input') {
                        if (value.length >= 3) {
                          setSearchTermName(value)
                        } else {
                          setSearchTermName('')
                          setAllocationDetailsFormData((prev) => ({
                            prev,
                            ...allocationDetailsFormData,
                            assignTo: null,
                            nonWorkingDates: []
                          }))
                        }
                      }
                    }}
                    onBlur={() => {
                      setAllocationDetailsFormErrors({
                        ...allocationDetailsFormErrors,
                        assignTo: allocationDetailsFormData.assignTo === null ? 'Assign to is required' : ''
                      })
                    }}
                    value={allocationDetailsFormData.assignTo}
                  />
                  {allocationDetailsFormErrors.assignTo && (
                    <Typography color="error"> {allocationDetailsFormErrors.assignTo}</Typography>
                  )}
                  {allocationDetailsFormData.assignTo && (
                    <SeeDetails
                      externalLink={`${externalLink}/${
                        allocationDetailsFormData.assignTo.isPlaceholder ? 'jobrole' : 'user'
                      }/${allocationDetailsFormData.assignTo.integrationId}/details`}
                      showText
                    />
                  )}
                </Box>
                {/* Additional Details */}
                <Box>
                  <Box
                    sx={{
                      marginTop: '1rem',
                      display: 'flex',
                      padding: '0',
                      alignItems: 'center',
                      gap: '1rem',
                      alignSelf: 'stretch'
                    }}
                  >
                    <Typography variant="OverlineS" sx={{ letterSpacing: '0.0625rem', textTransform: 'uppercase' }}>
                      Additional Details
                    </Typography>
                    <Divider
                      sx={{
                        display: 'flex',
                        height: '0.0625rem',
                        justifyContent: 'center',
                        alignItems: 'center',
                        flex: ' 1 0 0'
                      }}
                    ></Divider>
                  </Box>
                </Box>
                {allocationDetailsFormData.assignTo?.isPlaceholder && (
                  <PlaceholderAdditionalDetails
                    updateResourceOrgStructure={setResourceOrgStructure}
                    resourceOrgStructureData={resourceOrgStructureData}
                    disableFields={Boolean(isLightUser) || isFormDisabled}
                  />
                )}
                {!allocationDetailsFormData.assignTo?.isPlaceholder && (
                  <ResourceAdditionalDetails
                    assignToId={allocationDetailsFormData?.assignTo?.id}
                    updateResourceOrgStructure={setResourceOrgStructure}
                    resourceOrgStructure={resourceOrgStructureData}
                  />
                )}
              </form>
            </Box>

            {!isLightUser && !assignment.isPlaceholder && (
              <TimesheetsInfoCta
                openTimesheetsView={setOpenTimesheetsView}
                timesheetDataExists={!!timesheetsData?.getTimesheetsForResource.length}
                isLoading={timesheetsDataLoading}
              />
            )}

            <FormTabs
              openNotesView={setOpenNotesView}
              assignmentNotes={assignment.assignmentNotes}
              taskNotes={assignment.taskNotes}
              isDisplayingNotes={openNotesView}
            />
          </Box>
        )}
        {openNotesView && (
          <Box width="100%">
            <FormTabs
              openNotesView={setOpenNotesView}
              assignmentNotes={assignment.assignmentNotes}
              taskNotes={assignment.taskNotes}
              isDisplayingNotes={openNotesView}
            />
          </Box>
        )}
        {openTimesheetsView && (
          <Box width="100%">
            <TimesheetsInfoList timesheetsList={timesheetsDataList ?? []} />
          </Box>
        )}
      </DialogContent>

      <Divider />
      <DialogActions sx={{ width: '100%', justifyContent: 'space-between', padding: '1rem' }}>
        <Box sx={{ display: 'flex', justifyContent: 'flex-start' }}>
          {!openNotesView && !isLightUser && !openTimesheetsView && (
            <>
              <Button
                onClick={handleClickOpenRemovePopup}
                variant="text"
                color="error"
                sx={{
                  display: 'flex',
                  gap: '0.25rem',
                  color: loadingDeleteAssignment ? palette.grey['400'] : palette.error.main
                }}
                disabled={loadingUpdateAssignment || isFormDisabled}
              >
                <DeleteIcon height="24" width="24" /> Remove Allocation{' '}
                {loadingDeleteAssignment && (
                  <CircularProgress
                    size={24}
                    sx={{
                      color: palette.primary.main,
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      marginTop: '-12px',
                      marginLeft: '-12px'
                    }}
                  />
                )}
              </Button>
              {Boolean(anchorEl) && (
                <RemovePopup
                  anchorEl={anchorEl}
                  message="Are you sure you want to remove this allocation?"
                  closeText="Close"
                  removeText="Remove"
                  onRemove={() => {
                    handleCloseRemovePopup()
                    handleRemoveAllocation().catch((error) => {
                      throw error
                    })
                  }}
                  onClose={handleCloseRemovePopup}
                  isLoading={loadingUpdateAssignment}
                />
              )}
            </>
          )}
          {(openTimesheetsView || openNotesView) && (
            <Button
              startIcon={<SimpleArrowIcon height="20" width="20" rotation="-90" />}
              sx={{ mx: 1, borderColor: palette.secondary.main }}
              variant="outlined"
              onClick={() => {
                setOpenNotesView(false)
                setOpenTimesheetsView(false)
              }}
              data-testid="go-back-button"
            >
              Go back
            </Button>
          )}
        </Box>
        {!openNotesView && !openTimesheetsView && (
          <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              sx={{ mx: 1, borderColor: palette.secondary.main }}
              variant="outlined"
              onClick={handleClose}
              disabled={loadingUpdateAssignment || loadingDeleteAssignment}
            >
              Close
            </Button>
            {!isLightUser && (
              <Button
                type="submit"
                form="form"
                sx={{ mx: 1 }}
                onClick={() => {
                  const form = document.getElementById('allocation-details-form') as HTMLFormElement
                  if (form) {
                    const event = new Event('submit', { bubbles: true, cancelable: true })
                    form.dispatchEvent(event)
                  }
                }}
                disabled={
                  !resourceOrgStructure ||
                  resourceOrgStructure?.loading ||
                  resourceHolidayAndTimeOffDetailsLoading ||
                  checkFormErrors() ||
                  loadingUpdateAssignment ||
                  loadingDeleteAssignment ||
                  isFormDisabled
                }
              >
                Update Allocation{' '}
                {loadingUpdateAssignment && (
                  <CircularProgress
                    size={24}
                    sx={{
                      color: palette.primary.main,
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      marginTop: '-12px',
                      marginLeft: '-12px'
                    }}
                  />
                )}
              </Button>
            )}
          </Box>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default memo(AllocationDetailsForm)
