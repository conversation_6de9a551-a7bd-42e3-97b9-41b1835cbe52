// Lion ui imports
import { Box } from 'lion-ui'

// Local imports
import type { FooterIconProps } from './FooterIcon.props'

const FooterIcon: React.FC<FooterIconProps> = ({ children }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        padding: '0.2rem',
        margin: '0 0.1rem',
        borderRadius: '6px',
        border: '1px solid #808080',
        alignItems: 'center'
      }}
    >
      {children && <>{children}</>}
    </Box>
  )
}

export default FooterIcon
