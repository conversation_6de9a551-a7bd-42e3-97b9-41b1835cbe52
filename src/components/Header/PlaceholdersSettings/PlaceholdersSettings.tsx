import { useQuery, useReactiveVar } from '@apollo/client'
import { debounce } from 'lodash'
import {
  businessUnitsEmptyVar,
  placeholderSettingsPayloadVar,
  placeholderSettingsResetVar,
  placeholderSettingsSaveVar,
  userInfoVar
} from '@gql/client/localState'
import { PLACEHOLDERS_BY_USER_ID_QUERY, PLACEHOLDERS_BY_WORK_CODE } from '@gql/queries/placeholders.query'
import { type PlaceholdersSettingsPayload } from '@typeDefs/PlaceholdersSettingsPayload'
import { gplError } from '@utils/gplError'
import { Autocomplete, Box, Checkbox, Chip, CircularProgress, FormLabel, palette, TextField, Typography } from 'lion-ui'
import { useCallback, useEffect, useState } from 'react'
import { type PlaceholdersSettingsDefaultsResponse } from '@typeDefs/PlaceholdersSettingsDefaults'
import { AGENCY_LOCATIONS_QUERY, COST_CENTERS_QUERY, SEARCH_RESOURCE_AGENCY_QUERY } from '@gql/queries/resources.query'
import { type Agency } from '@typeDefs/Agency'
import { type CostCenter } from '@typeDefs/CostCenter'

import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank'
import CheckBoxIcon from '@mui/icons-material/CheckBox'
import { type JobRole } from '@typeDefs/JobRole'

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />
const checkedIcon = <CheckBoxIcon fontSize="small" />

enum PlaceholdersSettingsInputType {
  BUSINESS_UNIT = 'businessUnit',
  LOCATION = 'location',
  COST_CENTER = 'costCenter',
  JOB_ROLE = 'jobrole'
}

const PlaceholdersSettings: React.FC = () => {
  const userInfo = useReactiveVar(userInfoVar)
  const [formValues, setFormValues] = useState<null | PlaceholdersSettingsPayload>(null)
  const [agencySearchTerm, setAgencySearchTerm] = useState<string>('')
  const [agencyInputValue, setAgencyInputValue] = useState<string>('')
  const [locationSearchTerm, setLocationSearchTerm] = useState<string>('')
  const [costCenterSearchTerm, setCostCenterSearchTerm] = useState<string>('')
  const [jobRoleSearchTerm, setJobRoleSearchTerm] = useState<string>('')
  const [jobRoleInputValue, setJobRoleInputValue] = useState<string>('')
  const savePlaceHolders = useReactiveVar(placeholderSettingsSaveVar)
  const placeholderSettingsReset = useReactiveVar(placeholderSettingsResetVar)

  const handleBusinessUnitChange = (value: Agency[] = []) => {
    setFormValues((prev) => ({
      ...prev,
      businessUnit: value,
      location: [],
      costCenter: []
    }))

    businessUnitsEmptyVar(value?.length === 0 || value === null)
    setLocationSearchTerm('')
    setAgencyInputValue('')
  }

  const handleLocationChange = (value: string[] = []) => {
    setFormValues((prev) => ({
      ...prev,
      location: value
    }))
    setLocationSearchTerm('')
  }

  const handleCostCenterChange = (value: CostCenter[] = []) => {
    setFormValues((prev) => ({
      ...prev,
      costCenter: value
    }))
    setCostCenterSearchTerm('')
  }

  const handleJobRoleChange = (value: JobRole[] = []) => {
    setFormValues((prev) => ({
      ...prev,
      jobrole: value
    }))
    setJobRoleInputValue('')
  }

  const {
    data: defaultData,
    loading: defaultDataLoading,
    error: defaultDataError
  } = useQuery<PlaceholdersSettingsDefaultsResponse>(PLACEHOLDERS_BY_USER_ID_QUERY, {
    variables: {
      params: {
        userLoggedInExternalId: userInfo?.altairNumber
      }
    },
    onError: (error) => {
      gplError(error, defaultDataError)
    },
    fetchPolicy: 'network-only'
  })

  const {
    data: agencyData,
    error: agencyError,
    loading: agencyLoading
  } = useQuery<{ searchAgencyOrgStructure: Agency[] }>(SEARCH_RESOURCE_AGENCY_QUERY, {
    variables: {
      searchTerm: agencySearchTerm
    },
    onError: (error) => {
      gplError(error, agencyError)
    },
    skip: agencySearchTerm.length < 3
  })

  const {
    data: locationData,
    error: locationError,
    loading: locationLoading
  } = useQuery<{ getLocationsByAgenciesOrgStructure: string[] }>(AGENCY_LOCATIONS_QUERY, {
    variables: {
      agencyCodes:
        formValues?.businessUnit?.filter((bu) => !isNaN(Number(bu.agencyCode))).map((item) => item.agencyCode) ?? []
    },
    onError: (error) => {
      gplError(error, locationError)
    },
    skip: !formValues?.businessUnit || formValues.businessUnit.length < 1
  })

  const {
    data: costCentersData,
    error: costCentersError,
    loading: costCentersLoading
  } = useQuery<{ getCostCentersByLocationsOrgStructure: CostCenter[] }>(COST_CENTERS_QUERY, {
    variables: {
      agencyCodes:
        formValues?.businessUnit?.filter((item) => !isNaN(Number(item.agencyCode))).map((bu) => bu.agencyCode) ?? [],
      cities: formValues?.location
    },
    onError: (error) => {
      gplError(error, costCentersError)
    },
    skip: !formValues?.businessUnit || formValues.businessUnit.length < 1
  })

  const {
    data: jobRoleData,
    error: jobRoleError,
    loading: jobRoleLoading
  } = useQuery<{ getPlaceholderByWorkCode: Array<{ workCodeName: string; workCode: string }> }>(
    PLACEHOLDERS_BY_WORK_CODE,
    {
      variables: {
        workCode: jobRoleSearchTerm
      },
      onError: (error) => {
        gplError(error, jobRoleError)
      },
      skip: jobRoleSearchTerm.length < 3
    }
  )

  const debouncedSetSearchTerm = debounce((category: string, value: string) => {
    switch (category) {
      case PlaceholdersSettingsInputType.BUSINESS_UNIT:
        setAgencySearchTerm(value)
        break
      case PlaceholdersSettingsInputType.JOB_ROLE:
        setJobRoleSearchTerm(value)
        break
    }
  }, 300)

  const handleClearFilters = useCallback(() => {
    setAgencySearchTerm('')
    setAgencyInputValue('')
    setCostCenterSearchTerm('')
    setLocationSearchTerm('')
    setJobRoleSearchTerm('')
    setJobRoleInputValue('')
    resetData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const resetData = () => {
    setFormValues(() => ({
      businessUnit: [],
      jobrole: [],
      location: [],
      costCenter: []
    }))
  }

  useEffect(() => {
    if (savePlaceHolders && formValues) {
      placeholderSettingsSaveVar(false)
      placeholderSettingsPayloadVar(formValues)
    }
  }, [savePlaceHolders, formValues])

  useEffect(() => {
    if (placeholderSettingsReset) {
      handleClearFilters()
      placeholderSettingsResetVar(false)
    }
  }, [placeholderSettingsReset, handleClearFilters])

  useEffect(() => {
    if (defaultData) {
      setFormValues(() => ({
        businessUnit: defaultData?.getPlaceholderFilterValuesByUserId.businessUnit.map((item) => ({
          agencyCode: item?.label.split(':')[0].trim(),
          agencyName: item?.label.split(':')[1].trim()
        })),
        jobrole: defaultData?.getPlaceholderFilterValuesByUserId.jobrole.map((item) => ({
          workCode: item?.label.split(':')[0].trim(),
          workCodeName: item?.label.split(':')[1].trim()
        })),
        location: defaultData?.getPlaceholderFilterValuesByUserId.location.map((item) => item?.label),
        costCenter: defaultData?.getPlaceholderFilterValuesByUserId.costCenter.map((item) => ({
          costCenterCode: item?.label.split(':')[0].trim(),
          costCenterName: item?.label.split(':')[1].trim()
        }))
      }))
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultData])

  return (
    <Box sx={{ padding: '1rem 2.5rem', maxWidth: '100%' }}>
      <Typography variant="body1" sx={{ marginBottom: '1rem' }}>
        Search for cross-agency placeholder assignments; these settings will be applied each time you access the
        calendar.
      </Typography>
      <Box>
        <FormLabel
          sx={{
            fontWeight: 'bold',
            color: palette.primary.dark,
            padding: '0.7rem 0',
            fontSize: '0.875rem',
            // eslint-disable-next-line @typescript-eslint/naming-convention
            '& .MuiFormLabel-asterisk': {
              color: palette['500-Accent-3'].main
            }
          }}
          htmlFor="businessUnit"
          required={true}
        >
          Agency
        </FormLabel>
        <Autocomplete
          size="small"
          multiple
          sx={{ marginTop: '0.5rem' }}
          fullWidth
          disablePortal
          disableCloseOnSelect
          id="businessUnit"
          data-testid="businessUnit"
          limitTags={3}
          options={[...(agencyData?.searchAgencyOrgStructure ?? [])]}
          getOptionLabel={(option: Agency) => `${option?.agencyCode}: ${option?.agencyName}`}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder="Search for a Agency..."
              InputProps={{
                ...params.InputProps,
                endAdornment: (
                  <>
                    {agencyLoading ? <CircularProgress size={20} sx={{ color: '#999', marginRight: '8px' }} /> : null}
                    {params.InputProps.endAdornment}
                  </>
                )
              }}
            />
          )}
          isOptionEqualToValue={(option, value) => option.agencyCode === value.agencyCode}
          value={formValues?.businessUnit ?? []}
          inputValue={agencyInputValue}
          onInputChange={(_e, newInputValue, reason) => {
            if (reason === 'input') {
              setAgencyInputValue(newInputValue)
              debouncedSetSearchTerm(PlaceholdersSettingsInputType.BUSINESS_UNIT, newInputValue)
            }
          }}
          onChange={(_e, newValue) => {
            handleBusinessUnitChange(newValue)
          }}
          noOptionsText={agencySearchTerm.length < 3 ? 'Type at least 3 characters' : ''}
          loading={agencyLoading}
          renderOption={(props, option, { selected }) => {
            const { key, ...optionProps } = props
            return (
              <li key={key} {...optionProps}>
                <Checkbox icon={icon} checkedIcon={checkedIcon} style={{ marginRight: 8 }} checked={selected} />
                {option.agencyCode}: {option.agencyName}
              </li>
            )
          }}
          renderTags={(selected, getTagProps) => {
            return selected.map((option, index) => {
              const { key, ...tagProps } = getTagProps({ index })
              return (
                <Chip key={option.agencyCode} {...tagProps} label={`${option.agencyCode} : ${option.agencyName}`} />
              )
            })
          }}
        />
      </Box>
      <Box sx={{ marginTop: '1rem' }}>
        <FormLabel
          sx={{
            fontWeight: 'bold',
            color: palette.primary.dark,
            padding: '0.7rem 0',
            fontSize: '0.875rem'
          }}
          htmlFor="location"
        >
          Location
        </FormLabel>
        <Autocomplete
          size="small"
          multiple
          sx={{ marginTop: '0.5rem' }}
          fullWidth
          disablePortal
          disableCloseOnSelect
          id="location"
          data-testid="location"
          options={[...(locationData?.getLocationsByAgenciesOrgStructure ?? [])]}
          getOptionLabel={(option) => `${option}`}
          limitTags={3}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder="Search for a location..."
              InputProps={{
                ...params.InputProps,
                endAdornment: (
                  <>
                    {locationLoading ? <CircularProgress size={20} sx={{ color: '#999', marginRight: '8px' }} /> : null}
                    {params.InputProps.endAdornment}
                  </>
                )
              }}
            />
          )}
          isOptionEqualToValue={(option, value) => option === value}
          value={formValues?.location ?? []}
          inputValue={locationSearchTerm}
          onInputChange={(_e, newInputValue, reason) => {
            if (reason === 'input') {
              setLocationSearchTerm(newInputValue)
            }
          }}
          onChange={(_e, newValue) => {
            handleLocationChange(newValue)
          }}
          noOptionsText={locationSearchTerm.length < 3 ? 'Type at least 3 characters' : 'No locations found'}
          loading={agencyLoading || locationLoading}
          renderOption={(props, option, { selected }) => {
            const { key, ...optionProps } = props
            if (option === 'No locations found' || option === 'Type at least 3 characters') {
              return (
                <li key={key} {...optionProps}>
                  <Typography variant="body2" color="textSecondary">
                    {option}
                  </Typography>
                </li>
              )
            }

            return (
              <li key={key} {...optionProps}>
                <Checkbox icon={icon} checkedIcon={checkedIcon} style={{ marginRight: 8 }} checked={selected} />
                {option}
              </li>
            )
          }}
          filterOptions={(options, state) => {
            if (state.inputValue.length === 0) {
              return options
            }
            if (state.inputValue.length < 3) {
              return ['Type at least 3 characters']
            }
            const filtered = options.filter((option) => option.toLowerCase().includes(state.inputValue.toLowerCase()))
            return filtered.length === 0 ? ['No locations found'] : filtered
          }}
          renderTags={(selected, getTagProps) => {
            return selected.map((option, index) => {
              const { key, ...tagProps } = getTagProps({ index })
              return <Chip key={option} {...tagProps} label={option} />
            })
          }}
        />
      </Box>

      <Box sx={{ marginTop: '1rem' }}>
        <FormLabel
          sx={{
            fontWeight: 'bold',
            color: palette.primary.dark,
            padding: '0.7rem 0',
            fontSize: '0.875rem'
          }}
          htmlFor="costCenter"
        >
          Cost Center
        </FormLabel>
        <Autocomplete
          size="small"
          multiple
          sx={{ marginTop: '0.5rem' }}
          fullWidth
          disablePortal
          disableCloseOnSelect
          id="location"
          data-testid="location"
          options={[...(costCentersData?.getCostCentersByLocationsOrgStructure ?? [])]}
          getOptionLabel={(option) => `${option?.costCenterCode}: ${option?.costCenterName}`}
          limitTags={3}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder="Search for a cost center..."
              InputProps={{
                ...params.InputProps,
                endAdornment: (
                  <>
                    {costCentersLoading ? (
                      <CircularProgress size={20} sx={{ color: '#999', marginRight: '8px' }} />
                    ) : null}
                    {params.InputProps.endAdornment}
                  </>
                )
              }}
            />
          )}
          isOptionEqualToValue={(option, value) => option.costCenterCode === value.costCenterCode}
          value={formValues?.costCenter ?? []}
          inputValue={costCenterSearchTerm}
          onInputChange={(_e, newInputValue, reason) => {
            if (reason === 'input') {
              setCostCenterSearchTerm(newInputValue)
            }
          }}
          onChange={(_e, newValue) => {
            handleCostCenterChange(newValue)
          }}
          noOptionsText={costCenterSearchTerm.length < 3 ? 'Type at least 3 characters' : 'No cost centers found'}
          loading={costCentersLoading}
          renderOption={(props, option, { selected }) => {
            const { key, ...optionProps } = props
            if (
              option.costCenterName === 'No cost centers found' ||
              option.costCenterName === 'Type at least 3 characters'
            ) {
              return (
                <li key={key} {...optionProps}>
                  <Typography variant="body2" color="textSecondary">
                    {option.costCenterName}
                  </Typography>
                </li>
              )
            }
            return (
              <li key={key} {...optionProps}>
                <Checkbox icon={icon} checkedIcon={checkedIcon} style={{ marginRight: 8 }} checked={selected} />
                {option.costCenterCode}: {option.costCenterName}
              </li>
            )
          }}
          filterOptions={(options, state) => {
            if (state.inputValue.length === 0) {
              return options
            }
            if (state.inputValue.length < 3) {
              return [{ costCenterCode: '', costCenterName: 'Type at least 3 characters' }]
            }
            const filtered = options.filter((option) => {
              const lowerCaseInput = state.inputValue.toLowerCase()
              const searchValue = option.costCenterCode.toLowerCase() + ' ' + option.costCenterName.toLowerCase()
              return searchValue.includes(lowerCaseInput)
            })
            return filtered.length === 0 ? [{ costCenterCode: '', costCenterName: 'No cost centers found' }] : filtered
          }}
          renderTags={(selected, getTagProps) => {
            return selected.map((option, index) => {
              const { key, ...tagProps } = getTagProps({ index })
              return (
                <Chip
                  key={option.costCenterCode}
                  {...tagProps}
                  label={`${option.costCenterCode} : ${option.costCenterName}`}
                />
              )
            })
          }}
        />
      </Box>

      <Box sx={{ marginTop: '1rem' }}>
        <FormLabel
          sx={{
            fontWeight: 'bold',
            color: palette.primary.dark,
            padding: '0.7rem 0',
            fontSize: '0.875rem'
          }}
          htmlFor="jobrole"
        >
          Job Role
        </FormLabel>
        <Autocomplete
          multiple
          sx={{ marginTop: '0.5rem' }}
          fullWidth
          disablePortal
          disableCloseOnSelect
          options={[...(jobRoleData?.getPlaceholderByWorkCode ?? [])]}
          id="jobrole"
          data-testid="jobrole"
          getOptionLabel={(option) => `${option?.workCode}: ${option?.workCodeName}`}
          size="small"
          isOptionEqualToValue={(option, value) => option.workCode === value.workCode}
          value={formValues?.jobrole ?? []}
          inputValue={jobRoleInputValue}
          limitTags={3}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder="Search for a job roles..."
              InputProps={{
                ...params.InputProps,
                endAdornment: (
                  <>
                    {jobRoleLoading ? <CircularProgress size={20} sx={{ color: '#999', marginRight: '8px' }} /> : null}
                    {params.InputProps.endAdornment}
                  </>
                )
              }}
            />
          )}
          onInputChange={(_e, newInputValue, reason) => {
            if (reason === 'input') {
              setJobRoleInputValue(newInputValue)
              debouncedSetSearchTerm(PlaceholdersSettingsInputType.JOB_ROLE, newInputValue)
            }
          }}
          onChange={(_e, value) => {
            handleJobRoleChange(value)
          }}
          loading={defaultDataLoading || jobRoleLoading}
          noOptionsText={jobRoleSearchTerm.length < 3 ? 'Type at least 3 characters' : ''}
          renderOption={(props, option, { selected }) => {
            const { key, ...optionProps } = props
            return (
              <li key={key} {...optionProps}>
                <Checkbox icon={icon} checkedIcon={checkedIcon} style={{ marginRight: 8 }} checked={selected} />
                {option.workCode}: {option.workCodeName}
              </li>
            )
          }}
          renderTags={(selected, getTagProps) => {
            return selected.map((option, index) => {
              const { key, ...tagProps } = getTagProps({ index })
              return <Chip key={option.workCode} {...tagProps} label={`${option.workCode} : ${option.workCodeName}`} />
            })
          }}
        />
      </Box>
    </Box>
  )
}

export default PlaceholdersSettings
