import { render } from '@testing-library/react'
import TimesheetsInfo from '@components/Forms/AllocationDetailsForm/Timesheets/TimesheetsInfo/TimesheetsInfo'
import mockTimesheets from '../../../__mocks__/timesheets.mock'
describe('TimesheetsInfo', () => {
  it('renders submitted timesheet message correctly', () => {
    const { getByText } = render(<TimesheetsInfo timesheet={mockTimesheets[0]} />)
    expect(getByText(new RegExp(`${mockTimesheets[0].user}\\s+has`))).toBeInTheDocument()
    expect(getByText(new RegExp(`submitted ${mockTimesheets[0].hours} hours`))).toBeInTheDocument()
    expect(getByText(/for approval\./)).toBeInTheDocument()
    expect(getByText('01 Sep')).toBeInTheDocument()
  })

  it('renders unsubmitted timesheet message correctly', () => {
    const { getByText } = render(<TimesheetsInfo timesheet={mockTimesheets[3]} />)
    expect(getByText(new RegExp(`${mockTimesheets[3].user}\\ has`))).toBeInTheDocument()
    expect(getByText(new RegExp(`entered ${mockTimesheets[3].hours} hours`))).toBeInTheDocument()
    expect(getByText(/but hasn't submitted them yet\./)).toBeInTheDocument()
    expect(getByText('04 Sep')).toBeInTheDocument()
  })

  it('renders rejected timesheet message correctly', () => {
    const { getByText } = render(<TimesheetsInfo timesheet={mockTimesheets[2]} />)
    expect(getByText(new RegExp(`${mockTimesheets[2].user}\\'s`))).toBeInTheDocument()
    expect(getByText(new RegExp(`${mockTimesheets[2].hours} hours`))).toBeInTheDocument()
    expect(getByText(/entry has been/)).toBeInTheDocument()
    expect(getByText(/rejected/)).toBeInTheDocument()
    expect(getByText(/03 Sep/)).toBeInTheDocument()
  })

  it('renders approved timesheet message correctly', () => {
    const { getByText } = render(<TimesheetsInfo timesheet={mockTimesheets[1]} />)
    expect(getByText(new RegExp(`${mockTimesheets[1].user}\\'s`))).toBeInTheDocument()
    expect(getByText(new RegExp(`${mockTimesheets[1].hours} hours`))).toBeInTheDocument()
    expect(getByText(/entry has been/)).toBeInTheDocument()
    expect(getByText(/approved/)).toBeInTheDocument()
    expect(getByText(/02 Sep/)).toBeInTheDocument()
  })
})
