import { useCallback, useEffect, useState, memo, useMemo, createRef, useRef } from 'react'

// Component imports
import CalendarHeader from '@components/Calendar/CalendarHeader/CalendarHeader'
import CalendarSorting from '../CalendarSorting/CalendarSorting'
import PlaceholderCollapse from '@components/Calendar/PlaceholderCollapse/PlaceholderCollapse'
import Header from '@components/Header/Header/Header'
import ScrollOnIntersect from '@components/Calendar/ScrollOnIntersect/ScrollOnIntersect'
import CalendarPagination from '../CalendarPagination/CalendarPagination'
import PrepareRow from './PrepareRow'

// GQL and Apollo imports
import CALENDAR_QUERY from '@gql/queries/calendar.query'
import { ASSIGNMENTS_QUERY } from '@gql/queries/assignments.query'
import { PLACEHOLDERS_FILTERS_QUERY, PLACEHOLDERS_QUERY } from '@gql/queries/placeholders.query'
import { RESOURCES_FILTERS_QUERY, RESOURCES_QUERY, SINGLE_RESOURCE_QUERY } from '@gql/queries/resources.query'
import { NetworkStatus, useQuery, useReactiveVar } from '@apollo/client'

import {
  activeDatesVar,
  activeSortVar,
  numOfWeeksVar,
  showAlertVar,
  userInfoVar,
  userProfileVar,
  renderKeyVar,
  resourcesIdsVar,
  placeholdersIdsVar,
  isExpandedVar,
  selectedFiltersVar,
  selectedPlaceholderOrgStructureFilterVar,
  isApplyFiltersVar,
  generalLoadingStateVar,
  placeholderSettingsSaveVar,
  employeeCodeVar
} from '@gql/client/localState'

// UI library imports
import { Box, Grid, palette, Typography } from 'lion-ui'

// Constants imports
import { numsOfDaysPerWeek } from '@constants/timeOptions'
import { UserProfile } from '@constants/userProfile'

// Type imports
import { type Assignment } from '@typeDefs/Assignments'
import { type Resource } from '@typeDefs/Resource'
import { CalendarViewOptions, CalendarWorkingDays } from '@constants/calendarViewOptions'
import { type Placeholder, type PlaceholderResponse } from '@typeDefs/Placeholders'

// Utils import
import { gplError } from '@utils/gplError'
import { type Pagination as PaginationType } from '@typeDefs/common/Pagination'
import { generateGridStyles, injectStyles } from '@utils/gridstackColumnStylesGenerator'
import { CalendarBodyLoader } from '@components/Loaders/CalendarBodyLoader/CalendarBodyLoader'
import { CalendarDatesLoader } from '@components/Loaders/CalendarDatesLoader/CalendarDatesLoader'
import { getUserLLid } from '@utils/user'
import { type SelectedFilters } from '@typeDefs/SelectedFilters'
import { getAssignmentsQueryParams } from '@utils/getAssignmentsQueryParams'
import getDateRange from '@utils/getDateRange'

const CalendarBody = () => {
  const activeDates = useReactiveVar(activeDatesVar)
  const activeSort = useReactiveVar(activeSortVar)
  const userProfile = useReactiveVar(userProfileVar)
  const weeksSelector = useReactiveVar(numOfWeeksVar)
  const pageNumberPlaceholder = 1
  const pageSizePlaceholder = 1000
  const isLightUser = userProfile === UserProfile.LIGHT_USER
  const mainLoaderRows = 7
  const generalLoading = useReactiveVar(generalLoadingStateVar)
  const renderKey = useReactiveVar(renderKeyVar)
  const [userLLid, setUserLLid] = useState<string>('')
  const employeeCode = useReactiveVar(employeeCodeVar)
  const activeUser = useReactiveVar(userInfoVar)
  const selectedFilters = useReactiveVar(selectedFiltersVar)
  const selectedPlaceholderOrgStructureFilter = useReactiveVar(selectedPlaceholderOrgStructureFilterVar)
  const [filtersPayload, setFiltersPayload] = useState<SelectedFilters | undefined>(undefined)
  const [firstLoad, setFirstLoad] = useState<boolean>(true)
  const [placeholdersRecords, setPlaceholdersRecords] = useState<Placeholder[]>([])
  const [resourcesRecords, setResourcesRecords] = useState<Resource[]>([])
  const [isLoadingApplyFilters, setIsLoadingApplyFilters] = useState<boolean>(false)
  const capacityMultipliers: { [key in CalendarViewOptions]: number } = useMemo(() => {
    return {
      [CalendarViewOptions.TWO_WEEKS]: CalendarWorkingDays.TWO_WEEKS,
      [CalendarViewOptions.FOUR_WEEKS]: CalendarWorkingDays.FOUR_WEEKS,
      [CalendarViewOptions.ONE_DAY]: CalendarWorkingDays.ONE_DAY
    }
  }, [])
  const currentColumns = useReactiveVar(numOfWeeksVar) * numsOfDaysPerWeek
  const [pagination, setPagination] = useState<PaginationType>({ pageNumber: 1, pageSize: 50 })
  const placeholderContainerRef = useMemo(() => createRef<HTMLDivElement>(), [])
  const resourceContainerRef = useMemo(() => createRef<HTMLDivElement>(), [])
  const placeholderScrollPositionRef = useRef<number>(0)
  const resourceScrollPositionRef = useRef<number>(0)
  const [isActiveDatesChanged, setIsActiveDatesChanged] = useState<boolean>(false)
  const headerRef = useRef<HTMLDivElement>(null)
  const isPlaceholderExpanded = useReactiveVar(isExpandedVar)
  const isApplyFilters = useReactiveVar(isApplyFiltersVar)
  const [placeholderHeight, setPlaceholderHeight] = useState<string>('215px')
  const expandedPlaceholder = useReactiveVar(placeholderSettingsSaveVar)

  const { data: currentResourceData, error: currentResourceError } = useQuery(SINGLE_RESOURCE_QUERY, {
    variables: { resourceId: employeeCode || userLLid },
    onError: (error) => {
      gplError(error, currentResourceError)
    },
    skip: !employeeCode && !userLLid
  })

  const {
    data: calendarData,
    error: calendarDataError,
    loading: calendarDataLoading,
    networkStatus: calendarNetworkStatus
  } = useQuery(CALENDAR_QUERY, {
    variables: { params: activeDates },
    onError: (error) => {
      gplError(error, calendarDataError)
    }
  })

  const {
    data: placeholdersData,
    error: placeholdersError,
    loading: placeholdersDataLoading,
    networkStatus: placeholdersNetworkStatus
  } = useQuery<{ placeholders: PlaceholderResponse }>(PLACEHOLDERS_QUERY, {
    variables: {
      params: {
        userId: activeUser?.altairNumber,
        pageNumber: pageNumberPlaceholder, // placeholders are not paginated, always use the first page
        pageSize: pageSizePlaceholder, // placeholders are not paginated, always use a big number
        sort: { field: activeSort.field, order: activeSort.order }
      }
    },
    notifyOnNetworkStatusChange: true,
    skip:
      isLightUser ||
      !currentResourceData ||
      !firstLoad ||
      calendarNetworkStatus !== NetworkStatus.ready ||
      !activeUser.altairNumber,
    onError: (error) => {
      gplError(error, placeholdersError)
    },
    fetchPolicy: 'network-only'
  })

  const {
    data: placeholderFiltersData,
    error: placeholdersFiltersError,
    loading: placeholdersFiltersDataLoading,
    networkStatus: placeholdersFiltersNetworkStatus
  } = useQuery<{ placeholderFilters: PlaceholderResponse }>(PLACEHOLDERS_FILTERS_QUERY, {
    variables: {
      params: {
        userId: activeUser?.altairNumber,
        pageNumber: pageNumberPlaceholder, // placeholders are not paginated, always use the first page
        pageSize: pageSizePlaceholder, // placeholders are not paginated, always use a big number
        sort: { field: activeSort.field, order: activeSort.order },
        startDate: activeDates.startDate,
        endDate: activeDates.endDate,
        ...filtersPayload,
        isExpandedPlaceholderApplied: expandedPlaceholder
      }
    },
    notifyOnNetworkStatusChange: true,
    skip:
      isLightUser ||
      !currentResourceData ||
      firstLoad ||
      calendarNetworkStatus !== NetworkStatus.ready ||
      !selectedFilters?.placeholders,
    onError: (error) => {
      gplError(error, placeholdersFiltersError)
    },
    fetchPolicy: 'network-only'
  })

  const {
    data: resourcesData,
    error: resourcesError,
    loading: resourcesDataLoading,
    networkStatus: resourcesNetworkStatus
  } = useQuery(RESOURCES_QUERY, {
    variables: {
      params: {
        userId: activeUser?.altairNumber,
        pageNumber: pagination.pageNumber,
        pageSize: pagination.pageSize,
        startDate: activeDates.startDate,
        endDate: activeDates.endDate,
        sort: { field: activeSort.field, order: activeSort.order }
      },
      notifyOnNetworkStatusChange: true
    },
    skip:
      !currentResourceData ||
      !firstLoad ||
      calendarNetworkStatus !== NetworkStatus.ready ||
      !calendarData ||
      !activeUser.altairNumber,
    onError: (error) => {
      gplError(error, resourcesError)
    },
    fetchPolicy: 'network-only'
  })

  const {
    data: resourcesFiltersData,
    error: resourcesFiltersError,
    loading: resourcesFiltersDataLoading,
    networkStatus: resourcesFiltersNetworkStatus
  } = useQuery(RESOURCES_FILTERS_QUERY, {
    variables: {
      params: {
        userId: activeUser?.altairNumber,
        pageNumber: pagination.pageNumber,
        pageSize: pagination.pageSize,
        startDate: activeDates.startDate,
        endDate: activeDates.endDate,
        sort: { field: activeSort.field, order: activeSort.order },
        ...filtersPayload
      },
      notifyOnNetworkStatusChange: true
    },
    skip:
      !currentResourceData ||
      firstLoad ||
      calendarNetworkStatus !== NetworkStatus.ready ||
      !isApplyFilters ||
      !selectedFilters?.resources,
    onError: (error) => {
      gplError(error, resourcesFiltersError)
    },

    fetchPolicy: 'network-only'
  })

  if (firstLoad) {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
    resourcesIdsVar(resourcesData?.resources.items.map((resource: Resource) => resource.id) ?? [])
    placeholdersIdsVar(placeholdersData?.placeholders.items.map((placeholder: Placeholder) => placeholder.id) ?? [])
  }

  const {
    data: assignmentsData,
    error: assignmentsError,
    networkStatus: assignmentsNetworkStatus,
    loading: assignmentsDataLoading,
    refetch: refetchAssignments
  } = useQuery<{ assignments: Assignment[] }>(ASSIGNMENTS_QUERY, {
    variables: { params: getAssignmentsQueryParams() },
    skip:
      placeholdersNetworkStatus !== NetworkStatus.ready ||
      resourcesNetworkStatus !== NetworkStatus.ready ||
      !calendarData ||
      !currentResourceData ||
      !activeUser.altairNumber,

    notifyOnNetworkStatusChange: true,
    fetchPolicy: 'network-only',
    onError: (error) => {
      gplError(error, assignmentsError)
    },
    onCompleted: () => {
      setIsActiveDatesChanged(false)
    }
  })

  const handleChangePage = useCallback((_: React.ChangeEvent<unknown>, newPage: number) => {
    setPagination((prev) => ({ ...prev, pageNumber: newPage }))
  }, [])

  if (
    calendarDataError ??
    placeholdersError ??
    placeholdersFiltersError ??
    resourcesError ??
    resourcesFiltersError ??
    assignmentsError ??
    currentResourceError
  ) {
    showAlertVar({ show: true, message: 'An error has occurred while loading the calendar header and assignments' })
  }

  const handleApplyFilters = useCallback(() => {
    if (selectedFilters && Object.keys(selectedFilters).length > 0) {
      const payload: SelectedFilters = { ...selectedFilters }
      setFiltersPayload(payload)
      setFirstLoad(false)
    }
  }, [selectedFilters])

  // Update pagination total pages once resources and assignments data are fully loaded
  useEffect(() => {
    if (
      resourcesNetworkStatus === NetworkStatus.ready &&
      assignmentsNetworkStatus === NetworkStatus.ready &&
      resourcesData
    ) {
      setPagination((prev) => ({ ...prev, totalPages: resourcesData.resources.totalPages }))
    }
  }, [resourcesNetworkStatus, assignmentsNetworkStatus, resourcesData])

  useEffect(() => {
    setResourcesRecords([])
    if (selectedFilters && Object.keys(selectedFilters).length > 0) {
      setIsLoadingApplyFilters(true)
      handleApplyFilters()
    } else {
      // Reset filters and records states if no filters are selected
      setFirstLoad(true)
      setIsLoadingApplyFilters(true)
      setFiltersPayload(undefined)
    }
  }, [selectedFilters, handleApplyFilters])

  useEffect(() => {
    if (!filtersPayload || !firstLoad) {
      return
    }

    if (
      (resourcesFiltersNetworkStatus === NetworkStatus.ready &&
        placeholdersFiltersNetworkStatus === NetworkStatus.ready &&
        resourcesFiltersData) ||
      (!isApplyFilters && placeholdersFiltersNetworkStatus === NetworkStatus.ready && !firstLoad)
    ) {
      const expandPlaceholdersIds = placeholderFiltersData?.placeholderFilters.items.map((item) => item.id) ?? []
      const userIds =
        filtersPayload && filtersPayload.users?.length > 0
          ? [...filtersPayload.users]
          : [...resourcesIdsVar(), ...placeholdersIdsVar(), ...expandPlaceholdersIds]
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      refetchAssignments({
        params: {
          startDate: activeDates.startDate,
          endDate: activeDates.endDate,
          workCode: activeUser?.altairNumber,
          userLoggedInExternalId: activeUser?.altairNumber,
          userIds,
          brandIds: filtersPayload?.brands,
          taskStates: filtersPayload?.taskStates,
          usersGroupIds: filtersPayload?.usersGroups,
          projectIds: filtersPayload?.projects,
          taskIds: filtersPayload?.tasks,
          isExpandedPlaceholderApplied: !isApplyFilters
        }
      })
        .then(() => {
          setIsLoadingApplyFilters(false)
          setIsActiveDatesChanged(false)
        })
        .catch(() => {
          setIsLoadingApplyFilters(false)
          setIsActiveDatesChanged(false)
          showAlertVar({ show: true, message: 'An error has occurred while applying filters' })
        })
    }
  }, [
    resourcesFiltersNetworkStatus,
    placeholdersFiltersNetworkStatus,
    resourcesFiltersData,
    activeDates.endDate,
    activeDates.startDate,
    filtersPayload,
    activeUser,
    isApplyFilters,
    firstLoad,
    placeholderFiltersData?.placeholderFilters.items,
    refetchAssignments
  ])

  useEffect(() => {
    const styles = generateGridStyles(currentColumns)
    injectStyles(styles, `grid-styles-${currentColumns}`)
  }, [currentColumns])

  useEffect(() => {
    const fetchUserLLid = async () => {
      try {
        const userLLid = await getUserLLid()
        setUserLLid(userLLid)
      } catch (error) {
        console.log('Error', error)
      }
    }
    void fetchUserLLid()
  }, [])

  useEffect(() => {
    if (currentResourceData) {
      userInfoVar(currentResourceData.getResourceById as Resource)
    }
  }, [currentResourceData])

  useEffect(() => {
    const updateHeight = () => {
      const header = headerRef.current
      if (header) {
        const headerHeight = header.getBoundingClientRect().height
        const placeholderHeight = headerHeight + 50 + 'px'
        setPlaceholderHeight(placeholderHeight)
      }
    }
    // waits until the DOM is updated
    setTimeout(() => {
      updateHeight()
    }, 400)
  }, [isPlaceholderExpanded])

  const placeholderAssignments = useMemo(() => {
    // save scroll positions before filtering
    const placeHolderContainer = placeholderContainerRef.current
    if (placeHolderContainer) placeholderScrollPositionRef.current = placeHolderContainer.scrollTop

    const resourceContainer = resourceContainerRef.current
    if (resourceContainer) resourceScrollPositionRef.current = resourceContainer.scrollTop

    const filteredAssignments =
      assignmentsData?.assignments.filter((assignment: Assignment) => assignment.isPlaceholder) ?? []

    // filter assignments by selected placeholder org structure filter
    if (
      selectedPlaceholderOrgStructureFilter?.businessUnits?.length &&
      selectedPlaceholderOrgStructureFilter.businessUnits?.length > 0
    ) {
      return filteredAssignments.filter(
        (assignment: Assignment) =>
          selectedPlaceholderOrgStructureFilter.businessUnits
            ?.map((bu) => bu.agencyCode)
            .includes(assignment.agencyCode) ??
          selectedPlaceholderOrgStructureFilter.costCenters
            ?.map((d) => d.costCenterCode)
            .includes(assignment.costCenterCode) ??
          selectedPlaceholderOrgStructureFilter.locations?.includes(assignment.locationName)
      )
    }

    // scroll to the previous position after filtering
    requestAnimationFrame(() => {
      const resourceContainer = resourceContainerRef.current
      if (resourceContainer) resourceContainer.scrollTop = resourceScrollPositionRef.current
    })

    requestAnimationFrame(() => {
      const placeHolderContainer = placeholderContainerRef.current
      if (placeHolderContainer) placeHolderContainer.scrollTop = placeholderScrollPositionRef.current
    })
    return filteredAssignments
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [assignmentsData?.assignments, selectedPlaceholderOrgStructureFilter])

  const placeholderWithAssignments = useMemo(() => {
    const filteredPlaceholders = placeholdersRecords.filter((placeholder: Placeholder) =>
      placeholderAssignments.some((assignment: Assignment) => assignment.userId === placeholder.id)
    )
    return filteredPlaceholders
  }, [placeholderAssignments, placeholdersRecords])

  useEffect(() => {
    // Update resourcesRecords and placeholdersRecords when resourcesData or resourcesFiltersData change
    if (resourcesData) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      setResourcesRecords(resourcesData.resources.items)
    }
    if (resourcesFiltersData) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      setResourcesRecords(resourcesFiltersData.resourcesByFilters.items)
    }
  }, [resourcesData, resourcesFiltersData])

  useEffect(() => {
    // Update resourcesRecords and placeholdersRecords when resourcesData or resourcesFiltersData change
    if (placeholdersData) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      setPlaceholdersRecords(placeholdersData.placeholders.items)
    }
    if (placeholderFiltersData) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      setPlaceholdersRecords(placeholderFiltersData.placeholderFilters.items)
    }
  }, [placeholdersData, placeholderFiltersData])

  // useEffect(() => {
  //   if (resourcesNetworkStatus === NetworkStatus.loading) {
  //     setResourcesRecords([])
  //   }
  // }, [resourcesNetworkStatus, assignmentsNetworkStatus])

  useEffect(() => {
    setResourcesRecords([])
    setIsActiveDatesChanged(true)
  }, [activeDates.startDate, activeDates.endDate, activeSort])

  useEffect(() => {
    if (
      !resourcesDataLoading &&
      !resourcesFiltersDataLoading &&
      !assignmentsDataLoading &&
      !placeholdersDataLoading &&
      !placeholdersFiltersDataLoading
    ) {
      setIsLoadingApplyFilters(false)
    }
  }, [
    assignmentsDataLoading,
    placeholdersDataLoading,
    placeholdersFiltersDataLoading,
    resourcesDataLoading,
    resourcesFiltersDataLoading
  ])

  useEffect(() => {
    const numOfWeeks = localStorage.getItem('numOfWeeks')
    if (numOfWeeks) {
      const previousActiveDates = activeDatesVar()
      numOfWeeksVar(Number(numOfWeeks) as CalendarViewOptions)
      activeDatesVar(getDateRange(Number(numOfWeeks), previousActiveDates.startDate))
    }
  }, [])

  return (
    <Box key={`${renderKey}`} sx={{ padding: 3, paddingRight: 0, paddingBottom: 0, overflow: 'hidden' }}>
      <Grid container>
        <Box
          sx={{ backgroundColor: palette.grey['50'], width: '100%', position: 'sticky', left: 0, top: 0, zIndex: 1 }}
          ref={headerRef}
        >
          <Box sx={{ backgroundColor: palette.grey['50'], width: '100%' }}>
            <Header />
            <Grid display={'flex'} alignItems={'flex-end'}>
              <Grid sx={{ width: '20%', minWidth: '18.125rem', backgroundColor: palette.grey['50'] }}>
                {!isLightUser && <CalendarSorting />}
              </Grid>
              <Grid sx={{ width: '79%', backgroundColor: palette.grey['50'] }}>
                {calendarDataError && (
                  <Typography>
                    An error has occurred while loading the calendar header: <b>{calendarDataError.message}</b>
                  </Typography>
                )}
                {calendarDataLoading && <CalendarDatesLoader numOfCols={currentColumns} />}
                {calendarData && <CalendarHeader calendarData={calendarData.getCalendarData} />}
              </Grid>
            </Grid>
          </Box>
          <Box sx={{ backgroundColor: palette.common.white, width: '100%' }}>
            {!isLightUser && (
              <PlaceholderCollapse length={placeholderWithAssignments?.length ?? 0}>
                <Grid
                  item
                  xs={12}
                  sx={{ backgroundColor: 'white', maxHeight: '30vh', overflowX: 'hidden', overflowY: 'auto' }}
                  ref={placeholderContainerRef}
                >
                  <ScrollOnIntersect direction="up" showPlaceholder={true} containerRef={placeholderContainerRef} />
                  {placeholderAssignments.length > 0 &&
                    placeholderWithAssignments.length > 0 &&
                    !isLoadingApplyFilters &&
                    placeholderWithAssignments.map((placeholder: Placeholder) => {
                      const resource = placeholdersRecords.find((r: Placeholder) => r.id === placeholder.id)
                      const assignmentsByResource =
                        placeholderAssignments.filter((a: Assignment) => a.userId === resource?.id) ?? []

                      return (
                        <Box key={placeholder.id}>
                          <PrepareRow
                            assignmentsByResource={assignmentsByResource}
                            resourcesData={null}
                            placeholdersData={placeholder}
                            capacityMultipliers={capacityMultipliers}
                            weeksSelector={weeksSelector}
                            type={'placeholder'}
                            assignmentsData={assignmentsByResource}
                          ></PrepareRow>
                        </Box>
                      )
                    })}
                  <ScrollOnIntersect direction="down" showPlaceholder={true} containerRef={placeholderContainerRef} />
                </Grid>
                {(placeholdersError ?? placeholdersFiltersError) && (
                  <Typography>
                    An error has occurred while loading the placeholders:{' '}
                    <b>{placeholdersError?.message ?? placeholdersFiltersError?.message}</b>
                  </Typography>
                )}
                {(isLoadingApplyFilters || placeholdersDataLoading || placeholdersFiltersDataLoading) && (
                  <Box data-testid="calendar-body-loader-ph">
                    <CalendarBodyLoader numOfRows={mainLoaderRows} />
                  </Box>
                )}
              </PlaceholderCollapse>
            )}
          </Box>
        </Box>
        <Box
          sx={{
            backgroundColor: palette.common.white,
            overflowY: 'scroll',
            overflowX: 'hidden',
            height: `calc(100vh - ${placeholderHeight})`,
            width: '100%'
          }}
          ref={resourceContainerRef}
        >
          <ScrollOnIntersect direction="up" showPlaceholder={false} containerRef={resourceContainerRef} />
          {assignmentsData &&
            !isLoadingApplyFilters &&
            !isActiveDatesChanged &&
            resourcesRecords.map((resource: Resource, index: number) => {
              const assignmentsByResource = (assignmentsData?.assignments ?? []).filter(
                (a: Assignment) => a.userId === resource.id
              )

              return (
                <Box key={resource.id}>
                  {generalLoading && (
                    <Box data-testid="calendar-body-loader-ph">
                      <CalendarBodyLoader numOfRows={mainLoaderRows} />
                    </Box>
                  )}
                  {!generalLoading && (
                    <PrepareRow
                      assignmentsByResource={assignmentsByResource}
                      resourcesData={resource}
                      placeholdersData={null}
                      capacityMultipliers={capacityMultipliers}
                      weeksSelector={weeksSelector}
                      type={'resource'}
                      assignmentsData={assignmentsByResource}
                    ></PrepareRow>
                  )}
                </Box>
              )
            })}
          {(resourcesError ?? resourcesFiltersError) && (
            <Typography>
              An error has occurred while loading the resources data:{' '}
              <b>{resourcesError?.message ?? resourcesFiltersError?.message}</b>
            </Typography>
          )}

          {resourcesRecords.length === 0 ||
            resourcesDataLoading ||
            resourcesFiltersDataLoading ||
            (isLoadingApplyFilters && (
              <Box data-testid="calendar-body-loader-res">
                <CalendarBodyLoader numOfRows={mainLoaderRows} />
              </Box>
            ))}
          {assignmentsError && (
            <Typography>
              An error has ocurred while loading the assignments data: <b>{assignmentsError.message}</b>
            </Typography>
          )}
          {((!assignmentsData && assignmentsDataLoading) || isLoadingApplyFilters || isActiveDatesChanged) && (
            <Box>
              <CalendarBodyLoader numOfRows={mainLoaderRows} />
            </Box>
          )}
          <ScrollOnIntersect direction="down" showPlaceholder={false} containerRef={resourceContainerRef} />
        </Box>
        <CalendarPagination
          totalPages={pagination.totalPages}
          pageNumber={pagination.pageNumber}
          onChangePage={handleChangePage}
        />
      </Grid>
    </Box>
  )
}

export default memo(CalendarBody)
