import type { FC } from 'react'
import { memo, useEffect, useState } from 'react'
import { useScroll } from '../../../context/scrollContext'
import { useReactiveVar } from '@apollo/client'
import { isExpandedVar } from '@gql/client/localState'

interface ScrollOnIntersectProps {
  direction: 'up' | 'down'
  speed?: number
  zoneHeight?: number
  showPlaceholder?: boolean
  containerRef?: React.RefObject<HTMLDivElement>
}

const ScrollOnIntersect: FC<ScrollOnIntersectProps> = ({
  direction,
  speed = 8,
  zoneHeight = 50,
  showPlaceholder,
  containerRef
}) => {
  const [showDragAreas, setShowDragAreas] = useState<boolean>()
  const isPlaceholderExpanded = useReactiveVar(isExpandedVar)
  const { dragging } = useScroll()

  const handleMouseEnter = () => {
    const scrollStep = direction === 'up' ? -speed : speed
    if (containerRef?.current) {
      const container = containerRef.current
      const scrollTop = container.scrollTop
      const scrollHeight = container.scrollHeight
      const clientHeight = container.clientHeight

      if (direction === 'up' && scrollTop > 0) {
        container.scrollTop = Math.max(scrollTop + scrollStep, 0)
      } else if (direction === 'down' && scrollTop < scrollHeight - clientHeight) {
        container.scrollTop = Math.min(scrollTop + scrollStep, scrollHeight - clientHeight)
      }
    } else {
      window.scrollBy(0, scrollStep)
    }
  }

  useEffect(() => {
    setTimeout(() => {
      setShowDragAreas(dragging)
    }, 1000)
  }, [dragging])

  return (
    <div
      onMouseMove={handleMouseEnter}
      role="none"
      style={{
        height: `${zoneHeight}px`,
        width: '100%',
        zIndex: 2,
        position: 'fixed',

        top: (() => {
          if (showDragAreas && direction === 'up') {
            let topValue
            if (showPlaceholder) {
              topValue = '100px'
            } else {
              topValue = isPlaceholderExpanded ? '380px' : '160px'
            }
            return topValue
          }
          return direction === 'up' ? '-100%' : ''
        })(),
        bottom: (() => {
          if (showDragAreas && direction === 'down') {
            return showPlaceholder ? '350px' : '40px'
          }
          if (direction === 'down') {
            return '-100%'
          }
          return ''
        })(),
        left: 0
      }}
    ></div>
  )
}

export default memo(ScrollOnIntersect)
