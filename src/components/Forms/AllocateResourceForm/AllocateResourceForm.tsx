// React imports
import { useState, memo, useEffect, useCallback } from 'react'

// UI library imports
import {
  Dialog,
  DialogContent,
  DialogActions,
  Divider,
  Button,
  Box,
  Typography,
  Autocomplete,
  FormControl,
  FormLabel,
  LionDateRangePicker,
  OutlinedInput,
  palette,
  TextField,
  CircularProgress,
  LinearProgress
} from 'lion-ui'

// Icons import
import CloseIcon from '@mui/icons-material/Close'
import SeeDetails from '../Common/SeeDetails/SeeDetails'

import ResourceAdditionalDetails from '../Common/ResourceAdditionalDetails/ResourceAdditionalDetails'

// Apollo import
import { useReactiveVar, useQuery, useMutation, useLazyQuery } from '@apollo/client'
import type { ApolloError } from '@apollo/client'
import {
  showAlertVar,
  userInfoVar,
  userProfileVar,
  activeDatesVar,
  renderKeyVar,
  brandIdsVar,
  generalLoadingStateVar,
  placeholderSettingsSaveVar
} from '@gql/client/localState'

// Type props import
import { type AllocateResourceFormProps } from './AllocateResourceForm.props'
import { type AllocateResourceFormErrors, type AllocateResourceFormData } from '@typeDefs/Forms'
import { type Project } from '@typeDefs/Project'
import { type Task } from '@typeDefs/Task'
import { type SearchResourcePlaceholder } from '@typeDefs/SearchResourcePlaceholder'
import { type ResourceHolidayAndTimeOffDetails, type ResourceOrgStructure } from '@typeDefs/Resource'

// Utils import
import { gplError } from '@utils/gplError'
import {
  filterAndMapNonWorkingDates,
  generateWarningMessage,
  getWeekendDates,
  handleDateChange,
  handleHoursChange,
  mergeDistinctNonWorkingDates
} from '../Common/Handlers/FormHandlers'
import { UserProfile } from '@constants/userProfile'

// GQL queries and mutations imports
import { GET_RESOURCE_AND_PLACEHOLDER } from '@gql/queries/searchResourcePlaceholder'
import {
  PROJECTS_BY_PROJECT_MANAGER_QUERY,
  PROJECTS_BY_RESOURCE_MANAGER_QUERY,
  PROJECTS_BY_PROJECT_ID_QUERY
} from '@gql/queries/projects.query'
import { TASK_BY_PROJECT_QUERY } from '@gql/queries/tasks.query'
import { PLACEHOLDERS_QUERY } from '@gql/queries/placeholders.query'
import { CREATE_ASSIGNMENT_MUTATION } from '@gql/mutations/assignment.mutation'
import { ASSIGNMENTS_QUERY } from '@gql/queries/assignments.query'
import {
  HOLIDAYS_AND_TIMEOFFDETAILS_QUERY,
  ORG_STRUCTURE_AGENCY_COST_CENTER_QUERY,
  RESOURCES_QUERY
} from '@gql/queries/resources.query'

import dayjs from 'dayjs'
import PlaceholderAdditionalDetails from '../Common/PlaceholderAdditionalDetails/PlaceholderAdditionalDetails'
import { NonWorkingDatesTypes } from '@constants/nonWorkingDatesTypes'
import getLocaleDateFormat from '@utils/getLocaleDateFormat'

const AllocateResourceForm: React.FC<AllocateResourceFormProps> = ({ open, onClose }: AllocateResourceFormProps) => {
  const defaultProjectOption: Project | null = null
  const defaultTaskOption: Project | null = null
  const defaultAssignToOption: SearchResourcePlaceholder | null = null
  const userProfile = useReactiveVar(userProfileVar)
  const userInfo = useReactiveVar(userInfoVar)
  const activeUser = useReactiveVar(userInfoVar)
  const expandPlaceholdersSaved = useReactiveVar(placeholderSettingsSaveVar)
  const [searchTermName, setSearchTermName] = useState('')
  const [initialAssignToData, setInitialAssignToData] = useState<SearchResourcePlaceholder[]>([])
  const expandedPlaceholder = useReactiveVar(placeholderSettingsSaveVar)
  const [projectOrgData, setProjectOrgData] = useState<ResourceOrgStructure | null | undefined>(null)

  const [allocateResourceFormData, setAllocateResourceFormData] = useState<AllocateResourceFormData>({
    project: defaultProjectOption,
    task: defaultTaskOption,
    hoursPerDay: userInfo?.minimumAgencyHoursPerDay ?? 0,
    duration: [new Date(), new Date()],
    assignTo: defaultAssignToOption,
    nonWorkingDates: []
  })

  const [allocateResourceFormErrors, setAllocateResourceFormErrors] = useState<AllocateResourceFormErrors>({
    project: '',
    task: '',
    hoursPerDay: '',
    duration: '',
    assignTo: ''
  })

  const [resourceOrgStructureData, setResourceOrgStructureData] = useState<ResourceOrgStructure | null | undefined>({
    agencyName: '',
    costCenterName: '',
    locationName: '',
    agencyCode: '',
    costCenterCode: ''
  })

  const [resourceOrgStructure, setResourceOrgStructure] = useState<
    { data: ResourceOrgStructure | undefined | null; loading: boolean } | null | undefined
  >(null)

  const {
    data: projects,
    error: projectsError,
    loading: projectsLoading
  } = useQuery<{ getProjectsByProjectManagerWorkCode?: Project[]; getProjectsByResourceManagerId?: Project[] }>(
    userProfile === UserProfile.PROJECT_MANAGER
      ? PROJECTS_BY_PROJECT_MANAGER_QUERY
      : PROJECTS_BY_RESOURCE_MANAGER_QUERY,
    {
      variables: {
        workCode: activeUser?.altairNumber,
        isExpandedPlaceholderApplied:
          (userProfile === UserProfile.RESOURCE_MANAGER && expandPlaceholdersSaved) || expandedPlaceholder
      },
      onError: (error) => {
        gplError(error, projectsError)
      },
      onCompleted: (data) => {
        const profileKey =
          userProfile === UserProfile.PROJECT_MANAGER
            ? 'getProjectsByProjectManagerWorkCode'
            : 'getProjectsByResourceManagerId'
        const brandIds = data[profileKey]?.map((item) => item.brandId).filter((item) => item) ?? []
        brandIdsVar([...new Set(brandIds)])
      },
      fetchPolicy: 'network-only'
    }
  )

  const options =
    userProfile === UserProfile.PROJECT_MANAGER
      ? (projects?.getProjectsByProjectManagerWorkCode ?? [])
      : (projects?.getProjectsByResourceManagerId ?? [])

  const {
    data: tasks,
    error: tasksError,
    loading: tasksLoading
  } = useQuery<{ getTasksByProjectId: Task[] }>(TASK_BY_PROJECT_QUERY, {
    variables: { projectId: allocateResourceFormData?.project?.id },
    onError: (error) => {
      gplError(error, tasksError)
    },
    skip: !allocateResourceFormData?.project?.id || allocateResourceFormData?.project.id === ''
  })

  const {
    data: assignToData,
    error: assignToDataError,
    loading: assignToDataLoading
  } = useQuery<{ getResourceAndPlaceholder: SearchResourcePlaceholder[] }>(GET_RESOURCE_AND_PLACEHOLDER, {
    variables: { params: { userId: activeUser?.altairNumber, searchName: searchTermName } },
    onError: (error) => {
      gplError(error, assignToDataError)
    },
    onCompleted(data) {
      if (initialAssignToData.length === 0) {
        setInitialAssignToData(data.getResourceAndPlaceholder)
      }
    }
  })

  const { error: resourceHolidayAndTimeOffDetailsError, loading: resourceHolidayAndTimeOffDetailsLoading } = useQuery<{
    getHolidaysAndTimeOffDetailsByResourceId: ResourceHolidayAndTimeOffDetails
  }>(HOLIDAYS_AND_TIMEOFFDETAILS_QUERY, {
    variables: {
      params: {
        resourceId: allocateResourceFormData.assignTo?.id,
        startDate: dayjs(allocateResourceFormData.duration[0]).format('YYYY-MM-DD'),
        endDate: dayjs(allocateResourceFormData.duration[1]).format('YYYY-MM-DD')
      }
    },
    skip:
      !allocateResourceFormData.assignTo?.id ||
      !allocateResourceFormData.duration ||
      allocateResourceFormData.assignTo?.isPlaceholder, // Skip query if data is missing
    onError: (error) => {
      gplError(error, resourceHolidayAndTimeOffDetailsError)
    },
    onCompleted: ({ getHolidaysAndTimeOffDetailsByResourceId }) => {
      const holidays = filterAndMapNonWorkingDates(
        (getHolidaysAndTimeOffDetailsByResourceId?.holidays ?? []).map((holiday) => ({ date: holiday.holidayDate })),
        allocateResourceFormData.duration[0],
        allocateResourceFormData.duration[1],
        NonWorkingDatesTypes.HOLIDAY
      )

      const timeOffDetails = filterAndMapNonWorkingDates(
        (getHolidaysAndTimeOffDetailsByResourceId?.timeOffDetails ?? []).map((timeOff) => ({
          date: timeOff.absenceDate
        })),
        allocateResourceFormData.duration[0],
        allocateResourceFormData.duration[1],
        NonWorkingDatesTypes.ABSENCE
      )

      const distinctNonWorkingDates = mergeDistinctNonWorkingDates([...holidays, ...timeOffDetails])

      if (distinctNonWorkingDates.length) {
        setAllocateResourceFormData((prev) => ({ ...prev, nonWorkingDates: distinctNonWorkingDates }))
      }
    }
  })
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [fetchProjectDetails, { loading: projectDetailsLoading, error: projectOrgError }] = useLazyQuery(
    PROJECTS_BY_PROJECT_ID_QUERY,
    {
      onError: (error: ApolloError) => {
        gplError(error, projectOrgError)
      }
    }
  )

  const [fetchOrgStructure] = useLazyQuery(ORG_STRUCTURE_AGENCY_COST_CENTER_QUERY, {
    onError: (error: ApolloError) => {
      console.error('Error fetching organization structure:', error)
    }
  })

  const shouldRenderPlaceholderDetails =
    allocateResourceFormData?.project?.id &&
    !projectDetailsLoading &&
    resourceOrgStructureData &&
    allocateResourceFormData.assignTo?.isPlaceholder

  const shouldResourceDetailsRender =
    allocateResourceFormData?.assignTo && !allocateResourceFormData?.assignTo?.isPlaceholder

  const externalLink = process.env.WF_SERVER_URL

  const [createAssignmentMutation, { loading }] = useMutation(CREATE_ASSIGNMENT_MUTATION, {})

  const handleClose = () => {
    setResourceOrgStructure({ data: null, loading: false })
    onClose(false)
  }

  const checkFormErrors = (): boolean => {
    return (
      !!allocateResourceFormErrors.project ||
      !!allocateResourceFormErrors.task ||
      !!allocateResourceFormErrors.assignTo ||
      !!allocateResourceFormErrors.hoursPerDay ||
      allocateResourceFormData.project === null ||
      allocateResourceFormData.task === null ||
      dayjs(getLocaleDateFormat(allocateResourceFormData?.task?.taskStartDate ?? '')).toDate() >
        allocateResourceFormData.duration[0] ||
      dayjs(getLocaleDateFormat(allocateResourceFormData?.task?.taskEndDate ?? '')).toDate() <
        allocateResourceFormData.duration[1] ||
      allocateResourceFormData.assignTo === null ||
      allocateResourceFormData.hoursPerDay === 0
    )
  }

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (checkFormErrors()) {
      return
    }

    generalLoadingStateVar(true)

    const weekends = getWeekendDates(allocateResourceFormData.duration[0], allocateResourceFormData.duration[1])

    const params = {
      taskExternalId: allocateResourceFormData.task?.taskId,
      userAssignedExternalId: allocateResourceFormData.assignTo?.id,
      userLoggedInExternalId: activeUser?.altairNumber,
      startDate: dayjs(allocateResourceFormData.duration[0]).format('YYYY-MM-DD'),
      dueDate: dayjs(allocateResourceFormData.duration[1]).format('YYYY-MM-DD'),
      hoursPerDay: allocateResourceFormData.hoursPerDay,
      calendarStartDate: activeDatesVar().startDate,
      calendarDueDate: activeDatesVar().endDate,
      createAssignmentDate: dayjs().toISOString(),
      agencyName: resourceOrgStructure?.data?.agencyName,
      costCenterName: resourceOrgStructure?.data?.costCenterName,
      locationName: resourceOrgStructure?.data?.locationName,
      agencyCode: resourceOrgStructure?.data?.agencyCode,
      costCenterCode: resourceOrgStructure?.data?.costCenterCode,
      nonWorkingDates: mergeDistinctNonWorkingDates([
        ...(allocateResourceFormData.nonWorkingDates ?? []),
        ...(weekends ?? [])
      ])
    }

    const isNew = isNewResource(allocateResourceFormData.assignTo)

    const isPlaceholder = allocateResourceFormData.assignTo?.isPlaceholder ?? false
    setSearchTermName('')
    const refetchQueries = isNew
      ? isPlaceholder
        ? [PLACEHOLDERS_QUERY, ASSIGNMENTS_QUERY, GET_RESOURCE_AND_PLACEHOLDER]
        : [RESOURCES_QUERY, ASSIGNMENTS_QUERY, GET_RESOURCE_AND_PLACEHOLDER]
      : [ASSIGNMENTS_QUERY]

    createAssignmentMutation({
      variables: { params },
      refetchQueries,
      awaitRefetchQueries: true,
      onCompleted: () => {
        renderKeyVar(Date.now())
        generalLoadingStateVar(false)
      }
    }).catch((error: ApolloError) => {
      showAlertVar({ show: true, message: error.message })
      generalLoadingStateVar(false)
    })

    handleClose()
  }

  if ((projectsError ?? tasksError) && open) {
    showAlertVar({ show: true, message: 'An error has occurred while loading the form values' })
  }

  const isNewResource = useCallback(
    (selectedResource: SearchResourcePlaceholder | null): boolean => {
      if (!selectedResource) return false

      // Check if the selected resource is in the initial assignToData
      const isResourceInInitialData = initialAssignToData.some((resource) => resource.id === selectedResource.id)
      return !isResourceInInitialData
    },
    [initialAssignToData]
  )

  useEffect(() => {
    setAllocateResourceFormData((prevData) => ({ ...prevData, hoursPerDay: userInfo?.minimumAgencyHoursPerDay }))
  }, [userInfo])

  const handleTaskChange = (value: Task | null) => {
    if (value?.taskStartDate && value?.taskEndDate) {
      setAllocateResourceFormData((prev) => ({
        ...prev,
        task: value,
        duration: [
          dayjs(getLocaleDateFormat(String(value.taskStartDate))).toDate(),
          dayjs(getLocaleDateFormat(String(value.taskEndDate))).toDate()
        ]
      }))
    } else {
      setAllocateResourceFormData((prev) => ({
        ...prev,
        task: value
      }))
    }
  }

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="lg">
      <span
        style={{
          display: loading ? 'inline-block' : 'none',
          position: 'absolute',
          width: '100%',
          height: '100%',
          top: 0,
          right: 0,
          zIndex: 1,
          background: 'white',
          opacity: 0.3
        }}
      ></span>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', padding: '1.45rem' }}>
        <Typography variant="h2">Allocate resource</Typography>

        <Button onClick={handleClose} variant="text" data-testid="close-icon" sx={{ minWidth: '0px' }}>
          <CloseIcon />
        </Button>
      </Box>
      <Divider />

      <DialogContent sx={{ width: '42.5rem' }}>
        <form data-testid="form-allocate-resource" id="form" onSubmit={handleSubmit}>
          <Box sx={{ maxHeight: '55vh' }}>
            <FormControl fullWidth>
              <Box sx={{ margin: '0.7rem 0' }}>
                <FormLabel
                  className={'MuiFormLabel-asterisk'}
                  required
                  sx={{
                    fontWeight: 'bold',
                    color: palette.primary.dark,
                    padding: '0.7rem 0',
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    '& .MuiFormLabel-asterisk': { color: palette['500-Accent-3'].main }
                  }}
                  htmlFor="project"
                >
                  Project
                </FormLabel>
                <Autocomplete
                  sx={{ marginTop: '0.5rem' }}
                  fullWidth
                  disablePortal
                  id="project"
                  data-testid="project"
                  options={options}
                  renderInput={(params) => (
                    <TextField
                      error={allocateResourceFormErrors.project !== ''}
                      {...params}
                      placeholder="Search for a project..."
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {projectsLoading ? (
                              <CircularProgress size={20} sx={{ color: '#999', marginRight: '8px' }} />
                            ) : null}
                            {params.InputProps.endAdornment}
                          </>
                        )
                      }}
                    />
                  )}
                  size="small"
                  isOptionEqualToValue={(option, value) => option === value}
                  value={allocateResourceFormData.project}
                  onChange={(_e, value) => {
                    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                    setAllocateResourceFormData({ ...allocateResourceFormData, project: value!, task: null })

                    if (value?.id) {
                      // eslint-disable-next-line @typescript-eslint/no-floating-promises
                      fetchProjectDetails({ variables: { projectId: value.id } }).then((response) => {
                        const projectData = response.data?.getProjectsByProjectId
                        if (projectData) {
                          fetchOrgStructure({
                            variables: {
                              params: {
                                costCenterCode: projectData[0].costCenterCode,
                                agencyCode: projectData[0].agencyCode
                              }
                            }
                          })
                            .then((orgResponse) => {
                              const orgData = orgResponse.data?.getOrgStructureByAgencyCodeCostCenterCode
                              if (orgData) {
                                // Update the remaining fields in resourceOrgStructureData
                                setResourceOrgStructureData((prev) => ({
                                  ...prev,
                                  costCenterCode: '',
                                  agencyCode: projectData[0].agencyCode ?? '',
                                  agencyName: orgData.agencyName ?? '',
                                  costCenterName: '',
                                  locationName: ''
                                }))

                                setProjectOrgData({
                                  costCenterCode: '',
                                  agencyCode: projectData[0].agencyCode ?? '',
                                  agencyName: orgData.agencyName ?? '',
                                  costCenterName: '',
                                  locationName: ''
                                })
                              }
                            })
                            .catch((error) => {
                              console.error('Error fetching organization structure:', error)
                              // showAlertVar({ show: true, message: 'Failed to fetch organization structure' })
                            })
                        }
                      })
                    }
                  }}
                  onBlur={() => {
                    setAllocateResourceFormErrors({
                      ...allocateResourceFormErrors,
                      project: allocateResourceFormData.project ? '' : 'Project is required'
                    })
                  }}
                  clearOnBlur={true}
                  defaultValue={defaultProjectOption}
                />
                {allocateResourceFormData.project && (
                  <SeeDetails
                    externalLink={`${externalLink}/project/${allocateResourceFormData.project.integrationId}/overview`}
                  />
                )}
                {allocateResourceFormErrors.project && (
                  <Typography color="error"> {allocateResourceFormErrors.project}</Typography>
                )}
              </Box>

              <Box sx={{ margin: '0.7rem 0' }}>
                <FormLabel
                  className={'MuiFormLabel-asterisk'}
                  required
                  sx={{
                    fontWeight: 'bold',
                    color: palette.primary.dark,
                    my: 3,
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    '& .MuiFormLabel-asterisk': { color: palette['500-Accent-3'].main }
                  }}
                  htmlFor="task"
                  disabled={!allocateResourceFormData.project}
                >
                  Task
                </FormLabel>
                <Autocomplete
                  sx={{
                    marginTop: '0.5rem',
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    '.Mui-disabled': {
                      pointerEvents: 'auto',
                      background: palette.grey['100'],
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      '&:hover': { cursor: 'not-allowed' },
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      '&::placeholder': {
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        WebkitTextFillColor: palette.grey[500],
                        opacity: 1
                      }
                    }
                  }}
                  fullWidth
                  disablePortal
                  id="task"
                  data-testid="task"
                  options={tasks?.getTasksByProjectId ?? []}
                  renderInput={(params) => (
                    <TextField
                      error={allocateResourceFormErrors.task !== ''}
                      {...params}
                      placeholder="Select a project to view tasks"
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {tasksLoading ? (
                              <CircularProgress size={20} sx={{ color: '#999', marginRight: '8px' }} />
                            ) : null}
                            {params.InputProps.endAdornment}
                          </>
                        )
                      }}
                    />
                  )}
                  size="small"
                  isOptionEqualToValue={(option, value) => option.id === value?.id}
                  disabled={!allocateResourceFormData.project}
                  value={allocateResourceFormData.task}
                  onChange={(e, value) => {
                    handleTaskChange(value)
                  }}
                  defaultValue={defaultTaskOption}
                  clearOnBlur={true}
                  onBlur={() => {
                    setAllocateResourceFormErrors({
                      ...allocateResourceFormErrors,
                      task: allocateResourceFormData.task ? '' : 'Task is required'
                    })
                  }}
                />
                {allocateResourceFormData.task && (
                  <SeeDetails externalLink={`${externalLink}/task/${allocateResourceFormData.task.taskId}/overview`} />
                )}
                {allocateResourceFormErrors.task && (
                  <Typography color="error"> {allocateResourceFormErrors.task}</Typography>
                )}
              </Box>

              <Box
                sx={{
                  display: 'flex',
                  flex: '1 1 0rem',
                  alignItems: 'flex-start',
                  justifyContent: 'space-between',
                  margin: '0.7rem 0'
                }}
              >
                <Box sx={{ width: '47%' }}>
                  <FormLabel
                    className={'MuiFormLabel-asterisk'}
                    required
                    sx={{
                      display: 'block',
                      fontWeight: 'bold',
                      color: palette.primary.dark,
                      my: 3,
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      '& .MuiFormLabel-asterisk': { color: palette['500-Accent-3'].main }
                    }}
                    htmlFor="hoursPerDay"
                  >
                    Hours per Day
                  </FormLabel>
                  <OutlinedInput
                    id="hoursPerDay"
                    data-testid="hoursPerDay"
                    name="hoursPerDay"
                    type="number"
                    value={allocateResourceFormData.hoursPerDay}
                    onChange={handleHoursChange(setAllocateResourceFormData, setAllocateResourceFormErrors)}
                    size="small"
                    placeholder="Must be more than 0"
                    sx={{ width: '100%' }}
                    error={Boolean(allocateResourceFormErrors.hoursPerDay)}
                  />
                  {allocateResourceFormErrors.hoursPerDay && (
                    <Typography color="error">{allocateResourceFormErrors.hoursPerDay}</Typography>
                  )}
                </Box>
                <Box sx={{ width: '49%' }}>
                  <Typography
                    variant="body1"
                    fontWeight="bolder"
                    component="label"
                    htmlFor="duration"
                    sx={{ display: 'block', my: 3 }}
                  >
                    Duration{' '}
                    <Typography fontWeight="bolder" component="span" color={palette['500-Accent-3'].main}>
                      *
                    </Typography>
                  </Typography>
                  <Box data-testid="dateRangePicker">
                    <LionDateRangePicker
                      key={
                        allocateResourceFormData.duration[0]?.toISOString() +
                        '-' +
                        allocateResourceFormData.duration[1]?.toISOString()
                      }
                      locale={navigator.language.toLowerCase()}
                      startDate={allocateResourceFormData.duration[0]}
                      endDate={allocateResourceFormData.duration[1]}
                      onDateChange={handleDateChange(setAllocateResourceFormData, allocateResourceFormData)}
                      monthsInView={2}
                      minDate={
                        allocateResourceFormData.task?.taskStartDate
                          ? dayjs(getLocaleDateFormat(allocateResourceFormData?.task?.taskStartDate ?? '')).toDate()
                          : null
                      }
                      maxDate={
                        allocateResourceFormData.task?.taskEndDate
                          ? dayjs(getLocaleDateFormat(allocateResourceFormData?.task?.taskEndDate ?? '')).toDate()
                          : null
                      }
                    />

                    {resourceHolidayAndTimeOffDetailsLoading && (
                      <Box>
                        <LinearProgress />
                      </Box>
                    )}

                    {generateWarningMessage(allocateResourceFormData) && (
                      <Box
                        sx={{
                          background: `${palette.info.light}33`,
                          marginTop: '5px',
                          borderRadius: '10px',
                          padding: '4px'
                        }}
                      >
                        <Typography fontWeight={'bold'} fontSize={'small'} color={palette.info.dark}>
                          Note:{' '}
                          <span style={{ color: palette.primary.dark, fontWeight: '500' }}>
                            The selected date range includes {generateWarningMessage(allocateResourceFormData)}. Please
                            review carefully!
                          </span>
                        </Typography>
                      </Box>
                    )}
                  </Box>
                </Box>
              </Box>

              <Box sx={{ margin: '0.7rem 0' }}>
                <FormLabel
                  className={'MuiFormLabel-asterisk'}
                  required
                  sx={{
                    fontWeight: 'bolder',
                    color: palette.primary.dark,
                    my: 3,
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    '& .MuiFormLabel-asterisk': { color: palette['500-Accent-3'].main }
                  }}
                  htmlFor="assignTo"
                >
                  Assign To
                </FormLabel>
                <Autocomplete
                  sx={{ marginTop: '0.5rem' }}
                  fullWidth
                  disablePortal
                  id="assignTo"
                  data-testid="assignTo"
                  options={assignToData?.getResourceAndPlaceholder ?? []}
                  getOptionLabel={(option) => option?.name}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      error={allocateResourceFormErrors.assignTo !== ''}
                      placeholder="Search for a resource..."
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {assignToDataLoading ? (
                              <CircularProgress size={20} sx={{ color: '#999', marginRight: '8px' }} />
                            ) : null}
                            {params.InputProps.endAdornment}
                          </>
                        )
                      }}
                    />
                  )}
                  size="small"
                  isOptionEqualToValue={(option, value) => option.id === value.id}
                  onChange={(_e, value) => {
                    setAllocateResourceFormData((prev) => ({
                      prev,
                      ...allocateResourceFormData,
                      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                      assignTo: value!,
                      nonWorkingDates: []
                    }))
                    if (value?.isPlaceholder) {
                      setTimeout(() => {
                        setResourceOrgStructureData(projectOrgData)
                      }, 0)
                    } else {
                      setResourceOrgStructure(undefined)
                      setResourceOrgStructureData(undefined)
                      setResourceOrgStructure({ data: undefined, loading: false })
                    }
                  }}
                  onInputChange={(_event, value, reason) => {
                    if (reason === 'clear') {
                      setSearchTermName('')
                      setAllocateResourceFormData((prev) => ({
                        prev,
                        ...allocateResourceFormData,
                        assignTo: null,
                        nonWorkingDates: []
                      }))
                    } else if (reason === 'input') {
                      if (value.length >= 3) {
                        setSearchTermName(value)
                      } else {
                        setSearchTermName('')
                        setAllocateResourceFormData((prev) => ({
                          prev,
                          ...allocateResourceFormData,
                          assignTo: null,
                          nonWorkingDates: []
                        }))
                      }
                    }
                  }}
                  onBlur={() => {
                    setAllocateResourceFormErrors({
                      ...allocateResourceFormErrors,
                      assignTo: allocateResourceFormData.assignTo ? '' : 'Assign to is required'
                    })
                  }}
                />
                {allocateResourceFormErrors.assignTo && (
                  <Typography color="error"> {allocateResourceFormErrors.assignTo}</Typography>
                )}
                {allocateResourceFormData.assignTo && (
                  <SeeDetails
                    externalLink={`${externalLink}/${allocateResourceFormData.assignTo.isPlaceholder ? 'jobrole' : 'user'}/${allocateResourceFormData.assignTo.integrationId}/details`}
                  />
                )}
              </Box>

              {/* Additional Details */}
              <Box>
                <Box
                  sx={{
                    marginTop: '1rem',
                    display: 'flex',
                    padding: '0',
                    alignItems: 'center',
                    gap: '1rem',
                    alignSelf: 'stretch'
                  }}
                >
                  <Typography variant="OverlineS" sx={{ letterSpacing: '0.0625rem', textTransform: 'uppercase' }}>
                    Additional Details
                  </Typography>
                  <Divider
                    sx={{
                      display: 'flex',
                      height: '0.0625rem',
                      justifyContent: 'center',
                      alignItems: 'center',
                      flex: ' 1 0 0'
                    }}
                  ></Divider>
                </Box>
              </Box>

              {shouldRenderPlaceholderDetails && (
                <PlaceholderAdditionalDetails
                  updateResourceOrgStructure={setResourceOrgStructure}
                  resourceOrgStructureData={resourceOrgStructureData}
                />
              )}
              {/* <PlaceholderAdditionalDetails /> */}

              {shouldResourceDetailsRender && (
                <ResourceAdditionalDetails
                  assignToId={allocateResourceFormData?.assignTo?.id}
                  updateResourceOrgStructure={setResourceOrgStructure}
                  resourceOrgStructure={resourceOrgStructure?.data}
                />
              )}
            </FormControl>
          </Box>
        </form>
      </DialogContent>
      <Divider />

      <Box sx={{ display: 'flex', justifyContent: 'space-between', padding: '1rem' }}>
        <DialogActions sx={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
          <Button sx={{ mx: 1 }} variant="outlined" onClick={handleClose}>
            Close
          </Button>

          <Button
            type="submit"
            form="form"
            sx={{ mx: 1 }}
            disabled={
              !resourceOrgStructure ||
              loading ||
              resourceHolidayAndTimeOffDetailsLoading ||
              checkFormErrors() ||
              resourceOrgStructure?.loading
            }
          >
            Allocate Resource{' '}
            {loading && (
              <CircularProgress
                size={24}
                sx={{
                  color: palette.primary.main,
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  marginTop: '-12px',
                  marginLeft: '-12px'
                }}
              />
            )}
          </Button>
        </DialogActions>
      </Box>
    </Dialog>
  )
}

export default memo(AllocateResourceForm)
