import type React from 'react'
import { useState } from 'react'
import { Box, Accordion, palette, Typography, AccordionDetails, AccordionSummary } from 'lion-ui'
import type AuditTrailContentEntryProps from './AuditTrailContentEntry.props'
import ChevronIcon from '@components/Icons/ChevronIcon/ChevronIcon'
import AuditTrailCaseRenderer from '@components/AuditTrail/AuditTrailCaseRenderer/AudiTrailCaseRenderer'
import getLocaleDateFormat from '@utils/getLocaleDateFormat'

const AuditTrailContentEntry = ({ onDelete, entry }: AuditTrailContentEntryProps): React.JSX.Element => {
  const [isOpen, setIsOpen] = useState(false)
  const formattedEntryDate = getLocaleDateFormat(entry.date)

  const handleExpand = (event: React.MouseEvent<HTMLDivElement>): void => {
    setIsOpen((prev) => !prev)
  }

  return (
    <Box sx={{ width: '100%' }}>
      <Accordion
        sx={{
          border: `1px solid ${palette.grey[200]}`,
          borderRadius: '16px!important',
          padding: '0px 12px',
          margin: '8px 0px 8px 0px!important',
          boxShadow: 'none'
        }}
      >
        <AccordionSummary
          expandIcon={<ChevronIcon width="18" height="18" fillColor={palette.primary.main} />}
          aria-controls={`panel${entry.id}-content`}
          id={`panel${entry.id}-header`}
          onClick={handleExpand}
          className="audit-trail-accordion-summary"
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography sx={{ fontSize: '0.875rem', marginRight: '2.1rem', fontWeight: 'bold' }} component="span">
                {formattedEntryDate}
              </Typography>
              <Typography sx={{ fontSize: '0.875rem', marginRight: '1.5rem', fontWeight: 'bold' }} component="span">
                {entry.time.slice(0, 5)}
              </Typography>
              <AuditTrailCaseRenderer entry={entry} isOpen={isOpen} isCollapsed />
            </Box>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <AuditTrailCaseRenderer entry={entry} isOpen={false} />
        </AccordionDetails>
      </Accordion>
    </Box>
  )
}

export default AuditTrailContentEntry
