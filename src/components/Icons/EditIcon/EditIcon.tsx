import { type IconProps } from '../Icon.props'

const TaskNotesIcon = ({ height, width }: IconProps) => {
  return (
    <svg width={width} height={height} viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.57741 0.243834C9.90285 -0.0816034 10.4305 -0.0816034 10.7559 0.243834L14.7559 4.24383C15.0814 4.56927 15.0814 5.09691 14.7559 5.42234L5.42259 14.7557C5.26631 14.912 5.05435 14.9998 4.83333 14.9998H0.833333C0.373096 14.9998 0 14.6267 0 14.1664V10.1664C0 9.94541 0.0877973 9.73345 0.244078 9.57717L9.57741 0.243834ZM1.66667 10.5116V13.3331H4.48816L9.98807 7.83317L7.16658 5.01168L1.66667 10.5116ZM8.34509 3.83317L11.1666 6.65466L12.9882 4.83309L10.1667 2.0116L8.34509 3.83317Z"
        fill="#202020"
      />
    </svg>
  )
}

export default TaskNotesIcon
