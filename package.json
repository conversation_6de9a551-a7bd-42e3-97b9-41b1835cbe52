{"private": true, "scripts": {"dev": "next", "build": "next build", "build:dev": "cross-env APP_ENV=development next build", "build:qa": "cross-env APP_ENV=qa next build", "build:staging": "cross-env APP_ENV=staging next build", "build:production": "cross-env APP_ENV=prod next build", "start": "next start", "type-check": "tsc", "test": "jest", "coverage": "npx jest --coverage", "lint-all": "eslint . --fix --max-warnings=0 --no-ignore", "lint:all": "eslint . --fix --max-warnings=0 --no-ignore", "lint": "eslint --fix --max-warnings=0 --no-ignore", "format-all": "prettier . --write", "format": "prettier --write", "prepare": "npx husky install", "pre-commit": "lint-staged"}, "lint-staged": {"*.{ts,tsx}": ["npm run format", "npm run lint", "npm test -- --findRelatedTests"]}, "dependencies": {"@adobe/uix-guest": "^1.0.1", "@apollo/client": "^3.10.7", "@azure/msal-browser": "^4.12.0", "@azure/msal-react": "^3.0.12", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.16.0", "@mui/material": "^5.16.0", "@reduxjs/toolkit": "^1.3.6", "autosuggest-highlight": "^3.3.4", "cross-fetch": "^4.0.0", "dayjs": "^1.11.11", "dotenv": "^16.4.5", "eslint-config-standard-with-typescript": "^43.0.1", "eslint-plugin-testing-library": "^6.2.2", "graphql": "^16.9.0", "gridstack": "^10.3.0", "jest-transform-stub": "^2.0.0", "jwt-decode": "^4.0.0", "lion-ui": "1.11.1", "next": "latest", "react": "^18.3.1", "react-dom": "^18.3.1", "react-redux": "^7.2.0", "react-virtuoso": "^4.7.11"}, "devDependencies": {"@next/eslint-plugin-next": "^14.0.2", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^13.5.0", "@types/autosuggest-highlight": "^3.2.3", "@types/jest": "^27.0.1", "@types/lodash": "^4.17.12", "@types/node": "^16.9.1", "@types/react": "^18.0.10", "@types/react-dom": "^18.0.5", "@types/react-redux": "^7.1.18", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-next": "^14.0.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jest-dom": "^5.4.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-promise": "^6.4.0", "eslint-plugin-react": "^7.33.2", "identity-obj-proxy": "^3.0.0", "jest": "^27.5.1", "jest-css-modules-transform": "^4.2.0", "lint-staged": "^15.2.10", "prettier": "^3.3.2", "sass": "^1.69.5", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "typescript": "^4.3.4"}}