import { type CalendarDayData } from '@typeDefs/Calendar'

export const mockCalendarData: CalendarDayData[] = [
  {
    dayShortName: 'Mo',
    dayMiddleName: 'Mon',
    dayNumber: 1,
    date: '2023-01-01',
    isWeekend: false
  },
  {
    dayShortName: 'Tu',
    dayMiddleName: 'Tue',
    dayNumber: 2,
    date: '2023-01-02',
    isWeekend: false
  },
  {
    dayShortName: 'We',
    dayMiddleName: 'Wed',
    dayNumber: 3,
    date: '2023-01-03',
    isWeekend: false
  },
  {
    dayShortName: 'Th',
    dayMiddleName: 'Thu',
    dayNumber: 4,
    date: '2023-01-04',
    isWeekend: false
  },
  {
    dayShortName: 'Fr',
    dayMiddleName: 'Fri',
    dayNumber: 5,
    date: '2023-01-05',
    isWeekend: false
  },
  {
    dayShortName: 'Sa',
    dayMiddleName: 'Sat',
    dayNumber: 6,
    date: '2023-01-06',
    isWeekend: false
  },
  {
    dayShortName: 'Su',
    dayMiddleName: 'Sun',
    dayNumber: 7,
    date: '2023-01-07',
    isWeekend: false
  }
]
