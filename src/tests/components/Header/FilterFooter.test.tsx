import { render, screen } from '@testing-library/react'
import FilterFooter from '@components/Header/FilterFooter/FilterFooter'
import type FilterFooterProps from '@components/Header/FilterFooter/FilterFooter.props'

describe('FilterFooter', () => {
  const defaultProps: FilterFooterProps = {
    enableFilter: false,
    filterAction: jest.fn(),
    buttonLabel: 'Apply Filters'
  }

  it('renders without crashing', () => {
    render(<FilterFooter {...defaultProps} />)
    expect(screen.getByText('Apply Filters')).toBeInTheDocument()
  })
})
