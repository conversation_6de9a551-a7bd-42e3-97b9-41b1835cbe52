import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import mockAuditTrailEntry from '../../__mocks__/auditTrailEntry.mock'
import UpdateAssignmentStatus from '@components/AuditTrail/AuditTrailContentCases/UpdateAssignmentStatus'

// Mock dependencies
jest.mock('@components/Icons/UserIcon/UserIcon', () => jest.fn(() => <div data-testid="UserIcon" />))
jest.mock('@components/Icons/TaskIcon/TaskIcon', () => jest.fn(() => <div data-testid="TaskIcon" />))
jest.mock('@components/AuditTrail/AuditTrailGoToDetails/AuditTrailGoToDetails', () =>
  jest.fn(() => <div data-testid="GoToDetails" />)
)

describe('UpdateAssignmentStatus Component', () => {
  const mockEntry = mockAuditTrailEntry

  test('renders correctly when isCollapsed is false', () => {
    render(<UpdateAssignmentStatus entry={mockEntry} isOpen={false} isCollapsed={false} />)

    expect(screen.getByTestId('UserIcon')).toBeInTheDocument()
    expect(screen.getByTestId('TaskIcon')).toBeInTheDocument()
    expect(screen.getByTestId('GoToDetails')).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.newAssignment.resourceName)).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.newAssignment.taskName)).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.newAssignment.projectName)).toBeInTheDocument()
  })

  test('renders correctly when isCollapsed is true and isOpen is false', () => {
    render(<UpdateAssignmentStatus entry={mockEntry} isOpen={false} isCollapsed={true} />)

    expect(screen.getByTestId('UserIcon')).toBeInTheDocument()
    expect(screen.getByTestId('TaskIcon')).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.newAssignment.resourceName)).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.newAssignment.taskName)).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.newAssignment.projectName)).toBeInTheDocument()
    expect(screen.getByText(/Approved request for/i)).toBeInTheDocument()
  })

  test('renders correctly when isCollapsed is true and isOpen is true', () => {
    render(<UpdateAssignmentStatus entry={mockEntry} isOpen={true} isCollapsed={true} />)

    expect(screen.getByText(/Approved request for/i)).toBeInTheDocument()
  })
})
