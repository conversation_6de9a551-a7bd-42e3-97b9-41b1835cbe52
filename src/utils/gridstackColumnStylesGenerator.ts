function getInstanceIdentifier(): string {
  if (typeof window === 'undefined') return 'server'

  const hostname = window.location.hostname

  if (hostname.includes('dev1')) return 'dev1'
  if (hostname.includes('qa1')) return 'qa1'
  if (hostname.includes('uat')) return 'uat'
  if (hostname === 'roar.marcel.ai') return 'prod'
  if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) return 'local'

  return 'default'
}

const instanceId = getInstanceIdentifier()

export const generateGridStyles = (columns: number): string => {
  let styles = `.gs-${columns} > .grid-stack-item { width: ${100 / columns}%; }`

  for (let i = 1; i <= columns; i++) {
    styles += `
        .gs-${columns} > .grid-stack-item[gs-x='${i}'] { left: ${(100 / columns) * i}%; }
        .gs-${columns} > .grid-stack-item[gs-w='${i}'] { width: ${(100 / columns) * i}%; }
      `
  }

  return styles
}

export const injectStyles = (styles: string, id: string): void => {
  if (typeof document !== 'undefined') {
    const isInIframe = window.self !== window.top
    const finalId = isInIframe ? `${id}-iframe-${instanceId}` : id

    let styleElement = document.getElementById(finalId) as HTMLStyleElement
    if (!styleElement) {
      styleElement = document.createElement('style')
      styleElement.id = finalId
      if (isInIframe) {
        styleElement.setAttribute('data-iframe-instance', instanceId)
        styleElement.setAttribute('data-environment', instanceId)
      }
      document.head.appendChild(styleElement)
    }
    styleElement.textContent = styles
  }
}
