import { employeeCodeVar, userProfileVar } from '@gql/client/localState'
import { getAccessToken } from '@auth/msalUtil'

// Mock dependencies
jest.mock('@gql/client/localState', () => ({
  employeeCodeVar: jest.fn(),
  userProfileVar: jest.fn(),
  typePolicies: {}
}))

jest.mock('@auth/msalUtil', () => ({
  getAccessToken: jest.fn()
}))

// Mock Apollo Client dependencies to prevent actual initialization
jest.mock('@apollo/client', () => ({
  // eslint-disable-next-line @typescript-eslint/naming-convention
  ApolloClient: jest.fn(),
  // eslint-disable-next-line @typescript-eslint/naming-convention
  InMemoryCache: jest.fn(),
  // eslint-disable-next-line @typescript-eslint/naming-convention
  HttpLink: jest.fn(),
  from: jest.fn()
}))

jest.mock('@apollo/client/link/context', () => ({
  setContext: jest.fn()
}))

jest.mock('@apollo/client/link/error', () => ({
  onError: jest.fn()
}))

jest.mock('cross-fetch', () => jest.fn())

describe('Apollo Client Auth Link Logic', () => {
  const mockEmployeeCodeVar = employeeCodeVar as jest.MockedFunction<typeof employeeCodeVar>
  const mockUserProfileVar = userProfileVar as jest.MockedFunction<typeof userProfileVar>
  const mockGetAccessToken = getAccessToken as jest.MockedFunction<typeof getAccessToken>

  beforeEach(() => {
    jest.clearAllMocks()
    mockUserProfileVar.mockReturnValue('lightUser')
  })

  // Test the auth link logic directly
  const createAuthLinkFunction = () => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return async (_: any, { headers }: { headers: Record<string, string> }) => {
      const token = await getAccessToken()

      if (token == null || token === '') {
        return { headers }
      }

      const employeeCode = employeeCodeVar()
      const authHeaders: Record<string, string> = {
        ...headers,
        authorization: `Bearer ${token}`,
        profile: userProfileVar()
      }

      // Add x-empCode header if employeeCode is available
      if (employeeCode) {
        authHeaders['x-empCode'] = employeeCode
      }

      return {
        headers: authHeaders
      }
    }
  }

  it('should include x-empCode header when employeeCode is available', async () => {
    // Setup mocks
    mockGetAccessToken.mockResolvedValue('mock-token')
    mockEmployeeCodeVar.mockReturnValue('EMP123')

    const authLinkFunction = createAuthLinkFunction()
    const result = await authLinkFunction(
      {},
      {
        headers: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          'existing-header': 'value'
        }
      }
    )

    expect(result).toEqual({
      headers: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'existing-header': 'value',
        authorization: 'Bearer mock-token',
        profile: 'lightUser',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'x-empCode': 'EMP123'
      }
    })
  })

  it('should not include x-empCode header when employeeCode is empty', async () => {
    // Setup mocks
    mockGetAccessToken.mockResolvedValue('mock-token')
    mockEmployeeCodeVar.mockReturnValue('')

    const authLinkFunction = createAuthLinkFunction()
    const result = await authLinkFunction(
      {},
      {
        headers: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          'existing-header': 'value'
        }
      }
    )

    expect(result).toEqual({
      headers: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'existing-header': 'value',
        authorization: 'Bearer mock-token',
        profile: 'lightUser'
      }
    })
    // eslint-disable-next-line @typescript-eslint/naming-convention
    expect(result.headers['x-empCode']).toBeUndefined()
  })

  it('should return headers without auth when no token', async () => {
    // Setup mocks
    mockGetAccessToken.mockResolvedValue(undefined)
    mockEmployeeCodeVar.mockReturnValue('EMP123')

    const authLinkFunction = createAuthLinkFunction()
    const result = await authLinkFunction(
      {},
      {
        headers: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          'existing-header': 'value'
        }
      }
    )

    expect(result).toEqual({
      headers: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'existing-header': 'value'
      }
    })
  })
})
