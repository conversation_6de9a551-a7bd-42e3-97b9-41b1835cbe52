import { type IconProps } from '../Icon.props'

const PlaceholderIcon = ({ height, width, rotation, fillColor }: IconProps) => {
  return (
    <svg
      style={{ transform: `rotate(${rotation}deg)` }}
      width={height}
      height={width}
      viewBox="0 0 12 14"
      fill={fillColor}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.00082 9.32096C7.46892 9.32096 7.86298 9.16244 8.18299 8.84542C8.50301 8.52841 8.66302 8.13496 8.66302 7.66507C8.66302 7.19519 8.50125 6.80071 8.1777 6.48163C7.85415 6.16255 7.46129 6.00301 6.99913 6.00301C6.53103 6.00301 6.1401 6.16446 5.82634 6.48734C5.51256 6.81023 5.35567 7.20443 5.35567 7.66994C5.35567 8.13849 5.51284 8.5308 5.82718 8.84686C6.14151 9.16292 6.53272 9.32096 7.00082 9.32096ZM3.85567 12.6446H10.163V12.069C10.163 11.7438 10.0706 11.4416 9.88584 11.1624C9.70106 10.8831 9.45866 10.6712 9.15866 10.5267C8.80704 10.3892 8.45508 10.2815 8.10279 10.2038C7.75051 10.1261 7.3829 10.0872 6.99998 10.0872C6.61705 10.0872 6.24944 10.1261 5.89716 10.2038C5.54487 10.2815 5.20269 10.3892 4.87063 10.5267C4.55107 10.6712 4.30202 10.8831 4.12348 11.1624C3.94494 11.4416 3.85567 11.7438 3.85567 12.069V12.6446ZM11.7959 15.8533H2.20406C1.79519 15.8533 1.43304 15.6981 1.11763 15.3876C0.802231 15.0771 0.644531 14.7125 0.644531 14.2938V1.70683C0.644531 1.28515 0.802231 0.918044 1.11763 0.605506C1.43304 0.292982 1.79519 0.136719 2.20406 0.136719H8.01574C8.22279 0.136719 8.42311 0.17735 8.61671 0.258612C8.81031 0.339887 8.98444 0.45795 9.13911 0.6128L12.8793 4.36361C13.0369 4.51234 13.1575 4.68333 13.2409 4.87657C13.3243 5.06981 13.366 5.26974 13.366 5.47638V14.2938C13.366 14.7125 13.2073 15.0771 12.8898 15.3876C12.5723 15.6981 12.2077 15.8533 11.7959 15.8533ZM11.7959 14.2938V5.52352L7.95433 1.70683H2.20406V14.2938H11.7959Z"
        fill="#202020"
      />
    </svg>
  )
}

export default PlaceholderIcon
