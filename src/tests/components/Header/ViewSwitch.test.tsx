import { render, fireEvent, screen } from '@testing-library/react'
import ViewSwitch from '@components/Header/ViewSwitch/ViewSwitch'

describe('ViewSwitch', () => {
  it('should render the ViewSwitch component', () => {
    render(<ViewSwitch />)
    const viewSwitchElement = screen.getByRole('combobox')
    expect(viewSwitchElement).toBeInTheDocument()
  })

  it('should change the active view when a different option is selected', () => {
    render(<ViewSwitch />)
    const selectElement = screen.getByRole('combobox')
    fireEvent.mouseDown(selectElement)
    const fourWeeksOption = screen.getByText('4 Weeks')
    fireEvent.click(fourWeeksOption)

    const selectedOptions = screen.getAllByText('4 Weeks')
    expect(selectedOptions.length).toEqual(2)
  })
})
