import { typePolicies } from '@gql/client/localState/typePolicies'
import { userProfileVar } from '@gql/client/localState/userProfile'
import { numOfWeeksVar } from '@gql/client/localState/numOfWeeks'
import { activeDatesVar } from '@gql/client/localState/activeDates'

jest.mock('@gql/client/localState/userProfile', () => ({
  userProfileVar: jest.fn()
}))

jest.mock('@gql/client/localState/numOfWeeks', () => ({
  numOfWeeksVar: jest.fn()
}))

jest.mock('@gql/client/localState/activeDates', () => ({
  activeDatesVar: jest.fn()
}))

describe('typePolicies', () => {
  beforeEach(() => {
    ;(userProfileVar as unknown as jest.Mock).mockReturnValue('mockUserProfile')
    ;(numOfWeeksVar as unknown as jest.Mock).mockReturnValue(4)
    ;(activeDatesVar as unknown as jest.Mock).mockReturnValue('2023-01-01')
  })

  it('should return correct value from userProfileVar', () => {
    // eslint-disable-next-line @typescript-eslint/unbound-method
    const readFunction = typePolicies.activeProfile.fields.activeProfile.read
    expect(readFunction()).toBe('mockUserProfile')
  })

  it('should return correct value from numOfWeeksVar', () => {
    // eslint-disable-next-line @typescript-eslint/unbound-method
    const readFunction = typePolicies.numOfWeeks.fields.numOfWeeks.read
    expect(readFunction()).toBe(4)
  })

  it('should return correct value from activeDatesVar', () => {
    // eslint-disable-next-line @typescript-eslint/unbound-method
    const readFunction = typePolicies.activeDates.fields.activeDates.read
    expect(readFunction()).toBe('2023-01-01')
  })
})
