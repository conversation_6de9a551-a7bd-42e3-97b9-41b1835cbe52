import { useEffect } from 'react'
import { useMsal, useIsAuthenticated } from '@azure/msal-react'
import { useRouter } from 'next/router'
import { useReactiveVar } from '@apollo/client'
import { wflionLoginIDVar } from '@gql/client/localState'

const MSALAuth = () => {
  const { instance } = useMsal()
  const isAuthenticated = useIsAuthenticated()
  const router = useRouter()

  const wflionLoginID = useReactiveVar(wflionLoginIDVar)

  const getScope = () => {
    if (typeof window !== 'undefined' && window.location.hostname === 'roar.marcel.ai') {
      // TODO: Remove this when we have a proper prod environment
      return 'api://82cf01b1-9445-4388-86cb-f5b01bcf6d99/user_api_access'
    }
    return 'api://a0ce10c3-4f71-4f9a-bff6-db67e89174b1/user_api_access'
  }

  useEffect(
    () => {
      const handleRedirectPromise = async (): Promise<void> => {
        try {
          const response = await instance.handleRedirectPromise()

          if (response) {
            console.log('%cAuth Response from Redirect', 'color: green; font-size: 3em;', response)
          } else if (!isAuthenticated) {
            // Step 2: Attempt SSO silent login
            try {
              const authResponse = await instance.ssoSilent({
                loginHint: wflionLoginID !== '' ? `${wflionLoginID}@publicisresources.com` : undefined,
                scopes: [getScope()]
              })

              console.log('%cAuth Response YYYY', 'color: green; font-size: 3em;', authResponse)

              const options = {
                method: 'GET',
                headers: {
                  authorization: `Bearer ${authResponse.accessToken}`
                }
              }

              try {
                const graphResponse = await fetch('https://graph.microsoft.com/v1.0/me', options)
                const json = await graphResponse.json()

                console.log('%cGraphResponse', 'font-size: 3em; color: blue;', json)

                if (json.error?.code === 'InvalidAuthenticationToken') {
                  router.reload()
                }
              } catch (error) {
                console.log('%cGraphError', 'color: red; font-size: 3em;', { error })
              }
            } catch (error) {
              console.log('%cSSO Silent Error', 'color: red; font-size: 3em;', { error })

              // Step 3: If SSO fails, initiate loginPopup
              void instance.loginPopup({
                scopes: [getScope()]
              })
            }
          }
        } catch (error) {
          console.log('%cRedirect Handling Error', 'color: red; font-size: 3em;', { error })
        }
      }

      void handleRedirectPromise()
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isAuthenticated, instance, wflionLoginID]
  )

  return <div>Loading...</div>
}

export default MSALAuth
