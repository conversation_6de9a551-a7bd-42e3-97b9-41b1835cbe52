import type React from 'react'
import { <PERSON>, Divider, IconButton, LinearProgress, palette, Typography } from 'lion-ui'
import type AuditTrailContentProps from './AuditTrailContent.props'
import AuditTrailIcon from '@components/Icons/AuditTrailIcon/AuditTrailIcon'
import CloseIcon from '@components/Icons/CloseIcon/CloseIcon'
import CloseCircleIcon from '@components/Icons/CloseCircleIcon/CloseCircleIcon'
import FilterIllustrationIcon from '@components/Icons/FilterIllustrationIcon/FilterIllustrationIcon'
import AuditTrailContentEntry from '../AuditTrailContentEntry/AuditTrailContentEntry'
import { useQuery, useMutation, useReactiveVar, type ApolloError } from '@apollo/client'
import { userInfoVar } from '@gql/client/localState'
import AUDIT_TRAIL_QUERY from '@gql/queries/AuditTrailEntries.query'
import { gplError } from '@utils/gplError'
import { type AuditTrailEntry } from '@typeDefs/AuditTrailEntry'
import { AuditTrailLoader } from '@components/Loaders/AuditTrailLoader/AuditTrailLoader'
import { CLEAR_AUDIT_TRAIL_MUTATION } from '@gql/mutations/auditTrail.mutation'

const AuditTrailContent = ({ onClose }: AuditTrailContentProps): React.JSX.Element => {
  const userInfo = useReactiveVar(userInfoVar)

  const handleCloseButton = () => {
    onClose()
  }

  const handleClearButton = async () => {
    try {
      await updateAssignmentMutation({
        variables: {
          userLoggedInExternalId: userInfo?.altairNumber
        }
      })
    } catch (error) {
      gplError(error as ApolloError, auditTrailDataError)
    }
  }

  const {
    data: auditTrailData,
    error: auditTrailDataError,
    loading: auditTrailDataLoading
  } = useQuery(AUDIT_TRAIL_QUERY, {
    variables: { userLoggedInExternalId: userInfo?.altairNumber },
    onError: (error) => {
      gplError(error, auditTrailDataError)
    },
    fetchPolicy: 'network-only'
  })

  const [updateAssignmentMutation, { loading: loadingClearAuditTrail }] = useMutation(CLEAR_AUDIT_TRAIL_MUTATION, {
    refetchQueries: [AUDIT_TRAIL_QUERY],
    awaitRefetchQueries: true
  })

  return (
    <Box
      sx={{
        width: '45rem'
      }}
    >
      <Box sx={{ padding: '1.45rem' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 4 }}>
            <AuditTrailIcon height="1.4rem" width="1.4rem" fillColor={palette.primary.main} />
            <Typography variant="h2">Recent history</Typography>
          </Box>

          <IconButton aria-label="Close audit trail" onClick={handleCloseButton}>
            <CloseIcon width="1.5rem" height="1.5rem" />
          </IconButton>
        </Box>
      </Box>
      <Divider />
      <Box
        sx={{
          padding: '1.45rem',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center'
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            width: '100%',
            marginBottom: '1rem',
            paddingLeft: '1.5rem',
            alignItems: 'center'
          }}
        >
          <Box
            sx={{
              display: 'flex',
              gap: 8,
              justifyContent: 'flex-start'
            }}
          >
            <Typography sx={{ fontSize: '0.75rem', marginRight: '2.5rem' }}>Date</Typography>
            <Typography sx={{ fontSize: '0.75rem' }}>Time</Typography>
            <Typography sx={{ fontSize: '0.75rem' }}>Action</Typography>
          </Box>

          <IconButton
            sx={{ borderRadius: '0.25rem', display: 'flex' }}
            disabled={loadingClearAuditTrail}
            aria-label="Clear audit trail"
            onClick={() => {
              void handleClearButton()
            }}
          >
            <CloseCircleIcon width="1rem" height="1rem" />
            <Typography
              sx={{ color: palette.primary.main, fontSize: '0.75rem', marginLeft: '0.5rem', fontWeight: '700' }}
            >
              Clear all
            </Typography>
          </IconButton>
        </Box>

        <Box sx={{ width: '100%' }}>
          {loadingClearAuditTrail && <LinearProgress />}
          <Divider />
        </Box>

        <Box
          data-testid="audit-trail-content-entry"
          sx={{
            maxHeight: '73.5vh',
            width: '100%',
            display: 'flex',
            flexDirection: 'column'
          }}
        >
          {auditTrailDataLoading && <AuditTrailLoader numOfRows={12} />}

          {!auditTrailDataLoading &&
            auditTrailData?.getAuditTrailForLoggedInUser.map((entry: AuditTrailEntry) => (
              <AuditTrailContentEntry
                entry={entry}
                key={entry.id}
                onDelete={() => {
                  console.log('delete')
                }}
              />
            ))}
        </Box>

        <Box
          sx={{
            maxHeight: '73.5vh',
            width: '100%',
            display: 'flex',
            flexDirection: 'column'
          }}
        >
          {!auditTrailDataLoading && !auditTrailData?.getAuditTrailForLoggedInUser.length && (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '60vh'
              }}
            >
              <FilterIllustrationIcon width="300" height="300" />
              <Typography sx={{ fontSize: '0.75rem' }}>No recent activity recorded yet.</Typography>
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  )
}

export default AuditTrailContent
