import { makeVar } from '@apollo/client'
import { type PlaceholdersSettingsPayload } from '@typeDefs/PlaceholdersSettingsPayload'

export const placeholderSettingsSaveVar = makeVar<boolean>(false)
export const placeholderSettingsResetVar = makeVar<boolean>(false)
export const placeholderSettingsPayloadVar = makeVar<PlaceholdersSettingsPayload>({})
export const businessUnitsEmptyVar = makeVar<boolean>(true)
export const isApplyFiltersVar = makeVar<boolean>(false)
