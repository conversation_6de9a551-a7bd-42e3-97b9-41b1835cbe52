// React import
import { useState, memo } from 'react'

// Lion ui import
import { Box, FormControl, MenuItem, palette, Select, type SelectChangeEvent } from 'lion-ui'

// Icons import
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown'
import SortIcon from '@mui/icons-material/Sort'

// Constants import
import { sortOptions } from '@constants/sortOptions'
import { type Sort } from '@typeDefs/Sort'

// GQL import
import { activeSortVar } from '@gql/client/localState'

const CalendarSorting = () => {
  const [selectedSortOption, setSelectedSortOption] = useState(activeSortVar)

  const sortChangeHandler = (option: SelectChangeEvent<string>) => {
    const newSelectedSortOption = sortOptions.find((sortOption) => sortOption.displayName === option.target.value)
    if (newSelectedSortOption) {
      setSelectedSortOption(newSelectedSortOption)
      activeSortVar(newSelectedSortOption)
    }
  }

  return (
    <FormControl
      fullWidth
      sx={{
        display: 'flex',
        paddingTop: 2,
        paddingBottom: 2,
        paddingLeft: 0,
        paddingRight: 4
      }}
    >
      <Select
        id="calendar-sort"
        aria-label="Sort options"
        onChange={sortChangeHandler}
        IconComponent={KeyboardArrowDownIcon}
        value={selectedSortOption?.displayName}
        sx={{
          borderRadius: '8px',
          height: '40px',
          backgroundColor: palette.common.white
        }}
        renderValue={(value: string) => {
          return (
            <Box sx={{ display: 'flex', gap: 1, color: palette.common.black }}>
              <SortIcon />
              {value}
            </Box>
          )
        }}
      >
        {sortOptions.map((option: Sort) => {
          return (
            <MenuItem key={option.displayName} value={option.displayName}>
              {option.displayName}
            </MenuItem>
          )
        })}
      </Select>
    </FormControl>
  )
}

export default memo(CalendarSorting)
