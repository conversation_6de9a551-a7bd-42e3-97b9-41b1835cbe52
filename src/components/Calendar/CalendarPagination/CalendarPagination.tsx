import { memo } from 'react'

import { Box, Pagination, PaginationItem, palette, Typography } from 'lion-ui'
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos'
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos'
import { type CalendarPaginationProps } from './CalendarPagination.prop'

const PreviousButton = () => (
  <Typography
    variant="BodyMBold"
    sx={{
      display: 'flex',
      alignItems: 'center'
    }}
  >
    <ArrowBackIosIcon /> Previous
  </Typography>
)

const NextButton = () => (
  <Typography
    variant="BodyMBold"
    sx={{
      display: 'flex',
      alignItems: 'center'
    }}
  >
    Next <ArrowForwardIosIcon />
  </Typography>
)

const CalendarPagination = ({ totalPages, pageNumber, onChangePage }: CalendarPaginationProps) => {
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        position: 'fixed',
        bottom: 0,
        zIndex: 10,
        borderTop: `1px solid ${palette.grey['200']}`,
        background: palette.grey['50'],
        alignItems: 'center',
        left: 0,
        height: '2.5rem',
        textAlign: 'center',
        width: '100%'
      }}
    >
      <Pagination
        count={totalPages}
        page={pageNumber}
        onChange={onChangePage}
        color="primary"
        renderItem={(item) => (
          <PaginationItem
            sx={{
              display: 'flex',
              alignItems: 'center'
            }}
            slots={{
              previous: PreviousButton,
              next: NextButton
            }}
            {...item}
          />
        )}
      />
    </Box>
  )
}

export default memo(CalendarPagination)
