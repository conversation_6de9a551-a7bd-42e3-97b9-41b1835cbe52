/* eslint-disable @typescript-eslint/no-explicit-any */
import dayjs from 'dayjs'

import { NonWorkingDatesTypes } from '@constants/nonWorkingDatesTypes'
import { type NonWorkingDate } from '@typeDefs/NonWorkingDate'

export const handleDateChange =
  (setFormData: React.Dispatch<React.SetStateAction<any>>, formData: any) =>
  (event: Array<{ startDate: Date; endDate: Date }> | undefined | Record<string, unknown>) => {
    if (event && Array.isArray(event) && event.length > 0) {
      const { startDate, endDate } = event[0]
      setFormData({ ...formData, duration: [startDate, endDate], nonWorkingDates: [] })
    }
  }

export const handleHoursChange =
  (setFormData: React.Dispatch<React.SetStateAction<any>>, setFormErrors: React.Dispatch<React.SetStateAction<any>>) =>
  (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    const parsedValue = parseFloat(value)

    setFormData((prevData: any) => ({
      ...prevData,
      [name]: parsedValue
    }))

    let errorMessage = ''
    if (value === '') {
      errorMessage = 'Hours Per Day is required'
    } else if (parsedValue < 0.1) {
      errorMessage = 'Hours Per Day must be greater than or equal to 0.1'
    } else if (parsedValue > 24) {
      errorMessage = 'Hours Per Day must be less than or equal to 24'
    } else if (!/^\d+(\.\d{1,2})?$/.test(value)) {
      errorMessage = 'Hours Per Day must be in two precisions'
    }
    setFormErrors((prevErrors: any) => ({
      ...prevErrors,
      hoursPerDay: errorMessage
    }))
  }

export const hasWeekend = (startDate: Date, endDate: Date): boolean => {
  const start = dayjs(startDate)
  const end = dayjs(endDate)

  if (!start.isValid() || !end.isValid()) {
    throw new Error('Invalid date format')
  }

  let current = start

  while (current.isBefore(end) || current.isSame(end, 'day')) {
    const dayOfWeek = current.day()

    if (dayOfWeek === 6 || dayOfWeek === 0) {
      return true
    }

    current = current.add(1, 'day')
  }

  return false
}

export const filterAndMapNonWorkingDates = (
  items: Array<{ date: string }>,
  startDate: Date,
  endDate: Date,
  reasonType: NonWorkingDatesTypes
): NonWorkingDate[] =>
  items
    .filter(
      (item) => dayjs(item.date).isBetween(startDate, endDate, null, '[]') // Include start and end dates
    )
    .map((item) => ({
      date: item.date,
      reason: reasonType,
      isWorking: false
    }))

export const mergeDistinctNonWorkingDates = (dates: NonWorkingDate[]): NonWorkingDate[] =>
  dates.reduce((acc: NonWorkingDate[], current) => {
    const existing = acc.find((item) => item.date === current.date && item.reason === current.reason)
    if (!existing) {
      acc.push(current)
    }
    return acc
  }, [])

export const generateWarningMessage = (formData: any): string => {
  const hasWeekends = hasWeekend(
    new Date(formData.duration[0] as string | number | Date),
    new Date(formData.duration[1] as string | number | Date)
  )
  const hasAbsences = (formData?.nonWorkingDates ?? []).length > 0

  if (hasWeekends && hasAbsences) {
    return 'weekends and user absences'
  }
  if (hasWeekends) {
    return 'weekends'
  }
  if (hasAbsences) {
    return 'user absences'
  }
  return ''
}

export const getWeekendDates = (startDateStr: Date, endDateStr: Date): NonWorkingDate[] => {
  const startDate = new Date(startDateStr)
  const endDate = new Date(endDateStr)
  const weekends: Array<{ date: string }> = []

  for (let d = new Date(startDate); d <= endDate; d = new Date(d.getFullYear(), d.getMonth(), d.getDate() + 1)) {
    const dayOfWeek = d.getDay()
    if (dayOfWeek === 0 || dayOfWeek === 6) {
      weekends.push({ date: dayjs(d).format('YYYY-MM-DD') })
    }
  }

  const weekendsDates = filterAndMapNonWorkingDates(weekends, startDate, endDate, NonWorkingDatesTypes.WEEK_END)

  return weekendsDates
}
