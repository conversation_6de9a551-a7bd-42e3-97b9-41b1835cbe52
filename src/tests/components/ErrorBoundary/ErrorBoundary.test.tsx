import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import ErrorBoundary from '@components/ErrorBoundary/ErrorBoundary'

// Mock ErrorFallback component
jest.mock('@components/ErrorBoundary/ErrorFallback', () => {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  return { ErrorFallback: jest.fn(() => <div>ErrorFallback Component</div>) }
})

describe('ErrorBoundary Component', () => {
  beforeEach(() => {
    // avoid login errors
    console.log = jest.fn()
  })

  afterAll(() => {
    jest.resetAllMocks()
  })

  const ChildComponent = () => {
    throw new Error('Test error')
  }

  test('renders ErrorFallback component when an error occurs', () => {
    render(
      <ErrorBoundary>
        <ChildComponent />
      </ErrorBoundary>
    )

    expect(screen.getByText('ErrorFallback Component')).toBeInTheDocument()
  })

  test('renders child components when no error occurs', () => {
    const NoErrorComponent = () => <div>No Error</div>

    render(
      <ErrorBoundary>
        <NoErrorComponent />
      </ErrorBoundary>
    )
    expect(screen.getByText('No Error')).toBeInTheDocument()
  })
})
