// React imports
import { useCallback, useMemo, type FC } from 'react'

// Apollo imports
import { numOfWeeksVar } from '@gql/client/localState'

// Constants imports
import { numsOfDaysPerWeek } from '@constants/timeOptions'

// UI components and styles
import { Box, Typography, palette } from 'lion-ui'
import HomeIcon from '@components/Icons/HomeIcon/HomeIcon'

// Utility functions
import isTodayDate from '@utils/isTodayDate'

// Type imports
import { type CalendarDayProps } from './CalendarDay.props'
import { CalendarViewOptions } from '@constants/calendarViewOptions'

const CalendarDay: FC<CalendarDayProps> = ({ day, month, index, isNewMonth }) => {
  const isToday = useCallback((date: string) => isTodayDate(date), [])
  const dayIsToday = isToday(day.date)
  const numOfWeeks = numOfWeeksVar()
  const numOfDaysInView = numOfWeeks * numsOfDaysPerWeek

  const calculateDayColor = useMemo(() => {
    if (dayIsToday) {
      return palette['500-Accent-1'].main
    }

    if (day.isWeekend) {
      return palette.error.main
    }

    return palette.common
  }, [dayIsToday, day.isWeekend])

  const calculateDayBorderBottomColor = useMemo(() => {
    if (dayIsToday) {
      return `2px solid ${palette['500-Accent-1'].main}`
    }

    if (day.isWeekend) {
      return `2px solid ${palette.error.main}`
    }

    return ''
  }, [dayIsToday, day.isWeekend])

  const isCurrentMonth = useMemo(() => {
    const currentDate = new Date()
    const currentMonth = currentDate.toLocaleString('en-US', { month: 'long' })
    return currentMonth === month
  }, [month])

  return (
    <Box
      sx={{
        width: `calc(100% / ${numOfDaysInView})`,
        position: 'relative',
        paddingTop: '1.5rem',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        borderLeft: index === 0 && isNewMonth ? `1px solid ${palette.secondary.main}` : 'unset'
      }}
    >
      {index === 0 && (
        <Typography
          variant="OverlineS"
          sx={{
            position: 'absolute',
            bottom: '2rem',
            left: '35%',
            ...(!isCurrentMonth
              ? { color: palette.grey['500'] }
              : { color: palette['500-Accent-1'].main, fontWeight: '600' })
          }}
        >
          {month}
        </Typography>
      )}

      <Box
        className="calendar-day"
        data-testid="Calendar.Header.Navigation.Day.Item"
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          padding: '0.35rem',
          width: '100%',
          height: '2rem',
          color: palette.common,
          borderBottom: calculateDayBorderBottomColor,
          background: dayIsToday ? palette['50-Accent-1'].main : ''
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {dayIsToday && numOfWeeks === CalendarViewOptions.TWO_WEEKS && (
            <Box
              sx={{
                color: palette['500-Accent-1'].main,
                display: 'flex',
                alignItems: 'center',
                marginRight: '0.25rem'
              }}
            >
              <HomeIcon height="0.75rem" width="0.75rem" />
            </Box>
          )}
          <Box
            sx={{
              color: calculateDayColor,
              ...(numOfWeeks !== CalendarViewOptions.TWO_WEEKS && {
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center'
              })
            }}
            data-testid={dayIsToday ? 'Calendar.Header.Navigation.Day.Item.Current' : ''}
          >
            <span style={{ marginRight: numOfWeeks === CalendarViewOptions.TWO_WEEKS ? '5px' : '0px' }}>
              {numOfWeeks === CalendarViewOptions.TWO_WEEKS ? day.dayMiddleName : day.dayShortName}
            </span>
            <span style={{ marginTop: numOfWeeks === CalendarViewOptions.TWO_WEEKS ? '0px' : '-5px' }}>
              {day.dayNumber}
            </span>
          </Box>
        </Box>
      </Box>
    </Box>
  )
}

export default CalendarDay
