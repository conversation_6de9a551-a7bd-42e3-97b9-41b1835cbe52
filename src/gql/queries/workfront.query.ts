import { gql } from '@apollo/client'

const WORKFRONT_USER_QUERY = gql`
  query GetWorkfrontUser($hostname: String, $imsToken: String, $userId: String) {
    getWorkfrontUser(hostname: $hostname, imsToken: $imsToken, userID: $userId) {
      id
      name
      objCode
      lionLoginID
    }
  }
`

const GET_USER_BY_LLID = gql`
  query GetUserByLLID($llid: String!) {
    getUserByLLID(llid: $llid) {
      employeeCode
      businessRole
      userName
    }
  }
`

export { WORKFRONT_USER_QUERY, GET_USER_BY_LLID }
