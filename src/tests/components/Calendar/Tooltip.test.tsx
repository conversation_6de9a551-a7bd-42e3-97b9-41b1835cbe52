import type React from 'react'
import { render } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import RMTooltip from '@components/Calendar/Tooltip/Tooltip'

describe('RMTooltip', () => {
  const theme = createTheme()

  const renderWithTheme = (ui: React.ReactElement) => {
    return render(<ThemeProvider theme={theme}>{ui}</ThemeProvider>)
  }

  test('renders correctly', () => {
    const { getByText } = renderWithTheme(
      <RMTooltip title="Test Tooltip">
        <span>Hover me</span>
      </RMTooltip>
    )

    expect(getByText('Hover me')).toBeInTheDocument()
  })
})
