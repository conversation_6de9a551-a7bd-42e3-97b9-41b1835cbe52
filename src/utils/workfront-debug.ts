import { getAuthEnvConfig } from './environments'
import { workfrontConfig } from './workfront'

export function debugWorkfrontConfig(): void {
  if (typeof window === 'undefined') {
    console.log('Workfront Debug: Running on server side')
    return
  }

  const envConfig = getAuthEnvConfig()

  console.group('Workfront Configuration Debug')

  // Environment Detection
  console.group('Environment Detection')
  console.log('Hostname:', window.location.hostname)
  console.log('Full URL:', window.location.href)
  console.log('Pathname:', window.location.pathname)
  console.log('Search params:', window.location.search)
  console.log('Detected environment:', envConfig.env)
  console.log('Is in iframe:', window.self !== window.top)

  try {
    console.log('Can access parent:', window.parent !== window.self)
    console.log('Parent hostname:', window.parent?.location?.hostname || 'Cannot access')
  } catch (e) {
    console.log('Cannot access parent (cross-origin):', e instanceof Error ? e.message : String(e))
  }
  console.groupEnd()

  // Workfront Configuration
  console.group('Workfront Configuration')
  console.log('Generated config:', workfrontConfig)
  console.log('URL Construction ID:', workfrontConfig.id)
  console.log('Adobe UIX ID:', workfrontConfig.uixId)
  console.log('Instance ID:', workfrontConfig.instanceId)
  console.log('Main Menu ID:', workfrontConfig.mainMenuId)
  console.log('UIX Main Menu ID:', workfrontConfig.uixMainMenuId)
  console.log('Menu Label:', workfrontConfig.menuLabel)
  console.log('Plugin URL:', workfrontConfig.url)
  console.log('Is in iframe:', workfrontConfig.isInIframe)
  console.groupEnd()

  // DOM Inspection
  console.group('🏗️ DOM Inspection')
  const existingStyles = document.querySelectorAll('style[data-iframe-instance]')
  console.log('Existing iframe-specific styles:', existingStyles.length)
  existingStyles.forEach((style, index) => {
    console.log(`Style ${index + 1}:`, {
      id: style.id,
      instance: style.getAttribute('data-iframe-instance'),
      environment: style.getAttribute('data-environment'),
      content: style.textContent?.substring(0, 100) + '...'
    })
  })

  // Check for Workfront-related elements
  const workfrontElements = document.querySelectorAll('[id*="roar-"], [id*="rm-"]')
  console.log('Workfront-related elements:', workfrontElements.length)
  workfrontElements.forEach((element, index) => {
    console.log(`Element ${index + 1}:`, {
      id: element.id,
      tagName: element.tagName,
      className: element.className
    })
  })
  console.groupEnd()

  // Adobe UIX Detection
  console.group('Adobe UIX Detection')
  console.log('Adobe UIX Guest available:', typeof window !== 'undefined' && 'AdobeUIXGuest' in window)
  console.log(
    'Window keys containing "adobe":',
    Object.keys(window).filter((key) => key.toLowerCase().includes('adobe'))
  )
  console.groupEnd()

  console.groupEnd()
}

/**
 * Checks for potential ID conflicts in the current document
 */
export function checkForIdConflicts(): void {
  if (typeof document === 'undefined') return

  console.group('ID Conflict Detection')

  const allElements = document.querySelectorAll('[id]')
  const idCounts = new Map<string, number>()

  allElements.forEach((element) => {
    const id = element.id
    idCounts.set(id, (idCounts.get(id) ?? 0) + 1)
  })

  const conflicts = Array.from(idCounts.entries()).filter(([_, count]) => count > 1)

  if (conflicts.length > 0) {
    console.warn('Found ID conflicts:')
    conflicts.forEach(([id, count]) => {
      console.warn(`- ID "${id}" appears ${count} times`)
      const elements = document.querySelectorAll(`#${id}`)
      elements.forEach((el, index) => {
        console.log(`  ${index + 1}:`, el)
      })
    })
  } else {
    console.log('No ID conflicts detected')
  }

  console.groupEnd()
}

/**
 * Monitors for changes in the DOM that might indicate iframe conflicts
 */
export function monitorIframeConflicts(): void {
  if (typeof window === 'undefined' || typeof MutationObserver === 'undefined') return

  console.log('Starting iframe conflict monitoring...')

  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element
            if (element.id && element.id.includes('roar-')) {
              console.log('New Workfront-related element added:', {
                id: element.id,
                tagName: element.tagName,
                className: element.className
              })
            }
          }
        })
      }
    })
  })

  observer.observe(document.body, {
    childList: true,
    subtree: true
  })

  // Stop monitoring after 30 seconds
  setTimeout(() => {
    observer.disconnect()
    console.log('Iframe conflict monitoring stopped')
  }, 30000)
}

// Auto-run debug on import in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Delay to ensure DOM is ready
  setTimeout(() => {
    debugWorkfrontConfig()
    checkForIdConflicts()
    monitorIframeConflicts()
  }, 1000)
}
