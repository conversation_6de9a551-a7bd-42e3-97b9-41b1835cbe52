import CalendarDragItemContent from '@components/Calendar/CalendarDragItemContent/CalendarDragItemContent'
import { mockAssignment } from '../../__mocks__/assignments.mock'
import { fireEvent, render, screen, waitFor } from '@testing-library/react'
import { MockedProvider } from '@apollo/client/testing'
import { UserProfile } from '@constants/userProfile'

// Create mock functions
const mockUserProfileVar = jest.fn()
const mockUserInfoVar = jest.fn()
const mockShouldCloseTooltip = jest.fn()

// Set default return values
mockUserProfileVar.mockReturnValue(UserProfile.PROJECT_MANAGER) // Non-light user to show context menu
mockUserInfoVar.mockReturnValue({ altairNumber: 'test-user-123' })
mockShouldCloseTooltip.mockReturnValue(false)

jest.mock('@gql/client/localState', () => ({
  userProfileVar: jest.fn().mockImplementation((...args: [string?]) => {
    if (args.length > 0) {
      return mockUserProfileVar(...args)
    }
    return mockUserProfileVar()
  }),
  userInfoVar: jest.fn().mockImplementation((...args: [object?]) => {
    if (args.length > 0) {
      return mockUserInfoVar(...args)
    }
    return mockUserInfoVar()
  }),
  shouldCloseTooltip: jest.fn().mockImplementation((...args: [boolean?]) => {
    if (args.length > 0) {
      return mockShouldCloseTooltip(...args)
    }
    return mockShouldCloseTooltip()
  })
}))

jest.mock('@apollo/client', () => ({
  ...jest.requireActual('@apollo/client'),
  useReactiveVar: jest.fn((varFn) => {
    // Return the current value of the reactive variable
    return varFn()
  })
}))

// Mock the useRemoveAssignment hook
jest.mock('@hooks/useRemoveAssignment', () => ({
  // eslint-disable-next-line @typescript-eslint/naming-convention
  __esModule: true,
  default: jest.fn(() => ({
    handleRemoveAllocation: jest.fn().mockResolvedValue(undefined),
    loadingDeleteAssignment: false
  }))
}))

const mockedAssignment = mockAssignment[0]

describe('CalendarDragItemContent', () => {
  const mockProps = {
    assignment: mockedAssignment,
    resourceType: 'Resource A',
    isLoading: false,
    isLocked: false,
    openModal: jest.fn(),
    currTileWidth: 2,
    currTileHeight: 20
  }

  // Helper render function to always wrap in MockedProvider
  const renderWithApollo = (props = mockProps) =>
    render(
      <MockedProvider>
        <CalendarDragItemContent {...props} />
      </MockedProvider>
    )

  it('renders correctly with required props', () => {
    const { getByTestId } = renderWithApollo()
    expect(getByTestId('calendar-drag-item-content')).toBeInTheDocument()
  })

  it('calls openModal when clicked and anchorPosition is null', () => {
    const { getByTestId } = renderWithApollo()
    fireEvent.click(getByTestId('calendar-drag-item-content'))
    expect(mockProps.openModal).toHaveBeenCalled()
  })

  xit('sets anchorPosition on right-click', () => {
    const { getByTestId } = renderWithApollo()
    fireEvent.contextMenu(getByTestId('calendar-drag-item-content'))
    // Verify anchorPosition is set (mocked behavior)
  })

  it('renders LeftContent and RightContent based on currTileHeight and currTileWidth', () => {
    const { getByText } = renderWithApollo()
    expect(getByText(mockedAssignment.projectName)).toBeInTheDocument()
    expect(getByText(mockedAssignment.taskName)).toBeInTheDocument()
    expect(getByText(`${mockedAssignment.hoursPerDay}h`)).toBeInTheDocument()
  })

  it('shows LinearProgress loader when isLoading is true', () => {
    const { getByTestId } = renderWithApollo({ ...mockProps, isLoading: true })
    expect(getByTestId('drag-item-loader')).toBeVisible()
  })

  it('renders RemoveModal when "Remove allocation" is clicked', async () => {
    renderWithApollo()
    const tile = screen.getByTestId('calendar-drag-item-content')
    fireEvent.contextMenu(tile)

    // Wait for context menu to appear
    await waitFor(() => {
      expect(screen.getByText('Remove allocation')).toBeInTheDocument()
    })

    fireEvent.click(screen.getByText('Remove allocation'))

    // Wait for modal to appear
    await waitFor(() => {
      expect(screen.getByText('Are you sure you want to remove this allocation?')).toBeInTheDocument()
    })
  })

  it('closes RemoveModal when the cancel button is clicked', async () => {
    renderWithApollo()
    const tile = screen.getByTestId('calendar-drag-item-content')
    fireEvent.contextMenu(tile)

    // Wait for context menu to appear
    await waitFor(() => {
      expect(screen.getByText('Remove allocation')).toBeInTheDocument()
    })

    fireEvent.click(screen.getByText('Remove allocation'))

    // Wait for modal to appear
    await waitFor(() => {
      expect(screen.getByText('Are you sure you want to remove this allocation?')).toBeInTheDocument()
    })

    // Click the cancel/close button
    fireEvent.click(screen.getByText('Cancel'))

    // Wait for modal to disappear
    await waitFor(() => {
      expect(screen.queryByText('Are you sure you want to remove this allocation?')).not.toBeInTheDocument()
    })
  })
})
