import { type IconProps } from '../Icon.props'

const ChevronIcon = ({ height, width, rotation, fillColor }: IconProps) => {
  return (
    <svg
      style={{ transform: `rotate(${rotation}deg)` }}
      width={height}
      height={width}
      viewBox="0 0 14 8"
      fill={fillColor}
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="return">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.9447 7.64297C12.6193 7.9684 12.0917 7.9684 11.7662 7.64297L7.05217 2.92892L2.33812 7.64297C2.01269 7.9684 1.48505 7.9684 1.15961 7.64297C0.834177 7.31753 0.834177 6.78989 1.15961 6.46446L6.46291 1.16116C6.78835 0.835718 7.31599 0.835718 7.64143 1.16115L12.9447 6.46446C13.2702 6.78989 13.2702 7.31753 12.9447 7.64297Z"
          fill="#202020"
        />
      </g>
    </svg>
  )
}

export default ChevronIcon
