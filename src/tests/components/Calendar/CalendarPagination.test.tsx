import { render, screen, fireEvent } from '@testing-library/react'
import CalendarPagination from '@components/Calendar/CalendarPagination/CalendarPagination'

describe('CalendarPagination Component', () => {
  const mockOnChangePage = jest.fn()
  const totalPages = 5
  const pageNumber = 1

  test('renders CalendarPagination component correctly', () => {
    render(<CalendarPagination totalPages={totalPages} pageNumber={pageNumber} onChangePage={mockOnChangePage} />)
    expect(screen.getByText('Previous')).toBeInTheDocument()
    expect(screen.getByText('Next')).toBeInTheDocument()
  })

  test('renders correct number of pagination items', () => {
    render(<CalendarPagination totalPages={totalPages} pageNumber={pageNumber} onChangePage={mockOnChangePage} />)
    const paginationItems = screen.getAllByRole('button')
    expect(paginationItems.length).toBe(totalPages + 2) // +2 for Previous and Next buttons
  })

  test('calls onChangePage function when a pagination item is clicked', () => {
    render(<CalendarPagination totalPages={totalPages} pageNumber={pageNumber} onChangePage={mockOnChangePage} />)
    const nextButton = screen.getByText('Next')
    fireEvent.click(nextButton)
    expect(mockOnChangePage).toHaveBeenCalled()
  })
})
