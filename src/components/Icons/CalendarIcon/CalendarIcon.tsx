import { type IconProps } from '../Icon.props'

const CalendarIcon = ({ height, width, rotation, fillColor }: IconProps) => {
  return (
    <svg
      style={{ transform: `rotate(${rotation}deg)` }}
      width={height}
      height={width}
      viewBox="0 0 14 14"
      fill={fillColor}
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="task">
        <path
          d="M8.92855 11.3332C8.46785 11.3332 8.07907 11.1741 7.76222 10.856C7.44537 10.5379 7.28695 10.1485 7.28695 9.68783C7.28695 9.22713 7.446 8.83835 7.7641 8.5215C8.0822 8.20465 8.4716 8.04623 8.9323 8.04623C9.393 8.04623 9.78177 8.20528 10.0986 8.52338C10.4155 8.84148 10.5739 9.23088 10.5739 9.69158C10.5739 10.1523 10.4149 10.5411 10.0968 10.8579C9.77864 11.1748 9.38924 11.3332 8.92855 11.3332ZM2.07245 13.9825C1.70022 13.9825 1.37612 13.8423 1.10015 13.5619C0.824196 13.2816 0.686218 12.9597 0.686218 12.5962V2.3984C0.686218 2.02357 0.824196 1.69726 1.10015 1.41945C1.37612 1.14165 1.70022 1.00275 2.07245 1.00275H2.98333V0.650563C2.98333 0.478529 3.04938 0.328395 3.18148 0.200162C3.31358 0.0719284 3.46619 0.0078125 3.6393 0.0078125C3.82593 0.0078125 3.9815 0.0719284 4.106 0.200162C4.2305 0.328395 4.29275 0.478529 4.29275 0.650563V1.00275H9.70722V0.650563C9.70722 0.478529 9.77107 0.328395 9.89877 0.200162C10.0265 0.0719284 10.1797 0.0078125 10.3585 0.0078125C10.542 0.0078125 10.6975 0.0719284 10.8252 0.200162C10.9528 0.328395 11.0166 0.478529 11.0166 0.650563V1.00275H11.9275C12.3023 1.00275 12.6287 1.14165 12.9065 1.41945C13.1843 1.69726 13.3232 2.02357 13.3232 2.3984V12.5962C13.3232 12.9597 13.1843 13.2816 12.9065 13.5619C12.6287 13.8423 12.3023 13.9825 11.9275 13.9825H2.07245ZM2.07245 12.5962H11.9275V5.44623H2.07245V12.5962ZM2.07245 4.3803H11.9275V2.3984H2.07245V4.3803Z"
          fill="black"
        />
      </g>
    </svg>
  )
}

export default CalendarIcon
