// lion ui import
import { Tooltip } from 'lion-ui'

// Component imports
import { styled } from '@mui/material/styles'
import { tooltipClasses, type TooltipProps } from '@mui/material'

const RMTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} arrow classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.arrow}`]: {
    color: theme.palette.common.black
  },
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: theme.palette.common.black,
    maxWidth: '20rem',
    borderRadius: '0.5rem',
    padding: '0.5rem 0.75rem'
  }
}))

export default RMTooltip
