import { type IconProps } from '../Icon.props'

const SwitchProfilesIcon = ({ height, width, fillColor = '#202020' }: IconProps) => {
  return (
    <svg width={width} height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M14.5674 12.3062C13.9252 12.3062 13.3931 12.0966 12.971 11.6775C12.5489 11.2583 12.3379 10.7311 12.3379 10.0959C12.3379 9.46074 12.5474 8.93209 12.9666 8.51C13.3858 8.08791 13.9129 7.87687 14.5481 7.87687C15.1834 7.87687 15.71 8.0877 16.1282 8.50935C16.5464 8.931 16.7555 9.45346 16.7555 10.0767C16.7555 10.7189 16.5466 11.251 16.1289 11.6731C15.7111 12.0952 15.1906 12.3062 14.5674 12.3062ZM11.0716 17.2917C10.8198 17.2917 10.6122 17.2112 10.4489 17.05C10.2855 16.8889 10.2038 16.6827 10.2038 16.4314V16.0038C10.2038 15.5844 10.2945 15.2253 10.476 14.9265C10.6575 14.6277 10.9124 14.3928 11.2409 14.2219C11.7498 13.9762 12.2867 13.7873 12.8518 13.6554C13.4168 13.5234 13.9847 13.4575 14.5553 13.4575C15.1259 13.4575 15.6855 13.5234 16.2339 13.6554C16.7823 13.7873 17.3215 13.9774 17.8515 14.2256C18.1696 14.3956 18.4241 14.6297 18.615 14.9279C18.8059 15.226 18.9013 15.5847 18.9013 16.004V16.4314C18.9013 16.6827 18.8174 16.8889 18.6496 17.05C18.4818 17.2112 18.2725 17.2917 18.0217 17.2917H11.0716ZM8.17558 9.78358C7.14612 9.78358 6.30756 9.45974 5.65989 8.81206C5.01223 8.16439 4.68839 7.32583 4.68839 6.29637C4.68839 5.26691 5.01223 4.42952 5.65989 3.78418C6.30756 3.13884 7.14612 2.81616 8.17558 2.81616C9.20504 2.81616 10.0464 3.13839 10.6996 3.78285C11.3528 4.42731 11.6794 5.26711 11.6794 6.30227C11.6794 7.32779 11.3528 8.16439 10.6996 8.81206C10.0464 9.45974 9.20504 9.78358 8.17558 9.78358ZM1.97825 17.2917C1.73748 17.2917 1.53463 17.2112 1.36969 17.05C1.20474 16.8889 1.12227 16.6841 1.12227 16.4357V15.0218C1.12227 14.4209 1.25361 13.8904 1.51629 13.4303C1.77897 12.9702 2.15749 12.6296 2.65185 12.4087C3.65931 11.969 4.60864 11.643 5.49985 11.4307C6.39106 11.2185 7.28331 11.1124 8.1766 11.1124C8.49907 11.1124 8.82773 11.1252 9.16258 11.1509C9.49743 11.1765 9.72131 11.2002 9.83423 11.222V12.1898C9.67044 12.2888 9.54555 12.3862 9.45958 12.4821C9.3736 12.578 9.28803 12.699 9.20287 12.8452C9.05976 12.8452 8.90684 12.8471 8.7441 12.8511C8.58135 12.855 8.39068 12.8569 8.17208 12.8569C7.3944 12.8569 6.64216 12.932 5.91537 13.082C5.18858 13.2321 4.38557 13.5185 3.50633 13.9412C3.30404 14.0275 3.14492 14.1719 3.02898 14.3742C2.91303 14.5765 2.85506 14.7814 2.85506 14.9889V15.5589H9.02083V17.2917H1.97825ZM8.17558 8.05077C8.67748 8.05077 9.09616 7.88365 9.4316 7.54941C9.76705 7.21518 9.93477 6.7988 9.93477 6.30029C9.93477 5.79852 9.76831 5.37923 9.43539 5.04241C9.10248 4.70559 8.68217 4.53718 8.17448 4.53718C7.67651 4.53718 7.2598 4.70577 6.92435 5.04295C6.58891 5.38014 6.42119 5.7973 6.42119 6.29445C6.42119 6.79483 6.58811 7.21264 6.92196 7.54789C7.2558 7.88314 7.67368 8.05077 8.17558 8.05077Z"
        fill={fillColor}
      />
    </svg>
  )
}

export default SwitchProfilesIcon
