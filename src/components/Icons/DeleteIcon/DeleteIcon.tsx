import { type IconProps } from '../Icon.props'

const DeleteIcon = ({ height, width, fillColor = '#D7322D' }: IconProps) => {
  return (
    <svg width={width} height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="delete">
        <g id="Vector">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M14.9349 8.83729C15.3915 8.89437 15.7155 9.31086 15.6584 9.76755L14.8251 16.4342C14.7729 16.8512 14.4184 17.1642 13.9982 17.1642H5.66483C5.24497 17.1642 4.89068 16.8518 4.83807 16.4353L4.00473 9.83778C3.94706 9.38117 4.27046 8.96426 4.72707 8.90659C5.18368 8.84891 5.60059 9.17231 5.65826 9.62892L6.39953 15.4975H13.2625L14.0046 9.56082C14.0617 9.10414 14.4782 8.7802 14.9349 8.83729Z"
            fill={fillColor}
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M11.4982 8.81295C11.9584 8.81295 12.3315 9.18604 12.3315 9.64628V12.9796C12.3315 13.4399 11.9584 13.8129 11.4982 13.8129C11.0379 13.8129 10.6648 13.4399 10.6648 12.9796V9.64628C10.6648 9.18604 11.0379 8.81295 11.4982 8.81295Z"
            fill={fillColor}
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M8.16483 8.81295C8.62507 8.81295 8.99817 9.18604 8.99817 9.64628V12.9796C8.99817 13.4399 8.62507 13.8129 8.16483 13.8129C7.70459 13.8129 7.3315 13.4399 7.3315 12.9796V9.64628C7.3315 9.18604 7.70459 8.81295 8.16483 8.81295Z"
            fill={fillColor}
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M2.5 6.33695C2.5 5.87672 2.8731 5.50362 3.33333 5.50362H16.66C17.1202 5.50362 17.4933 5.87672 17.4933 6.33695C17.4933 6.79719 17.1202 7.17029 16.66 7.17029H3.33333C2.8731 7.17029 2.5 6.79719 2.5 6.33695Z"
            fill={fillColor}
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M5.66975 2.97982C5.66975 2.51958 6.04285 2.14648 6.50308 2.14648H13.1723C13.6325 2.14648 14.0056 2.51958 14.0056 2.97982V6.32065C14.0056 6.78089 13.6325 7.15398 13.1723 7.15398C12.712 7.15398 12.3389 6.78089 12.3389 6.32065V3.81315H7.33642V6.32065C7.33642 6.78089 6.96332 7.15398 6.50308 7.15398C6.04285 7.15398 5.66975 6.78089 5.66975 6.32065V2.97982Z"
            fill={fillColor}
          />
        </g>
      </g>
    </svg>
  )
}

export default DeleteIcon
