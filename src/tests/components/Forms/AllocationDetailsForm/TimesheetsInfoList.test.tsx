import { render } from '@testing-library/react'
import TimesheetsInfoList from '@components/Forms/AllocationDetailsForm/Timesheets/TimesheetsInfoList/TimesheetsInfoList'
import mockTimesheets from '../../../__mocks__/timesheets.mock'

describe('TimesheetsInfoList', () => {
  it('renders TimesheetsInfoList component correctly', () => {
    const { container } = render(<TimesheetsInfoList timesheetsList={mockTimesheets} />)
    // eslint-disable-next-line
    const timesheetItems = container.getElementsByClassName('timesheet-info-item')
    expect(timesheetItems.length).toBe(mockTimesheets.length)
  })

  it('renders Divider component correctly between timesheets', () => {
    const { container } = render(<TimesheetsInfoList timesheetsList={mockTimesheets} />)
    // eslint-disable-next-line
    const dividers = container.querySelectorAll('hr')
    expect(dividers.length).toBe(mockTimesheets.length - 1)
  })
})
