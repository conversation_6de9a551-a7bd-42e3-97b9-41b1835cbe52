import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import mockAuditTrailEntry from '../../__mocks__/auditTrailEntry.mock'
import UpdateResource from '@components/AuditTrail/AuditTrailContentCases/UpdateResource'

// Mock dependencies
jest.mock('@components/Icons/UserIcon/UserIcon', () => jest.fn(() => <div data-testid="UserIcon" />))
jest.mock('@components/Icons/TaskIcon/TaskIcon', () => jest.fn(() => <div data-testid="TaskIcon" />))
jest.mock('@components/AuditTrail/AuditTrailGoToDetails/AuditTrailGoToDetails', () =>
  jest.fn(() => <div data-testid="GoToDetails" />)
)

describe('UpdateResource Component', () => {
  const mockEntry = mockAuditTrailEntry

  test('renders correctly when isCollapsed is false', () => {
    render(<UpdateResource entry={mockEntry} isOpen={false} isCollapsed={false} />)

    expect(screen.getAllByTestId('UserIcon').length).toBeGreaterThanOrEqual(2)
    expect(screen.getByTestId('TaskIcon')).toBeInTheDocument()
    expect(screen.getByTestId('GoToDetails')).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.resourceName)).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.taskName)).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.projectName)).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.newAssignment.resourceName)).toBeInTheDocument()
  })

  test('renders correctly when isCollapsed is true and isOpen is false', () => {
    render(<UpdateResource entry={mockEntry} isOpen={false} isCollapsed={true} />)

    expect(screen.getAllByTestId('UserIcon').length).toBeGreaterThanOrEqual(2)
    expect(screen.getByTestId('TaskIcon')).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.resourceName)).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.taskName)).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.projectName)).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.newAssignment.resourceName)).toBeInTheDocument()
    expect(screen.getByText(/Changed allocation from/i)).toBeInTheDocument()
  })

  test('renders correctly when isCollapsed is true and isOpen is true', () => {
    render(<UpdateResource entry={mockEntry} isOpen={true} isCollapsed={true} />)

    expect(screen.getByText(/Changed allocation from/i)).toBeInTheDocument()
  })
})
