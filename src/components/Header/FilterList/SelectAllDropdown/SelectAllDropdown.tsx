import { useEffect, useState, useMemo, useCallback } from 'react'
import { Box, Button, Menu, MenuItem, Checkbox, palette } from 'lion-ui'
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown'
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp'
import { activeFiltersVar } from '@gql/client/localState'
import { useReactiveVar } from '@apollo/client'
import { type SelectAllDropdownProps } from './SelectAllDropdown.props'
import { getIconByCategory } from '@utils/filterSearch'

export const SelectAllDropdown = ({ items, category }: SelectAllDropdownProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)

  const activeFilters = useReactiveVar(activeFiltersVar)

  const countSelectedFilteredItems = useMemo(() => {
    return items.filter((item) => activeFilters[category].some((existingItem) => existingItem.id === item.id)).length
  }, [items, activeFilters, category])

  const [selectedItems, setSelectedItems] = useState<number>(countSelectedFilteredItems)
  const [selectAllCheck, setSelectAllCheck] = useState(false)
  const [indeterminate, setIndeterminate] = useState(false)

  const updateSelectionStates = useCallback(() => {
    setIndeterminate(selectedItems > 0 && selectedItems < items.length)
    setSelectAllCheck(selectedItems === items.length && items.length > 0)
  }, [selectedItems, items.length])

  const handleOpenMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
  }

  const handleSelectAll = () => {
    const updatedItems = items.map((item) => ({
      ...item,
      category,
      icon: getIconByCategory(category)
    }))

    const filteredItems = updatedItems.filter(
      (newItem) => !activeFilters[category].some((existingItem) => existingItem.id === newItem.id)
    )
    activeFiltersVar({
      ...activeFilters,
      [category]: [...activeFilters[category], ...filteredItems]
    })
    setSelectedItems(items.length)
  }

  const handleDeselectAll = () => {
    activeFiltersVar({
      ...activeFilters,
      [category]: []
    })
    setSelectedItems(0)
  }

  const handleCheckboxChange = () => {
    if (selectAllCheck) {
      handleDeselectAll()
    } else {
      handleSelectAll()
    }
  }

  useEffect(() => {
    setSelectedItems(countSelectedFilteredItems)
  }, [countSelectedFilteredItems])

  useEffect(() => {
    updateSelectionStates()
  }, [selectedItems, items.length, updateSelectionStates])

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', marginLeft: 'auto', marginRight: '2.2rem' }}>
      <Button
        id="select-all-button"
        aria-controls={open ? 'select-all-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={handleOpenMenu}
        variant="text"
      >
        {!open ? <ArrowDropDownIcon /> : <ArrowDropUpIcon />}
        Select All {selectedItems > 0 && `(${selectedItems})`}
      </Button>
      <Menu
        id="select-all-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleMenuClose}
        MenuListProps={{
          // eslint-disable-next-line @typescript-eslint/naming-convention
          'aria-labelledby': 'basic-button'
        }}
      >
        <MenuItem
          onClick={() => {
            handleMenuClose()
            handleSelectAll()
          }}
        >
          Select all
        </MenuItem>
        <MenuItem
          onClick={() => {
            handleMenuClose()
            handleDeselectAll()
          }}
        >
          Unselect all
        </MenuItem>
      </Menu>
      <Checkbox
        checked={selectAllCheck}
        indeterminate={indeterminate}
        onClick={handleCheckboxChange}
        color="primary"
        sx={{
          color: palette.grey[900]
        }}
      />
    </Box>
  )
}
