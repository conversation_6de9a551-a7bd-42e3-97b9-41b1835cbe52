import { render } from '@testing-library/react'
import { useRouter } from 'next/router'
import { useMsal, useIsAuthenticated } from '@azure/msal-react'
import MSALAuth from '../../auth/MSALAuth'

// Mock MSAL hooks
jest.mock('@azure/msal-react', () => ({
  useMsal: jest.fn(),
  useIsAuthenticated: jest.fn()
}))

// Mock router
jest.mock('next/router', () => ({
  useRouter: jest.fn()
}))

// Mock reactive variables
jest.mock('@gql/client/localState', () => ({
  wflionLoginIDVar: jest.fn()
}))

jest.mock('@apollo/client', () => ({
  useReactiveVar: jest.fn()
}))

describe('MSALAuth', () => {
  const useRouterMock = useRouter as jest.Mock
  const useMsalMock = useMsal as jest.Mock
  const useIsAuthenticatedMock = useIsAuthenticated as jest.Mock
  const useReactiveVarMock = jest.requireMock('@apollo/client').useReactiveVar

  const mockInstance = {
    handleRedirectPromise: jest.fn(),
    ssoSilent: jest.fn()
  }

  beforeEach(() => {
    useRouterMock.mockReturnValue({ push: jest.fn() })
    useMsalMock.mockReturnValue({ instance: mockInstance })
    useIsAuthenticatedMock.mockReturnValue(false)
    useReactiveVarMock.mockReturnValue('test')
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  test('attempts SSO silent login when not authenticated', async () => {
    useIsAuthenticatedMock.mockReturnValue(false)
    mockInstance.ssoSilent.mockResolvedValueOnce({ accessToken: 'mock-token' })

    render(<MSALAuth />)

    await new Promise((resolve) => setTimeout(resolve, 0)) // Wait for useEffect

    expect(mockInstance.ssoSilent).toHaveBeenCalledWith({
      scopes: ['api://a0ce10c3-4f71-4f9a-bff6-db67e89174b1/user_api_access'],
      loginHint: '<EMAIL>'
    })
  })

  test('handles redirect promise when there is a response', async () => {
    useIsAuthenticatedMock.mockReturnValue(true)
    mockInstance.handleRedirectPromise.mockResolvedValueOnce({})

    render(<MSALAuth />)

    await new Promise((resolve) => setTimeout(resolve, 0)) // Wait for useEffect

    expect(mockInstance.handleRedirectPromise).toHaveBeenCalled()
  })

  test('uses correct scope for production environment', async () => {
    // Mock window object for production environment check
    Object.defineProperty(window, 'location', {
      value: { hostname: 'roar.marcel.ai' }
    })

    mockInstance.ssoSilent.mockResolvedValueOnce({ accessToken: 'mock-token' })

    render(<MSALAuth />)

    await new Promise((resolve) => setTimeout(resolve, 0)) // Wait for useEffect

    expect(mockInstance.ssoSilent).toHaveBeenCalledWith({
      scopes: ['api://82cf01b1-9445-4388-86cb-f5b01bcf6d99/user_api_access'],
      loginHint: '<EMAIL>'
    })
  })

  xit('uses correct scope for non-production environment', async () => {
    // Mock window object for non-production environment check
    Object.defineProperty(window, 'location', {
      value: { hostname: 'localhost' }
    })

    mockInstance.ssoSilent.mockResolvedValueOnce({ accessToken: 'mock-token' })

    render(<MSALAuth />)

    await new Promise((resolve) => setTimeout(resolve, 0)) // Wait for useEffect

    expect(mockInstance.ssoSilent).toHaveBeenCalledWith({
      scopes: ['api://a0ce10c3-4f71-4f9a-bff6-db67e89174b1/user_api_access'],
      loginHint: '<EMAIL>'
    })
  })
})
