export const sanitizeNotes = (inputText: string): string[] => {
  if (!inputText) {
    return []
  }

  // Replace &nbsp; with spaces
  const stringWithSpaces = inputText.replace(/&nbsp;/g, ' ')

  // Replace </p> and <br> tags with newline characters
  const stringWithNewlines = stringWithSpaces.replace(/<\/p\s*>|<br\s*\/?>/gi, '\n')

  // Remove all other HTML tags
  const plainText = stringWithNewlines.replace(/<\/?(?:[a-z][a-z0-9]*)\b[^>]*>/gi, '')

  // Split the text by newline characters to render separate <p> elements for each line
  const textSegments = plainText.split('\n')

  return textSegments
}

export const sanitizeNotesPreview = (inputText: string): string => {
  if (!inputText) {
    return ''
  }
  // Replace <br> tags with spaces
  const stringWithNewlines = inputText.replace(/<br\s*\/?>/gi, ' ')

  // Remove all other HTML tags
  const plainText = stringWithNewlines.replace(/<\/?(?:[a-z][a-z0-9]*)\b[^>]*>/gi, '')

  return plainText
}
