import { gql } from '@apollo/client'

export const UPDATE_ASSIGNMENT_MUTATION = gql`
  mutation updateAssignment($assignment: UpdateAssignmentParams) {
    updateAssignment(assignment: $assignment)
  }
`
export const DELETE_ASSIGNMENT_MUTATION = gql`
  mutation DeleteAssignmentById($assignmentId: String, $userLoggedInExternalId: String) {
    deleteAssignmentById(assignmentId: $assignmentId, userLoggedInExternalId: $userLoggedInExternalId)
  }
`

export const CREATE_ASSIGNMENT_MUTATION = gql`
  mutation CreateAssignment($params: CreateAssignmentParams) {
    createAssignment(params: $params) {
      id
      startDate
      dueDate
      hoursPerDay
      state
      assignmentState
      userId
      projectId
      taskId
      totalDays
      x
      y
      width
      height: hoursPerDay
      isPlaceholder
      taskName
      projectName
      externalLink
      altairJobNumber
      projectManagerName
      taskState
      taskNotes
      assignmentNotes
      taskStartDate
      taskDueDate
      nonWorkingDates {
        date
        reason
        isWorking
      }
      __typename
    }
  }
`

export const APPROVE_REQUEST_MUTATION = gql`
  mutation ApproveRejectAssignment($params: ApproveRejectAssignment) {
    approveRejectAssignment(params: $params)
  }
`
