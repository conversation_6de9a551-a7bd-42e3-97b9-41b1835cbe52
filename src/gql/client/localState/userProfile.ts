import { makeVar } from '@apollo/client'
import { type Resource } from '@typeDefs/Resource'

// Initialize userProfileVar with localStorage value if available, otherwise default to 'lightUser'
const getInitialProfile = (): string => {
  if (typeof window !== 'undefined') {
    const savedProfile = localStorage.getItem('selectedProfile')
    if (savedProfile) {
      return savedProfile
    }
  }
  return 'lightUser'
}

export const userProfileVar = makeVar(getInitialProfile())
// eslint-disable-next-line @typescript-eslint/consistent-type-assertions
export const userInfoVar = makeVar({} as Resource)
export const userNameVar = makeVar('')
export const userProfilesVar = makeVar<string[]>([])

// Auth reactive variables
export const employeeCodeVar = makeVar('')
export const businessRolesVar = makeVar<string[]>([])
export const businessRoleVar = makeVar('ROAR CROSSCOMPANY MANAGER')
export const usernameVar = makeVar('')
export const wflionLoginIDVar = makeVar('')

// Workfront query state tracking
export const isWorkfrontQueryActiveVar = makeVar(false)
