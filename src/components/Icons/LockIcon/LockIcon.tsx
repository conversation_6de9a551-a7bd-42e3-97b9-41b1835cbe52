import { type IconProps } from '../Icon.props'

const LockIcon = ({ height, width, fillColor = '#024426' }: IconProps) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} viewBox="0 0 10 12" fill="none">
      <path
        d="M1.8625 11.2875C1.53937 11.2875 1.26276 11.1724 1.03265 10.9423C0.80255 10.7122 0.6875 10.4356 0.6875 10.1125V4.9125C0.6875 4.58593 0.80255 4.30637 1.03265 4.07382C1.26276 3.84127 1.53937 3.725 1.8625 3.725H2.4875V2.8C2.4875 2.09008 2.73069 1.4876 3.21707 0.992563C3.70345 0.497521 4.2972 0.25 4.99832 0.25C5.69944 0.25 6.29375 0.497521 6.78125 0.992563C7.26875 1.4876 7.5125 2.09008 7.5125 2.8V3.725H8.1375C8.46407 3.725 8.74362 3.84127 8.97617 4.07382C9.20872 4.30637 9.325 4.58593 9.325 4.9125V10.1125C9.325 10.4356 9.20872 10.7122 8.97617 10.9423C8.74362 11.1724 8.46407 11.2875 8.1375 11.2875H1.8625ZM5.0021 8.475C5.26737 8.475 5.49375 8.3832 5.68125 8.19961C5.86875 8.01602 5.9625 7.79532 5.9625 7.5375C5.9625 7.2875 5.86805 7.06042 5.67915 6.85625C5.49025 6.65208 5.26317 6.55 4.9979 6.55C4.73263 6.55 4.50625 6.65208 4.31875 6.85625C4.13125 7.06042 4.0375 7.28958 4.0375 7.54375C4.0375 7.79792 4.13195 8.01667 4.32085 8.2C4.50975 8.38333 4.73683 8.475 5.0021 8.475ZM3.6625 3.725H6.3375V2.80289C6.3375 2.41346 6.2098 2.08854 5.95441 1.82812C5.69902 1.56771 5.38235 1.4375 5.00441 1.4375C4.62647 1.4375 4.30833 1.56771 4.05 1.82812C3.79167 2.08854 3.6625 2.41346 3.6625 2.80289V3.725Z"
        fill={fillColor}
      />
    </svg>
  )
}

export default LockIcon
