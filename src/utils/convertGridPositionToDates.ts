import { activeDatesVar } from '@gql/client/localState'
import dayjs from 'dayjs'

const convertGridPositionToDates = (xPosition: number, width: number): { startDate: string; endDate: string } => {
  const { startDate } = activeDatesVar()
  const startDateDay = dayjs(startDate)

  const assignmentStartDate = startDateDay.add(xPosition, 'day').format('YYYY-MM-DD')
  const assignmentEndDate = startDateDay.add(xPosition + width - 1, 'day').format('YYYY-MM-DD')

  return { startDate: assignmentStartDate, endDate: assignmentEndDate }
}

export default convertGridPositionToDates
