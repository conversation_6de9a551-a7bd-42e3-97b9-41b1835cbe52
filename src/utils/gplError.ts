import { type ApolloError } from '@apollo/client'
import { type ErrorNetwork } from '@typeDefs/common/ErrorNetwork'

export const gplError = (error: ApolloError, errorState: ApolloError | undefined): void => {
  if (error.networkError && 'result' in error.networkError && errorState) {
    const networkError = error.networkError as unknown as { result: { errors: ErrorNetwork[] } }
    const errorArray = networkError.result.errors.map((error: ErrorNetwork) => error.message)
    errorState.message = errorArray.join(', ')
  } else {
    console.error('GraphQL error:', error.message)
  }
}
