import { useMemo, memo } from 'react'

import { type CalendarViewOptions, CalendarWorkingDays } from '@constants/calendarViewOptions'
import { type Assignment } from '@typeDefs/Assignments'
import { type Resource } from '@typeDefs/Resource'
import { type Placeholder } from '@typeDefs/Placeholders'

import CalendarRow from '@components/Calendar/CalendarRow/CalendarRow'

interface PrepareRowParams {
  type: 'resource' | 'placeholder'
  assignmentsByResource: Assignment[]
  resourcesData: Resource | null
  placeholdersData: Placeholder | null
  assignmentsData: Assignment[]
  capacityMultipliers: { [key in CalendarViewOptions]: number }
  weeksSelector: CalendarViewOptions
}

const PrepareRow = ({
  type,
  assignmentsByResource,
  resourcesData,
  placeholdersData,
  assignmentsData,
  capacityMultipliers,
  weeksSelector
}: PrepareRowParams) => {
  const resource: Resource = useMemo(
    () => resourcesData ?? (placeholdersData as Resource),
    [resourcesData, placeholdersData]
  )

  const assignmentsByResources = useMemo(() => assignmentsData, [assignmentsData])

  const resourceWithCapacity = useMemo(
    () => ({
      ...resource,
      id: resource?.id ?? '',
      name: resource?.name ?? '',
      totalCapacity:
        (capacityMultipliers[weeksSelector] ?? CalendarWorkingDays.TWO_WEEKS) *
        (resource?.minimumAgencyHoursPerDay ?? 0),
      location: resource?.location ?? '',
      position: resource?.position ?? '',
      jobTitle: resource?.jobTitle ?? '',
      profitCenter: resource?.profitCenter ?? '',
      altairNumber: resource?.altairNumber ?? '',
      workCode: resource?.workCode ?? '',
      agencyName: resource?.agencyName ?? '',
      agencyCode: resource?.agencyCode ?? '',
      requiresAssignApproval: resource?.requiresAssignApproval ?? false,
      minimumAgencyHoursPerDay: resource?.minimumAgencyHoursPerDay ?? 0
    }),
    [resource, capacityMultipliers, weeksSelector]
  )
  if (!resource) return null

  return (
    <CalendarRow
      key={resource.id}
      index={resource.id}
      resource={resourceWithCapacity}
      assignments={assignmentsByResources}
      type={type}
    />
  )
}

export default memo(PrepareRow)
