import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import PlaceholderCollapse from '@components/Calendar/PlaceholderCollapse/PlaceholderCollapse'
import { userProfileVar } from '@gql/client/localState'
import { UserProfile } from '@constants/userProfile'

describe('PlaceholderCollapse Component', () => {
  test('renders with correct initial state', () => {
    render(<PlaceholderCollapse length={5}>Test Children</PlaceholderCollapse>)

    expect(screen.getByText('Show placeholders (5)')).toBeInTheDocument()
    expect(screen.getByText('Test Children')).not.toBeVisible()
  })

  test('toggles expansion state on click', () => {
    render(<PlaceholderCollapse length={5}>Test Children</PlaceholderCollapse>)

    const header = screen.getByRole('button', { name: /show placeholders/i })
    fireEvent.click(header)

    expect(screen.getByText('Hide placeholders (5)')).toBeInTheDocument()
  })

  test('displays correct length when length is undefined', () => {
    render(<PlaceholderCollapse length={undefined}>Test Children</PlaceholderCollapse>)

    expect(screen.getByText('Hide placeholders (0)')).toBeInTheDocument()
  })

  test('Placeholder bar should be hidden for light users', () => {
    userProfileVar(UserProfile.LIGHT_USER)
    render(<PlaceholderCollapse length={5}>Test Children</PlaceholderCollapse>)

    expect(screen.queryByText('Show placeholders')).not.toBeInTheDocument()
    expect(screen.queryByText('Hide placeholders')).not.toBeInTheDocument()
  })
})
