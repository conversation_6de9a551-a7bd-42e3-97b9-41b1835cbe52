import { type IconProps } from '../Icon.props'

const CrossRowIcon = ({ height, width, fillColor }: IconProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={height}
      height={width}
      viewBox="0 0 12 12"
      fill="none"
      style={{ top: '2px', position: 'relative' }}
    >
      <path
        d="M4.90084 5.5049L2.10084 2.72448C2.00809 2.63172 1.96172 2.52148 1.96172 2.39376C1.96172 2.26605 2.00809 2.15581 2.10084 2.06305C2.18786 1.97863 2.29666 1.93643 2.42726 1.93643C2.55784 1.93643 2.66534 1.97863 2.74977 2.06305L5.55519 4.85055L4.90084 5.5049ZM7.65031 10.2136C7.52088 10.2136 7.41126 10.1686 7.32144 10.0785C7.23163 9.98836 7.18672 9.8784 7.18672 9.74856C7.18672 9.61873 7.23163 9.50925 7.32144 9.42013C7.41126 9.33099 7.52088 9.28643 7.65031 9.28643H8.6253L6.48292 7.1299L7.11933 6.48805L9.29922 8.64294V7.63751C9.29922 7.50809 9.34427 7.39847 9.43436 7.30865C9.52446 7.21883 9.63442 7.17393 9.76426 7.17393C9.89409 7.17393 10.0036 7.21883 10.0927 7.30865C10.1818 7.39847 10.2264 7.50809 10.2264 7.63751V9.69295C10.2264 9.83483 10.1751 9.95707 10.0725 10.0597C9.96986 10.1623 9.84762 10.2136 9.70574 10.2136H7.65031ZM2.08834 9.91823C1.99559 9.82963 1.94922 9.72148 1.94922 9.59376C1.94922 9.46605 1.99559 9.35581 2.08834 9.26305L8.6503 2.7011H7.65031C7.52088 2.7011 7.41126 2.65605 7.32144 2.56596C7.23163 2.47586 7.18672 2.3659 7.18672 2.23606C7.18672 2.10623 7.23163 1.99675 7.32144 1.90763C7.41126 1.81849 7.52088 1.77393 7.65031 1.77393H9.70574C9.84762 1.77393 9.96986 1.82523 10.0725 1.92784C10.1751 2.03045 10.2264 2.1527 10.2264 2.29458V4.35001C10.2264 4.47944 10.1813 4.58906 10.0913 4.67888C10.0012 4.76869 9.89119 4.8136 9.76136 4.8136C9.63152 4.8136 9.52205 4.76869 9.43292 4.67888C9.34379 4.58906 9.29922 4.47944 9.29922 4.35001V3.36253L2.73727 9.92448C2.65229 10.0089 2.54609 10.0511 2.41864 10.0511C2.2912 10.0511 2.1811 10.0068 2.08834 9.91823Z"
        fill={fillColor}
      />
    </svg>
  )
}

export default CrossRowIcon
