// eslint-disable-next-line @typescript-eslint/no-var-requires
const path = require('path')

const isDev = process.env.NODE_ENV === 'development'

// eslint-disable-next-line
const nextConfig = () => {
  const config = {
    reactStrictMode: false,
    transpilePackages: ['gridstack'],
    sassOptions: {
      includePaths: [path.join(__dirname, 'styles')],
      prependData: `@import "~@styles/globals.scss";`
    },
    env: {
      API_URL: process.env.API_URL ?? 'http://localhost:3000',
      NEXT_PUBLIC_BFF_URL: process.env.NEXT_PUBLIC_BFF_URL,
      WF_SERVER_URL: process.env.WF_SERVER_URL ?? 'https://roar.sb03.workfront.com',
      APP_INSIGHTS_CONNECTION_STRING: process.env.APPLICATIONINSIGHTS_CONNECTION_STRING ?? 'no-app-insights'
    },
    assetPrefix: isDev ? '' : '/rm',
    basePath: isDev ? '' : '/rm',
    output: 'standalone'
  }

  return config
}

module.exports = nextConfig
