import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import FilterSearch from '@components/Header/FilterSearch/FilterSearch'
import { availableFilters } from '@constants/filters'
import { ApolloClient, ApolloProvider, InMemoryCache } from '@apollo/client'
import { mockResourcesQuery } from '../../../tests/__mocks__/resources-query.mock'
import { MockedProvider } from '@apollo/client/testing'
import { RESOURCES_QUERY } from '@gql/queries/resources.query'
import { activeSortVar } from '@gql/client/localState'

let apolloClientInit: ApolloClient<unknown>
const cache = new InMemoryCache()

const mocks = [
  {
    request: {
      query: RESOURCES_QUERY,
      variables: {
        params: {
          userId: '6746b7530004c4e701fd5f8ed20696d0',
          pageNumber: 1,
          pageSize: 50,
          sort: { field: activeSortVar().field, order: activeSortVar().order }
        }
      }
    },
    result: {
      data: {
        resources: {
          items: [
            {
              id: 'resource-1',
              name: 'Resource one',
              totalCapacity: 0,
              minimumAgencyHoursPerDay: 8
            }
          ],
          totalPages: 1
        }
      }
    }
  }
]

jest.mock('@apollo/client', () => {
  const actualApolloClient = jest.requireActual('@apollo/client')
  return {
    ...actualApolloClient,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    useApolloClient: jest.fn(() => ({
      readQuery: jest.fn(() => ({
        resources: {
          items: [
            {
              id: 'resource-1',
              name: 'Resource one',
              totalCapacity: 0,
              minimumAgencyHoursPerDay: 8
            }
          ],
          totalPages: 1
        }
      }))
    }))
  }
})

describe('FilterSearch Component', () => {
  beforeEach(() => {
    cache.writeQuery(mockResourcesQuery)
    apolloClientInit = new ApolloClient({
      cache
    })
    apolloClientInit.writeQuery(mockResourcesQuery)
  })
  it('renders FilterSearch component', () => {
    render(
      <MockedProvider addTypename={false}>
        <FilterSearch />
      </MockedProvider>
    )
    expect(screen.getByPlaceholderText('Search for resources, projects, tasks...')).toBeInTheDocument()
  })

  test('updates input field on typing', () => {
    render(
      <MockedProvider addTypename={false}>
        <FilterSearch />
      </MockedProvider>
    )
    const input = screen.getByPlaceholderText('Search for resources, projects, tasks...')
    fireEvent.click(input)
    const filterChip = screen.getByText(availableFilters[0].label)
    fireEvent.click(filterChip)
    fireEvent.change(input, { target: { value: 'test' } })
    expect(input).toHaveValue('test')
  })

  test('should change content when clicked a filter chip', () => {
    render(
      <MockedProvider addTypename={false}>
        <FilterSearch />
      </MockedProvider>
    )
    const inputElement = screen.getByPlaceholderText('Search for resources, projects, tasks...')
    fireEvent.click(inputElement)
    const filterChip = screen.getByText(availableFilters[0].label)
    fireEvent.click(filterChip)

    const noItemsElement = screen.queryByText('Filters and Categories')
    const FilterChipSelected = screen.getByText(availableFilters[0].label)

    expect(noItemsElement).not.toBeInTheDocument()
    expect(FilterChipSelected).toBeInTheDocument()
  })

  test('should close filter menu on Escape key press', () => {
    render(
      <MockedProvider addTypename={false}>
        <FilterSearch />
      </MockedProvider>
    )
    const inputElement = screen.getByPlaceholderText('Search for resources, projects, tasks...')
    fireEvent.click(inputElement)
    fireEvent.keyDown(document, { key: 'Escape', code: 'Escape' })
    expect(screen.queryByText('Filters and Categories')).not.toBeInTheDocument()
  })

  test('escape key resets filter selection', () => {
    render(
      <MockedProvider addTypename={false}>
        <FilterSearch />
      </MockedProvider>
    )
    const input = screen.getByPlaceholderText('Search for resources, projects, tasks...')
    fireEvent.click(input)
    fireEvent.keyDown(document, { key: 'Escape', code: 'Escape' })
    expect(screen.queryByText('Filter List Item')).not.toBeInTheDocument() // Replace 'Filter List Item' with an actual item text
  })

  it('should render SelectAllDropdown when a filter is selected', () => {
    cache.writeQuery(mockResourcesQuery)

    render(
      <MockedProvider mocks={mocks} cache={cache} addTypename={false}>
        <ApolloProvider client={apolloClientInit}>
          <FilterSearch />
        </ApolloProvider>
      </MockedProvider>
      //
      // </ApolloProvider>
    )
    const inputElement = screen.getByPlaceholderText('Search for resources, projects, tasks...')
    fireEvent.click(inputElement)
    const filterChip = screen.getByText(availableFilters[0].label)
    fireEvent.click(filterChip)

    const selectAllDropdown = screen.getByText('Select All')
    expect(selectAllDropdown).toBeInTheDocument()
  })
})
