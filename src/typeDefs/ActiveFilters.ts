import { type LabelItemWithSubItems, type LabelItem } from './LabelItem'

export type ActiveFilterIndexed = Record<string, LabelItem[]>

export interface ActiveFilters extends ActiveFilterIndexed {
  projects: LabelItem[] // Array<Partial<Project>>
  resources: LabelItem[] // Array<Partial<Resource>>
  brands: LabelItem[] // Array<Partial<Brand>>
  usersGroups: LabelItem[]
  tasks: LabelItem[] | LabelItemWithSubItems[]
  placeholders: LabelItem[]
}
