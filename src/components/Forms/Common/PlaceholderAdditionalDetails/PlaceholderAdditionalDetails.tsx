import { memo, useEffect, useState } from 'react'

import { debounce } from 'lodash'

import { Autocomplete, Box, FormLabel, palette, TextField, Typography, CircularProgress } from 'lion-ui'

import { useQuery } from '@apollo/client'
import {
  AGENCY_LOCATION_QUERY,
  COST_CENTER_QUERY,
  ORG_STRUCTURE_AGENCY_COST_CENTER_QUERY,
  SEARCH_RESOURCE_AGENCY_QUERY
} from '@gql/queries/resources.query'
import { gplError } from '@utils/gplError'

import { type Agency } from '@typeDefs/Agency'
import { type CostCenter } from '@typeDefs/CostCenter'
import { type ResourceOrgStructure } from '@typeDefs/Resource'

interface PlaceholderAdditionalDetailsProps {
  resourceOrgStructureData?: ResourceOrgStructure | null
  updateResourceOrgStructure: (resourceOrgStructure: { data: ResourceOrgStructure; loading: boolean } | null) => void
  disableFields?: boolean
}

const PlaceholderAdditionalDetails: React.FC<PlaceholderAdditionalDetailsProps> = ({
  updateResourceOrgStructure,
  resourceOrgStructureData,
  disableFields
}) => {
  const [additionalDetailsFormData, setAdditionalDetailsFormData] = useState<{
    businessUnit: Agency | null
    location: string | null
    costCenter: CostCenter | null
  }>({
    businessUnit:
      resourceOrgStructureData?.agencyCode && resourceOrgStructureData?.agencyName
        ? { agencyCode: resourceOrgStructureData.agencyCode, agencyName: resourceOrgStructureData.agencyName }
        : null,
    location: resourceOrgStructureData?.locationName ?? null,
    costCenter:
      resourceOrgStructureData?.agencyCode && resourceOrgStructureData?.costCenterName
        ? {
            costCenterCode: resourceOrgStructureData.costCenterCode,
            costCenterName: resourceOrgStructureData.costCenterName
          }
        : null
  })

  const [getLocations, setGetLocations] = useState<boolean>(true)

  const [additionalDetailsFormErrors, setAdditionalDetailsFormErrors] = useState<{
    businessUnit: string
    location: string
    costCenter: string
  }>({
    businessUnit: '',
    location: '',
    costCenter: ''
  })

  const [agencySearchTerm, setAgencySearchTerm] = useState<string>('')
  const [locationSearchTerm, setLocationSearchTerm] = useState<string>('')

  const {
    data: agencyData,
    error: agencyError,
    loading: agencyLoading
  } = useQuery<{ searchAgencyOrgStructure: Agency[] }>(SEARCH_RESOURCE_AGENCY_QUERY, {
    variables: {
      searchTerm: agencySearchTerm
    },
    onError: (error) => {
      gplError(error, agencyError)
    },
    skip: agencySearchTerm.length < 3
  })

  const {
    data: locationData,
    error: locationError,
    loading: locationLoading
  } = useQuery<{ getLocationByAgencyOrgStructure: string[] }>(AGENCY_LOCATION_QUERY, {
    variables: {
      agencyCode: additionalDetailsFormData.businessUnit?.agencyCode
    },
    onError: (error) => {
      gplError(error, locationError)
    },
    skip: !additionalDetailsFormData.businessUnit?.agencyCode
  })

  const {
    data: costCenterData,
    error: costCenterError,
    loading: costCenterLoading
  } = useQuery<{ getCostCenterByLocationOrgStructure: CostCenter[] }>(COST_CENTER_QUERY, {
    variables: {
      agencyCode: additionalDetailsFormData.businessUnit?.agencyCode,
      city: additionalDetailsFormData.location
    },
    onError: (error) => {
      gplError(error, costCenterError)
    },
    skip: !additionalDetailsFormData.businessUnit?.agencyCode
  })

  const { error: locationsError } = useQuery(ORG_STRUCTURE_AGENCY_COST_CENTER_QUERY, {
    variables: {
      params: {
        costCenterCode: additionalDetailsFormData.costCenter?.costCenterCode,
        agencyCode: additionalDetailsFormData.businessUnit?.agencyCode
      }
    },
    onCompleted: (data) => {
      setAdditionalDetailsFormData((prev) => ({
        ...prev,
        location: data.getOrgStructureByAgencyCodeCostCenterCode.locationName
      }))
    },
    onError: (error) => {
      gplError(error, locationsError)
    },
    skip: getLocations
  })

  const debouncedSetSearchTerm = debounce((value: string) => {
    setAgencySearchTerm(value)
  }, 300)

  const handleBusinessUnitChange = (value: Agency | null) => {
    setAdditionalDetailsFormData((prev) => ({
      ...prev,
      businessUnit: value,
      location: null,
      costCenter: null
    }))
    setLocationSearchTerm('')

    setAdditionalDetailsFormErrors((prev) => ({
      ...prev,
      businessUnit: value ? '' : 'Agency is required'
    }))
  }

  const handleCostCenterChange = (value: CostCenter | null) => {
    setAdditionalDetailsFormData((prev) => ({
      ...prev,
      costCenter: value
    }))
    const isLocationEmpty = additionalDetailsFormData.location === null
    setGetLocations(!isLocationEmpty)
  }

  useEffect(() => {
    if (additionalDetailsFormData.businessUnit) {
      updateResourceOrgStructure({
        data: {
          agencyCode: additionalDetailsFormData.businessUnit.agencyCode,
          agencyName: additionalDetailsFormData.businessUnit.agencyName,
          costCenterName: additionalDetailsFormData.costCenter?.costCenterName ?? '',
          costCenterCode: additionalDetailsFormData.costCenter?.costCenterCode ?? '',
          locationName: additionalDetailsFormData.location ?? ''
        },
        loading: false
      })
    } else {
      updateResourceOrgStructure(null)
    }
  }, [additionalDetailsFormData, updateResourceOrgStructure])

  useEffect(() => {
    if (resourceOrgStructureData) {
      setAdditionalDetailsFormData({
        businessUnit:
          resourceOrgStructureData?.agencyCode && resourceOrgStructureData?.agencyName
            ? { agencyCode: resourceOrgStructureData.agencyCode, agencyName: resourceOrgStructureData.agencyName }
            : null,
        location: resourceOrgStructureData?.locationName ?? null,
        costCenter:
          resourceOrgStructureData?.agencyCode && resourceOrgStructureData?.costCenterName
            ? {
                costCenterCode: resourceOrgStructureData.costCenterCode,
                costCenterName: resourceOrgStructureData.costCenterName
              }
            : null
      })
    } else {
      setAdditionalDetailsFormData({
        businessUnit: null,
        location: null,
        costCenter: null
      })
    }
  }, [resourceOrgStructureData])

  return (
    <Box>
      <Box
        sx={{
          marginTop: '1rem',
          display: 'flex',
          padding: '0',
          alignItems: 'flex-start',
          gap: '1rem',
          alignSelf: 'stretch',
          flexDirection: 'column',
          marginBottom: '1rem'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: '1.5rem', alignSelf: 'stretch' }}>
          <Box sx={{ flex: '1 1 50%' }}>
            <FormLabel
              className={'MuiFormLabel-asterisk'}
              required
              sx={{
                fontWeight: 'bold',
                color: palette.primary.dark,
                padding: '0.7rem 0',
                // eslint-disable-next-line @typescript-eslint/naming-convention
                '& .MuiFormLabel-asterisk': {
                  color: palette['500-Accent-3'].main
                }
              }}
              htmlFor="businessUnit"
            >
              Agency
            </FormLabel>
            <Autocomplete
              fullWidth
              disablePortal
              disabled={disableFields}
              id="businessUnit"
              data-testid="businessUnit"
              options={agencyData?.searchAgencyOrgStructure ?? []}
              getOptionLabel={(option) => `${option.agencyCode}: ${option?.agencyName}`}
              renderInput={(params) => (
                <TextField
                  error={additionalDetailsFormErrors.businessUnit !== ''}
                  {...params}
                  placeholder="Search for a Agency..."
                  InputProps={{
                    ...params.InputProps,
                    endAdornment: (
                      <>
                        {agencyLoading ? (
                          <CircularProgress size={20} sx={{ color: '#999', marginRight: '8px' }} />
                        ) : null}
                        {params.InputProps.endAdornment}
                      </>
                    )
                  }}
                />
              )}
              size="small"
              isOptionEqualToValue={(option, value) => option === value}
              clearOnBlur={true}
              value={additionalDetailsFormData.businessUnit}
              onChange={(_e, value) => {
                handleBusinessUnitChange(value)
              }}
              onInputChange={(_e, value) => {
                debouncedSetSearchTerm(value)
              }}
              onBlur={() => {
                setAdditionalDetailsFormErrors((prev) => ({
                  ...prev,
                  businessUnit: additionalDetailsFormData.businessUnit ? '' : 'Agency is required'
                }))
              }}
              defaultValue={null}
              noOptionsText={agencySearchTerm.length < 3 ? 'Type at least 3 characters' : ''}
            />
            {additionalDetailsFormErrors.businessUnit && (
              <Typography color="error"> {additionalDetailsFormErrors.businessUnit}</Typography>
            )}
          </Box>
          <Box sx={{ flex: '1 1 50%' }}>
            <FormLabel
              sx={{
                fontWeight: 'bold',
                color: palette.primary.dark,
                padding: '0.7rem 0',
                // eslint-disable-next-line @typescript-eslint/naming-convention
                '& .MuiFormLabel-asterisk': {
                  color: palette['500-Accent-3'].main
                }
              }}
              htmlFor="location"
            >
              Location
            </FormLabel>
            <Autocomplete
              fullWidth
              disablePortal
              disabled={disableFields}
              id="location"
              data-testid="location"
              options={locationData?.getLocationByAgencyOrgStructure ?? []}
              renderInput={(params) => (
                <TextField
                  {...params}
                  placeholder="Search for a location..."
                  InputProps={{
                    ...params.InputProps,
                    endAdornment: (
                      <>
                        {locationLoading ? (
                          <CircularProgress size={20} sx={{ color: '#999', marginRight: '8px' }} />
                        ) : null}
                        {params.InputProps.endAdornment}
                      </>
                    )
                  }}
                />
              )}
              size="small"
              isOptionEqualToValue={(option, value) => option === value}
              clearOnBlur={true}
              value={additionalDetailsFormData.location}
              onChange={(_e, value) => {
                setAdditionalDetailsFormData((prev) => ({
                  ...prev,
                  location: value
                }))
              }}
              onInputChange={(_e, value) => {
                setLocationSearchTerm(value)
              }}
              defaultValue={null}
              noOptionsText={locationSearchTerm.length < 3 ? 'Type at least 3 characters' : ''}
            />
          </Box>
        </Box>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'flex-start',
            gap: '1.5rem',
            alignSelf: 'stretch',
            justifyContent: 'flex-start'
          }}
        >
          <Box
            sx={{
              flex: '1 1 50%',
              maxWidth: '50%',
              paddingRight: '0.75rem'
            }}
          >
            <FormLabel
              sx={{
                fontWeight: 'bold',
                color: palette.primary.dark,
                padding: '0.7rem 0'
              }}
              htmlFor="costCenter"
            >
              Cost Center
            </FormLabel>
            <Autocomplete
              disablePortal
              disabled={disableFields}
              id="costCenter"
              data-testid="costCenter"
              options={costCenterData?.getCostCenterByLocationOrgStructure ?? []}
              getOptionLabel={(option) => `${option?.costCenterCode} : ${option?.costCenterName}`}
              isOptionEqualToValue={(option, value) => option.costCenterCode === value.costCenterCode} // Custom equality check
              renderInput={(params) => (
                <TextField
                  // error={additionalDetailsFormErrors.costCenter !== ''}
                  {...params}
                  placeholder="Search for a cost center..."
                  InputProps={{
                    ...params.InputProps,
                    endAdornment: (
                      <>
                        {costCenterLoading ? (
                          <CircularProgress size={20} sx={{ color: '#999', marginRight: '8px' }} />
                        ) : null}
                        {params.InputProps.endAdornment}
                      </>
                    )
                  }}
                />
              )}
              size="small"
              clearOnBlur={true}
              value={additionalDetailsFormData.costCenter}
              onChange={(_e, value, reason) => {
                handleCostCenterChange(value)
                if (reason === 'clear') {
                  setAdditionalDetailsFormData((prev) => ({
                    ...prev,
                    costCenter: null
                  }))
                }
              }}
              defaultValue={null}
            />
          </Box>
        </Box>
      </Box>
    </Box>
  )
}

export default memo(PlaceholderAdditionalDetails)
