import { render, screen, fireEvent } from '@testing-library/react'
import DatePicker from '@components/Header/DatePicker/DatePicker'

describe('DatePicker Component', () => {
  test('renders DatePicker component', () => {
    render(<DatePicker />)
    const datePickerElement = screen.getByRole('button')
    expect(datePickerElement).toBeInTheDocument()
  })

  test('allows date selection', () => {
    render(<DatePicker />)
    const datePickerElement: HTMLInputElement = screen.getByRole('button')
    fireEvent.click(datePickerElement)

    const newDateButton = screen.getByRole('button', { name: '15' })
    expect(newDateButton).toBeInTheDocument()
  })
})
