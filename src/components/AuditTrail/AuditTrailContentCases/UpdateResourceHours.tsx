import type React from 'react'
import { Box, palette, Typography } from 'lion-ui'
import type AuditTrailContentCaseProps from './AuditTrailContentCase.props'
import UserIcon from '@components/Icons/UserIcon/UserIcon'
import TaskIcon from '@components/Icons/TaskIcon/TaskIcon'
import AuditTrailGoToDetails from '@components/AuditTrail/AuditTrailGoToDetails/AuditTrailGoToDetails'
import PlaceholderIcon from '@components/Icons/PlaceholderIcon/PlaceholderIcon'

const UpdateResourceHours = ({ entry, isOpen, isCollapsed }: AuditTrailContentCaseProps): React.JSX.Element => {
  return (
    <Box sx={{ color: '#4D4D4D' }}>
      {!isCollapsed && (
        <Box sx={{ paddingLeft: '10rem', width: '35rem' }}>
          {entry.details.oldAssignment.isPlaceholder ? (
            <PlaceholderIcon width="12" height="12" />
          ) : (
            <UserIcon width="12" height="12" />
          )}{' '}
          <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
            {entry.details.oldAssignment.resourceName}
          </Typography>
          {' | '}
          <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
            {entry.details.oldAssignment.hoursPerDay}
          </Typography>
          {' hours '}
          in{' '}
          <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
            {entry.details.oldAssignment.projectName}
          </Typography>{' '}
          under <TaskIcon width="12" height="12" />{' '}
          <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
            {entry.details.oldAssignment.taskName}
          </Typography>{' '}
          to{' '}
          {entry.details.newAssignment.isPlaceholder ? (
            <PlaceholderIcon width="12" height="12" />
          ) : (
            <UserIcon width="12" height="12" />
          )}{' '}
          <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
            {entry.details.newAssignment.resourceName}
          </Typography>
          {' | '}
          <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
            {entry.details.newAssignment.hoursPerDay}
          </Typography>
          {' hours '}
          <AuditTrailGoToDetails
            assignmentId={entry.details.newAssignment.externalId}
            resourceId={entry.details.newAssignment.resourceId}
          />
        </Box>
      )}

      {isCollapsed && (
        <Box>
          {!isOpen && (
            <Box
              sx={{
                display: 'inline-block',
                maxWidth: '25rem',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }}
            >
              Changed allocation, hours per day from{' '}
              {entry.details.oldAssignment.isPlaceholder ? (
                <PlaceholderIcon width="12" height="12" />
              ) : (
                <UserIcon width="12" height="12" />
              )}{' '}
              <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
                {entry.details.oldAssignment.resourceName}
              </Typography>
              {' | '}
              <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
                {entry.details.oldAssignment.hoursPerDay}
              </Typography>
              {' hours '}
              in{' '}
              <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
                {entry.details.oldAssignment.projectName}
              </Typography>{' '}
              under <TaskIcon width="12" height="12" />{' '}
              <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
                {entry.details.oldAssignment.taskName}
              </Typography>{' '}
              to{' '}
              {entry.details.newAssignment.isPlaceholder ? (
                <PlaceholderIcon width="12" height="12" />
              ) : (
                <UserIcon width="12" height="12" />
              )}{' '}
              <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
                {entry.details.newAssignment.resourceName}
              </Typography>
              {' | '}
              <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
                {entry.details.newAssignment.hoursPerDay}
              </Typography>
              {' hours '}
            </Box>
          )}

          {isOpen && (
            <Box
              sx={{
                display: 'inline-block',
                maxWidth: '21rem',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }}
            >
              Changed allocation, hours per day from
            </Box>
          )}
        </Box>
      )}
    </Box>
  )
}

export default UpdateResourceHours
