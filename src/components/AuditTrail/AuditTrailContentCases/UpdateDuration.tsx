import type React from 'react'
import { Box, palette, Typography } from 'lion-ui'
import type AuditTrailContentCaseProps from './AuditTrailContentCase.props'
import UserIcon from '@components/Icons/UserIcon/UserIcon'
import TaskIcon from '@components/Icons/TaskIcon/TaskIcon'
import ScheduleIcon from '@components/Icons/ScheduleIcon/ScheduleIcon'
import getLocaleDateFormat from '@utils/getLocaleDateFormat'
import AuditTrailGoToDetails from '@components/AuditTrail/AuditTrailGoToDetails/AuditTrailGoToDetails'
import PlaceholderIcon from '@components/Icons/PlaceholderIcon/PlaceholderIcon'

const UpdateDuration = ({ entry, isOpen, isCollapsed }: AuditTrailContentCaseProps): React.JSX.Element => {
  const formattedOldStartDate = getLocaleDateFormat(entry.details.oldAssignment.startDate)
  const formattedOldDueDate = getLocaleDateFormat(entry.details.oldAssignment.dueDate)
  const formattedNewStartDate = getLocaleDateFormat(entry.details.newAssignment.startDate)
  const formattedNewDueDate = getLocaleDateFormat(entry.details.newAssignment.dueDate)

  return (
    <Box sx={{ color: '#4D4D4D' }}>
      {!isCollapsed && (
        <Box sx={{ paddingLeft: '10rem', width: '35rem' }}>
          <TaskIcon width="12" height="12" />{' '}
          <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
            {entry.details.oldAssignment.taskName}
          </Typography>{' '}
          in{' '}
          <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
            {entry.details.oldAssignment.projectName}
          </Typography>{' '}
          on{' '}
          {entry.details.oldAssignment.isPlaceholder ? (
            <PlaceholderIcon width="12" height="12" />
          ) : (
            <UserIcon width="12" height="12" />
          )}{' '}
          <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
            {entry.details.oldAssignment.resourceName}
          </Typography>{' '}
          from <ScheduleIcon width="14" height="14" />{' '}
          <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
            {formattedOldStartDate} - {formattedOldDueDate}
          </Typography>{' '}
          to <ScheduleIcon width="14" height="14" />{' '}
          <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
            {formattedNewStartDate} - {formattedNewDueDate}
          </Typography>
          <AuditTrailGoToDetails
            assignmentId={entry.details.newAssignment.externalId}
            resourceId={entry.details.newAssignment.resourceId}
          />
        </Box>
      )}

      {isCollapsed && (
        <Box>
          {!isOpen && (
            <Box
              sx={{
                display: 'inline-block',
                maxWidth: '25rem',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }}
            >
              Changed duration for <TaskIcon width="12" height="12" />{' '}
              <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
                {entry.details.oldAssignment.taskName}
              </Typography>{' '}
              in{' '}
              <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
                {entry.details.oldAssignment.projectName}
              </Typography>{' '}
              on{' '}
              {entry.details.oldAssignment.isPlaceholder ? (
                <PlaceholderIcon width="12" height="12" />
              ) : (
                <UserIcon width="12" height="12" />
              )}{' '}
              <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
                {entry.details.oldAssignment.resourceName}
              </Typography>{' '}
              from <ScheduleIcon width="14" height="14" />{' '}
              <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
                {formattedOldStartDate} - {formattedOldDueDate}
              </Typography>{' '}
              to <ScheduleIcon width="14" height="14" />{' '}
              <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
                {formattedNewStartDate} - {formattedNewDueDate}
              </Typography>
            </Box>
          )}

          {isOpen && (
            <Box
              sx={{
                display: 'inline-block',
                maxWidth: '21rem',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }}
            >
              Changed duration for
            </Box>
          )}
        </Box>
      )}
    </Box>
  )
}

export default UpdateDuration
