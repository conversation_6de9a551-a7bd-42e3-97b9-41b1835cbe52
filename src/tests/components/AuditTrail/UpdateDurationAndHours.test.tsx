import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import UpdateDurationAndHours from '@components/AuditTrail/AuditTrailContentCases/UpdateDurationAndHours'
import mockAuditTrailEntry from '../../__mocks__/auditTrailEntry.mock'
import getLocaleDateFormat from '@utils/getLocaleDateFormat'

// Mock dependencies
jest.mock('@components/Icons/UserIcon/UserIcon', () => jest.fn(() => <div data-testid="UserIcon" />))
jest.mock('@components/Icons/TaskIcon/TaskIcon', () => jest.fn(() => <div data-testid="TaskIcon" />))
jest.mock('@components/Icons/CalendarIcon/CalendarIcon', () => jest.fn(() => <div data-testid="CalendarIcon" />))

describe('UpdateDurationAndHours Component', () => {
  const mockEntry = mockAuditTrailEntry

  xit('renders correctly when isCollapsed is false', () => {
    render(<UpdateDurationAndHours entry={mockEntry} isOpen={false} isCollapsed={false} />)

    expect(screen.getByTestId('UserIcon')).toBeInTheDocument()
    expect(screen.getByTestId('TaskIcon')).toBeInTheDocument()
    expect(screen.getByTestId('CalendarIcon')).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.resourceName)).toBeInTheDocument()
    expect(screen.getByText(`${mockEntry.details.oldAssignment.hoursPerDay} hours`)).toBeInTheDocument()
    expect(
      screen.getByText(
        `${getLocaleDateFormat(mockEntry.details.oldAssignment.startDate)} - ${getLocaleDateFormat(mockEntry.details.oldAssignment.dueDate)}`
      )
    ).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.taskName)).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.projectName)).toBeInTheDocument()
    expect(screen.getByText(`${mockEntry.details.newAssignment.hoursPerDay} hours`)).toBeInTheDocument()
    expect(
      screen.getByText(
        `${getLocaleDateFormat(mockEntry.details.newAssignment.startDate)} - ${getLocaleDateFormat(mockEntry.details.newAssignment.dueDate)}`
      )
    ).toBeInTheDocument()
    expect(screen.getByText('Go to details')).toBeInTheDocument()
  })

  test('renders correctly when isCollapsed is true and isOpen is false', () => {
    render(<UpdateDurationAndHours entry={mockEntry} isOpen={false} isCollapsed={true} />)

    expect(screen.getByTestId('UserIcon')).toBeInTheDocument()
    expect(screen.getByTestId('TaskIcon')).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.resourceName)).toBeInTheDocument()
    expect(screen.getByText(`${mockEntry.details.oldAssignment.hoursPerDay} hours`)).toBeInTheDocument()
    expect(
      screen.getByText(
        `${getLocaleDateFormat(mockEntry.details.oldAssignment.startDate)} - ${getLocaleDateFormat(mockEntry.details.oldAssignment.dueDate)}`
      )
    ).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.taskName)).toBeInTheDocument()
    expect(screen.getByText(mockEntry.details.oldAssignment.projectName)).toBeInTheDocument()
    expect(screen.getByText(`${mockEntry.details.newAssignment.hoursPerDay} hours`)).toBeInTheDocument()
    expect(
      screen.getByText(
        `${getLocaleDateFormat(mockEntry.details.newAssignment.startDate)} - ${getLocaleDateFormat(mockEntry.details.newAssignment.dueDate)}`
      )
    ).toBeInTheDocument()
  })

  test('renders correctly when isCollapsed is true and isOpen is true', () => {
    render(<UpdateDurationAndHours entry={mockEntry} isOpen={true} isCollapsed={true} />)

    expect(screen.getByText('Changed hours per day, duration for')).toBeInTheDocument()
  })
})
