import { Apollo<PERSON>rovider } from '@apollo/client'
import type { AppProps } from 'next/app'
import apolloClientInit from '@gql/client/apolloClientInit'
import apolloClientWorkfront from '@gql/client/apolloClientWorkfront'
import { RoarThemeProvider, CssBaseline, Box, palette } from 'lion-ui'
import ErrorBoundary from '@components/ErrorBoundary/ErrorBoundary'
import AlertNotification from '@components/Alert/Alert'
import { ScrollProvider } from '@context/scrollContext'
import { AuthenticatedTemplate, MsalProvider, UnauthenticatedTemplate } from '@azure/msal-react'
import MSALAuth from '@auth/MSALAuth'
import { msalInstance } from '@auth/msalConfig'
import WorkfrontPlugin from './Workfront/Plugin'
import WorkfrontRegister from './Workfront/Register'
import { isWorkfrontComponent } from '@utils/workfront'
import { initializeEmployeeCodeFromUrl } from '@utils/auth'
import { useEffect } from 'react'

const RmCalendarApp: React.FC<AppProps> = ({ Component, pageProps }) => {
  const darkMode = false

  const isWorkfrontPageComponent =
    Component === WorkfrontRegister || Component === WorkfrontPlugin || isWorkfrontComponent(Component)

  // Initialize employeeCode from URL parameters on app startup
  useEffect(() => {
    initializeEmployeeCodeFromUrl()
  }, [])

  if (isWorkfrontPageComponent) {
    return (
      <ApolloProvider client={apolloClientWorkfront}>
        <ScrollProvider>
          <RoarThemeProvider darkMode={darkMode}>
            <CssBaseline />
            <Component {...pageProps} />
          </RoarThemeProvider>
        </ScrollProvider>
      </ApolloProvider>
    )
  }

  return (
    <MsalProvider instance={msalInstance}>
      <ApolloProvider client={apolloClientInit}>
        <ScrollProvider>
          <RoarThemeProvider darkMode={darkMode}>
            <CssBaseline />
            <AuthenticatedTemplate>
              <ErrorBoundary>
                <Box sx={{ backgroundColor: palette.grey['50'] }}>
                  <Component {...pageProps} />
                </Box>
                <AlertNotification />
              </ErrorBoundary>
            </AuthenticatedTemplate>
            <UnauthenticatedTemplate>
              <MSALAuth />
            </UnauthenticatedTemplate>
          </RoarThemeProvider>
        </ScrollProvider>
      </ApolloProvider>
    </MsalProvider>
  )
}

export default RmCalendarApp
