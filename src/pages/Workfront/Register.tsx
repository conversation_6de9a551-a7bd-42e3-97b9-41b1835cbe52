import { useEffect } from 'react'
import { RmCalendarIcon } from '../../../public/icons'
import { workfrontConfig } from '@utils/workfront'
import { debugWorkfrontConfig } from '@utils/workfront-debug'

// Function name is Adobe-specific, allow as-is
// eslint-disable-next-line @typescript-eslint/naming-convention
function ExtensionRegistration(): JSX.Element {
  const init = async (): Promise<void> => {
    try {
      debugWorkfrontConfig()

      console.log('Extension Registration - Initializing with config:', {
        id: workfrontConfig.id,
        mainMenuId: workfrontConfig.mainMenuId,
        uixId: workfrontConfig.uixId,
        uixMainMenuId: workfrontConfig.uixMainMenuId,
        instanceId: workfrontConfig.instanceId,
        isInIframe: workfrontConfig.isInIframe,
        hostname: typeof window !== 'undefined' ? window.location.hostname : 'undefined',
        url: window.location.href
      })

      const register = await import('@adobe/uix-guest').then((module) => module.register)

      await register({
        methods: {
          // @ts-expect-error: TODO: Connect with Adobe Team to fix TypeScript error
          id: workfrontConfig.uixId,
          mainMenu: {
            getItems(): Array<{
              id: string
              label: string
              icon: string
              url: string
            }> {
              const menuItems = [
                {
                  id: workfrontConfig.uixMainMenuId,
                  label: workfrontConfig.menuLabel,
                  icon: RmCalendarIcon,
                  url: workfrontConfig.url
                }
              ]

              console.log('Extension Registration - Returning menu items:', menuItems)
              return menuItems
            }
          },
        }
      })

      console.log('Extension Registration - Successfully registered with UIX ID:', workfrontConfig.uixId)
      console.log('Extension Registration - URLs will use original ID:', workfrontConfig.id)
      console.log('Extension Registration - Successfully registered')
    } catch (error) {
      console.error('Extension Registration - Error during registration:', error)
      console.error('Extension Registration - Failed with config:', workfrontConfig)
    }
  }

  useEffect(() => {
    init().catch((error) => {
      console.error('Error', error)
    })
  }, []) // eslint-disable-line react-hooks/exhaustive-deps

  return <div></div>
}

export default ExtensionRegistration
