import { render, screen, fireEvent } from '@testing-library/react'
import TimesheetsInfoCta from '@components/Forms/AllocationDetailsForm/Timesheets/TimesheetInfoCta/TimesheetsInfoCta'
import { type TimesheetsInfoCtaProps } from '@components/Forms/AllocationDetailsForm/Timesheets/TimesheetInfoCta/TimesheetsInfoCta.props'

describe('TimesheetsInfoCta', () => {
  const mockOpenTimesheetsView = jest.fn()

  const defaultProps: TimesheetsInfoCtaProps = {
    timesheetDataExists: false,
    isLoading: false,
    openTimesheetsView: mockOpenTimesheetsView
  }

  test('renders correctly when there is no timesheet data', () => {
    render(<TimesheetsInfoCta {...defaultProps} />)
    expect(screen.getByText('There is no timesheet data to display')).toBeInTheDocument()
  })

  test('renders correctly when there is timesheet data', () => {
    render(<TimesheetsInfoCta {...defaultProps} timesheetDataExists={true} />)
    expect(screen.getByText('Latest timesheet history')).toBeInTheDocument()
  })

  test('calls handleOpenTimesheetsView when button is clicked', () => {
    render(<TimesheetsInfoCta {...defaultProps} timesheetDataExists={true} />)
    const button = screen.getByRole('button')
    fireEvent.click(button)
    expect(mockOpenTimesheetsView).toHaveBeenCalledWith(true)
  })
})
