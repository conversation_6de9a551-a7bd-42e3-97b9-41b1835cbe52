// React imports
import { useState, forwardRef, useEffect, useRef } from 'react'

// lion ui import
import { Box, LinearProgress, Zoom } from 'lion-ui'

// Apollo import
import { type ApolloError, useReactiveVar } from '@apollo/client'

// Graphql import
import { userProfileVar, shouldCloseTooltip, userInfoVar } from '@gql/client/localState'
import { UserProfile } from '@constants/userProfile'

// Component imports
import TileContextMenu from '@components/Calendar/TileContextMenu/TileContextMenu'
import CrossRowIcon from '@components/Icons/CrossRowIcon/CrossRowIcon'
import RMTooltip from '@components/Calendar/Tooltip/Tooltip'
import TooltipContent from '@components/Calendar/ToolTipContent/TooltipContent'

// Types import
import type CalendarDragItemContentProps from './CalendarDragItemContent.props'
import { type TileStatus } from '@typeDefs/Tile'
import { AssignmentStatus, TaskStates } from '@constants/taskStatus'
import { type AnchorPosition } from '../TileContextMenu/TileContextMenu.props'
import LockIcon from '@components/Icons/LockIcon/LockIcon'
import getCalendarItemStyle, { getTileStatus } from '@components/Calendar/CalendarDragItem/CalendarDragItem.style'

import { gplError } from '@utils/gplError'
import useRemoveAssignment from '@hooks/useRemoveAssignment'
import RemoveModal from '../RemoveModal/RemoveModal'

// eslint-disable-next-line react/display-name
const CalendarDragItemContent = forwardRef<HTMLDivElement, CalendarDragItemContentProps>(
  ({ assignment, resourceType, isLoading, isLocked, openModal, currTileWidth, currTileHeight }, ref) => {
    const {
      taskState,
      assignmentState,
      projectName,
      taskName,
      altairJobNumber,
      projectAgencyCode,
      projectAgencyName,
      hoursPerDay
    } = assignment

    const divRef = useRef<HTMLDivElement>(null)
    const [displayHoursPerDay, setDisplayHoursPerDay] = useState(hoursPerDay)
    const userProfile = useReactiveVar(userProfileVar)
    const [anchorPosition, setAnchorPosition] = useState<AnchorPosition | null>(null)
    const isLightUser = userProfile === UserProfile.LIGHT_USER
    const altJobNumber = altairJobNumber ?? 'Altair Job Number not available'
    const assignmentIcaStatus = assignment.assignmentIcaStatus
    const closeTooltip = useReactiveVar(shouldCloseTooltip)
    const tileStatus = getTileStatus(assignmentState, taskState, resourceType)
    const userInfo = useReactiveVar(userInfoVar)

    const heightBreakpoint = 10
    const showLineStep = 4

    const applyEllipsisStyles = {
      textOverflow: 'ellipsis',
      overflow: 'hidden',
      whiteSpace: 'nowrap',
      width: '99%'
    }

    const handleAllocationForm = () => {
      if (anchorPosition === null) {
        openModal()
      }
    }

    const handleRightClick = (event: React.MouseEvent<HTMLDivElement>) => {
      if (
        assignment.assignmentState.toLowerCase() === AssignmentStatus.EXTERNAL ||
        assignment.taskState === TaskStates.complete ||
        assignment.blockedByTimesheet
      ) {
        return
      }
      event.stopPropagation()
      event.preventDefault()
      shouldCloseTooltip(true)
      setAnchorPosition({
        mouseX: event.clientX - 2,
        mouseY: event.clientY - 4
      })
    }

    const [openRemoveModal, setOpenRemoveModal] = useState(false)

    const handleCloseContextMenu = () => {
      shouldCloseTooltip(false)
      setAnchorPosition(null)
    }

    const { handleRemoveAllocation, loadingDeleteAssignment } = useRemoveAssignment(
      assignment.id,
      userInfo?.altairNumber,
      () => {
        setOpenRemoveModal(false)
      }
    )

    useEffect(() => {
      if (isLoading) {
        const gsItemHeight = divRef.current?.parentElement?.parentElement
        const gsHeightAttr = gsItemHeight?.getAttribute('gs-h')
        const gsHeight = gsHeightAttr ? parseFloat(gsHeightAttr) : 0
        setDisplayHoursPerDay(gsHeight / 4)
      }
    }, [isLoading])

    // To avoid ghost effect of hours per day when the tile is updated
    useEffect(() => {
      setDisplayHoursPerDay(hoursPerDay)
    }, [hoursPerDay])

    const LeftContent = () => (
      <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
        <Box>
          <Box
            sx={currTileHeight <= heightBreakpoint ? { textWrap: 'nowrap', overflow: 'hidden' } : applyEllipsisStyles}
          >
            <b>{projectName}</b>
          </Box>
          {currTileHeight >= heightBreakpoint + showLineStep && <Box sx={applyEllipsisStyles}>{taskName}</Box>}
          {currTileHeight >= heightBreakpoint + showLineStep * (currTileWidth === 1 ? 2 : 1.5) && (
            <Box sx={applyEllipsisStyles}>{altJobNumber}</Box>
          )}
          {currTileHeight >= heightBreakpoint + showLineStep * (currTileWidth === 1 ? 4 : 2.5) && (
            <Box sx={applyEllipsisStyles}>
              {assignment.isPlaceholder
                ? `${assignment.agencyCode} : ${assignment.agencyName}`
                : `${projectAgencyCode} : ${projectAgencyName}`}
            </Box>
          )}
          {currTileHeight >= heightBreakpoint + showLineStep * (currTileWidth === 1 ? 6 : 3.5) &&
            assignment.isPlaceholder && (
              <>
                <Box sx={applyEllipsisStyles}>{assignment.locationName}</Box>
                <Box sx={applyEllipsisStyles}>{assignment.costCenterName}</Box>
              </>
            )}
        </Box>
        <Box>
          {currTileHeight >= heightBreakpoint && !isLightUser && assignmentIcaStatus && !assignment.isPlaceholder && (
            <Box sx={applyEllipsisStyles}>
              <CrossRowIcon
                height="12"
                width="12"
                fillColor={getCalendarItemStyle(tileStatus.toLowerCase() as TileStatus).color}
              />
              {assignmentIcaStatus}
            </Box>
          )}
        </Box>
      </Box>
    )

    const RightContent = () => (
      <Box sx={{ height: '100%' }}>
        {currTileHeight <= heightBreakpoint ? (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              flexDirection: 'row',
              alignItems: 'center'
            }}
          >
            {currTileWidth > 2 && (
              <Box sx={{ ...applyEllipsisStyles, marginRight: '0.2rem' }}>
                {(TaskStates as Record<string, string>)[tileStatus?.toLowerCase()] || tileStatus}
              </Box>
            )}
            {isLocked && (
              <LockIcon
                height="10"
                width="10"
                fillColor={getCalendarItemStyle(tileStatus.toLowerCase() as TileStatus).color}
              />
            )}
            <Box>
              <Box sx={{ fontSize: '0.625rem' }} id={`hours-${assignment.userId}-${assignment.id}`}>
                {displayHoursPerDay}
                {currTileWidth > 1 && <span>h</span>}
              </Box>
            </Box>
            {!isLightUser && assignmentIcaStatus && !assignment.isPlaceholder && (
              <Box sx={{ padding: '0 0 0.1rem 0' }}>
                <CrossRowIcon
                  height="10"
                  width="10"
                  fillColor={getCalendarItemStyle(tileStatus.toLowerCase() as TileStatus).color}
                />
              </Box>
            )}
          </Box>
        ) : (
          <Box
            sx={{
              minWidth: '0.2rem',
              height: '100%',
              padding: '0.15rem 0.1rem 0 0',
              display: 'flex',
              justifyContent: 'space-between',
              flexDirection: 'column',
              alignItems: 'end'
            }}
          >
            <Box sx={{ display: 'flex' }}>
              {currTileWidth > 1 && (
                <Box sx={{ ...applyEllipsisStyles, marginRight: '0.2rem', marginTop: '-3px' }}>
                  {(TaskStates as Record<string, string>)[tileStatus?.toLowerCase()] || tileStatus}
                </Box>
              )}
              {isLocked && (
                <LockIcon
                  height="12"
                  width="12"
                  fillColor={getCalendarItemStyle(tileStatus.toLowerCase() as TileStatus).color}
                />
              )}
            </Box>
            <Box>
              <Box sx={{ fontSize: '0.625rem' }} id={`hours-${assignment.userId}-${assignment.id}`}>
                {displayHoursPerDay}h
              </Box>
            </Box>
          </Box>
        )}
      </Box>
    )

    return (
      <RMTooltip
        title={closeTooltip ? '' : <TooltipContent assignment={assignment} />}
        placement="bottom"
        arrow
        enterDelay={1200}
        enterNextDelay={700}
        leaveDelay={200}
        TransitionComponent={Zoom}
        slotProps={{
          popper: {
            modifiers: [
              {
                name: 'offset',
                options: {
                  offset: [0, -8]
                }
              }
            ]
          }
        }}
      >
        <Box
          ref={divRef}
          sx={{
            display: 'flex',
            flexWrap: 'nowrap',
            justifyContent: 'space-between',
            height: '100%',
            padding: '0.1rem 0.2rem',
            fontSize: '0.75rem',
            alignItems: 'start'
          }}
          data-testid="calendar-drag-item-content"
          onClick={handleAllocationForm}
          onContextMenu={handleRightClick}
        >
          <Box sx={{ flex: 1, height: '100%', display: 'flex', minWidth: 0 }}>
            <Box sx={{ flex: 1, height: '100%', minWidth: 0 }}>
              <LeftContent />
            </Box>
            <Box
              sx={{
                flexShrink: 0,
                display: 'flex',
                justifyContent: 'end',
                height: '100%',
                width: 'auto',
                minWidth: currTileWidth === 1 ? '45%' : '10%'
              }}
            >
              <RightContent />
            </Box>
          </Box>
          {!isLightUser && Boolean(anchorPosition) && (
            <TileContextMenu
              anchorPosition={anchorPosition}
              handleClose={handleCloseContextMenu}
              assignment={assignment}
              onOpenRemoveModal={() => {
                setOpenRemoveModal(true)
              }}
            />
          )}

          <RemoveModal
            open={openRemoveModal}
            message="Are you sure you want to remove this allocation?"
            closeText="Cancel"
            removeText="Remove"
            onRemove={() => {
              handleRemoveAllocation().catch((error) => {
                gplError(error as ApolloError, error as ApolloError)
              })
            }}
            onClose={() => {
              setOpenRemoveModal(false)
            }}
            isLoading={loadingDeleteAssignment}
          />

          <Box
            data-testid="drag-item-loader"
            sx={{ width: '100%', position: 'absolute', bottom: 0, left: 0, display: isLoading ? 'block' : 'none' }}
          >
            <LinearProgress />
          </Box>
        </Box>
      </RMTooltip>
    )
  }
)

export default CalendarDragItemContent
