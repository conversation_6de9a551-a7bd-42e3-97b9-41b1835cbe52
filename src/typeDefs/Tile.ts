/* eslint-disable @typescript-eslint/naming-convention */
import { type CSSProperties } from 'react'

export interface TileStyle {
  backgroundColor: string
  color?: string
  borderRadius?: string
  border?: string
  cursor?: string
  backgroundImage?: string
  '.ui-resizable-sw'?: CSSProperties
  '.ui-resizable-se'?: CSSProperties
  '.ui-resizable-e'?: CSSProperties
  '.ui-resizable-w'?: CSSProperties
  '.ui-resizable-s'?: CSSProperties
  '.ui-resizable-handle'?: CSSProperties
  '&::before'?: ApprovalStatusBorder
}

export interface ApprovalStatusBorder {
  content: string
  position: string
  left: number
  top: number
  height: string
  width: string
  backgroundColor: string
  borderRadius: string
}

export type TileStatus =
  | 'active'
  | 'approved'
  | 'cancelled'
  | 'requested'
  | 'rejected'
  | 'onhold'
  | 'completed'
  | 'draft'
  | 'proposed'
  | 'filtered'
  | 'external'
  | 'default'
  | 'tentative'
  | 'new'
  | 'complete'
  | 'inprogress'
