# Use an official Node.js image as the base image
FROM node:22.3.0-alpine

# Set the working directory inside the container
RUN mkdir -p /app
WORKDIR /app

# Set build-time variables
ARG NODE_ENV
ARG NPM_TOKEN
ARG NEXT_PUBLIC_BFF_URL
ARG WF_SERVER_URL

# Set environment variables
ENV NODE_ENV=$NODE_ENV
ENV NPM_TOKEN=$NPM_TOKEN
ENV NEXT_PUBLIC_BFF_URL=$NEXT_PUBLIC_BFF_URL
ENV WF_SERVER_URL=$WF_SERVER_URL

# Copy the package.json and package-lock.json files
COPY package*.json ./

# Copy the .npmrc file if it exists
COPY .npmrc ./

# Configure npm to use the provided token
RUN npm config set //pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/registry/:_password=[${NPM_TOKEN}]
RUN npm config set //pkgs.dev.azure.com/agencytech/_packaging/lion-ui/npm/:_password=[${NPM_TOKEN}]

# Clean npm cache and install project dependencies
RUN npm install

# Copy the rest of the application code
COPY . .

# Ensure there are no root-owned files in the app directory
RUN chown -R node:node /app

# Switch to non-root user
USER node

# Build the application for production
RUN npm run build

# Expose the port on which the application will run
EXPOSE 3000

# Command to run the application
CMD ["npm", "start"]