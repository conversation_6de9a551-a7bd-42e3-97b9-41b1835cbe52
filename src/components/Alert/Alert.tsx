import { useEffect, useState, useCallback } from 'react'

import { useReactiveVar } from '@apollo/client'
import { showAlertVar } from '@gql/client/localState'

import { Snack<PERSON>, Alert as LionAlert, palette } from 'lion-ui'

const AlertNotification = () => {
  const alertValue = useReactiveVar(showAlertVar)
  const [alert, setAlert] = useState(alertValue)

  useEffect(() => {
    setAlert(alertValue)
  }, [alertValue])

  const createMarkup = useCallback((html: string) => {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    return { __html: html }
  }, [])

  const handleClose = () => {
    showAlertVar({
      ...alert,
      show: false,
      message: ''
    })
    setAlert((prevAlert) => ({
      ...prevAlert,
      show: false,
      message: ''
    }))
  }

  return (
    <Snackbar
      open={alert.show && alert.message !== ''}
      onClose={handleClose}
      anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      // eslint-disable-next-line @typescript-eslint/naming-convention
      sx={{ a: { textDecoration: 'none', color: palette.common.white }, maxWidth: '31.8125rem' }}
    >
      <LionAlert
        onClose={handleClose}
        severity={alert.severity ?? 'error'}
        sx={{
          backgroundColor: palette['Error-Main'].main,
          color: palette.common.white,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          '.MuiAlert-icon': {
            color: palette.common.white
          }
        }}
      >
        <span dangerouslySetInnerHTML={createMarkup(alert.message)} />
      </LionAlert>
    </Snackbar>
  )
}

export default AlertNotification
