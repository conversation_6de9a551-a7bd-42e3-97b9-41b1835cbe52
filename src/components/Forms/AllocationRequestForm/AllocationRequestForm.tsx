// React imports
import { useCallback, memo, useState } from 'react'
import Image from 'next/image'

// Lion-ui imports
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  Divider,
  FormControl,
  InputAdornment,
  OutlinedInput,
  Typography,
  palette
} from 'lion-ui'

// GQL imports
import {
  resourceDetailsVar,
  showAlertVar,
  showAllocationDetailsFormVar,
  userInfoVar,
  userProfileVar,
  generalLoadingStateVar
} from '@gql/client/localState'
import { useMutation, useReactiveVar } from '@apollo/client'
import { UPDATE_ASSIGNMENT_MUTATION, APPROVE_REQUEST_MUTATION } from '@gql/mutations/assignment.mutation'
import { type UpdateAssignmentParams } from '@typeDefs/Assignments'
import { gplError } from '@utils/gplError'
import { ASSIGNMENTS_QUERY } from '@gql/queries/assignments.query'
import { TaskAssignmentState } from '@constants/taskStatus'

// Component imports
import FormHeader from '@components/Forms/Common/FormHeader/FormHeader'
import AssignmentUpdate from './AssignmentUpdate'

// Icons imports
import backIcon from '@assets/icons/back.svg'
import messageIcon from '@assets/icons/message.svg'
import { UserProfile } from '@constants/userProfile'

const AllocationRequestForm = () => {
  const assignment = useReactiveVar(showAllocationDetailsFormVar)
  const resourceDetails = useReactiveVar(resourceDetailsVar)
  const [isEditingAssignment, setIsEditingAssignment] = useState<boolean>(false)
  const [isRejectRequest, setIsRejectRequest] = useState(false)
  const [rejectReason, setRejectReason] = useState('')
  const userProfile = useReactiveVar(userProfileVar)
  const activeUser = useReactiveVar(userInfoVar)

  const [approveRequestMutation] = useMutation(APPROVE_REQUEST_MUTATION, {
    refetchQueries: [ASSIGNMENTS_QUERY],
    awaitRefetchQueries: true,

    onCompleted: () => {
      generalLoadingStateVar(false)
    },
    onError: (error) => {
      gplError(error, error)
      generalLoadingStateVar(false)
    }
  })

  // Define a callback to handle closing the dialog
  const handleClose = useCallback(() => {
    showAllocationDetailsFormVar(undefined)
    resourceDetailsVar(undefined)
  }, [])

  const handleUpdateAssignment = (values: UpdateAssignmentParams) => {
    updateAssignmentMutation({ variables: { assignment: values } }).catch((error) => {
      console.error(error)
    })
    handleClose()
  }

  const [updateAssignmentMutation, { loading: loadingUpdateAssignment }] = useMutation(UPDATE_ASSIGNMENT_MUTATION, {
    refetchQueries: [ASSIGNMENTS_QUERY],
    awaitRefetchQueries: true,
    onCompleted: () => {
      generalLoadingStateVar(false)
    },
    onError: (error) => {
      showAlertVar({ message: error.message, show: true })
      gplError(error, error)
      generalLoadingStateVar(false)
    }
  })

  const rejectRequestHandler = useCallback(() => {
    setIsRejectRequest(true)
  }, [])

  const goBackHandler = useCallback(() => {
    setIsRejectRequest(false)
  }, [])

  const approveRequestHandler = useCallback(() => {
    if (assignment) {
      approveRequestMutation({
        variables: {
          params: {
            externalId: assignment.id,
            taskAssignmentState: TaskAssignmentState.approved,
            userLoggedInExternalId: activeUser?.altairNumber
          }
        }
      }).catch((error) => {
        console.error(error)
        generalLoadingStateVar(false)
      })
    }
  }, [approveRequestMutation, assignment, activeUser.altairNumber])

  const rejectRequestConfirmationHandler = useCallback(() => {
    if (assignment) {
      approveRequestMutation({
        variables: {
          params: {
            externalId: assignment.id,
            taskAssignmentState: TaskAssignmentState.rejected,
            reason: rejectReason,
            userLoggedInExternalId: activeUser?.altairNumber
          }
        }
      }).catch((error) => {
        console.error(error)
      })
    }
  }, [approveRequestMutation, assignment, rejectReason, activeUser.altairNumber])

  const renderRejectionBody = useCallback(() => {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: '1rem',
          padding: '1rem',
          width: '100%',
          alignItems: 'right'
        }}
      >
        <Typography variant="BodyMBold" sx={{ color: palette.grey['700'] }}>
          Reason for Rejection
        </Typography>
        <FormControl variant="standard">
          <OutlinedInput
            onChange={(event) => {
              setRejectReason(event.target.value)
            }}
            id="rejectReason"
            placeholder="Enter your comments for the rejection here..."
            sx={{ padding: '0.5rem', height: '40px' }}
            startAdornment={
              <InputAdornment position="start">
                <Image src={messageIcon} height={20} width={20} alt="Assignment icon" />
              </InputAdornment>
            }
          />
        </FormControl>
      </Box>
    )
  }, [])

  // If assignment is undefined, render nothing
  if (!assignment) {
    return null
  }

  return (
    <Dialog open={true} onClose={handleClose} maxWidth="lg">
      <FormHeader
        title="Resource request"
        projectName={assignment.projectName}
        taskName={assignment.taskName}
        state={assignment.assignmentState}
        stateStyle={assignment.assignmentState}
        onClose={handleClose}
      />
      <Divider />

      <DialogContent
        sx={{
          width: '42.5rem',
          display: 'flex',
          padding: '1.25rem 1.5rem',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '1rem',
          alignSelf: 'stretch'
        }}
      >
        {!isRejectRequest && (
          <>
            <Box
              sx={{
                display: 'flex',
                width: '100%',
                flexDirection: 'row',
                alignItems: 'flex-start',
                marginBottom: '1rem',
                opacity: isEditingAssignment ? 0.3 : 1
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  width: '50%',
                  flexDirection: 'column',
                  gap: '0.375rem;',
                  alignItems: 'flex-start'
                }}
              >
                <Typography variant="BodyMBold" sx={{ color: palette.grey['700'] }}>
                  Resource name
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    padding: ' 0.25rem 0rem',
                    alignItems: 'center',
                    gap: '0.25rem',
                    flex: ' 1 0 0'
                  }}
                >
                  <Typography
                    sx={{ fontSize: '0.875rem', color: palette.grey['700'] }}
                    data-testid="ResourceDetailsName"
                  >
                    {resourceDetails?.name}
                  </Typography>
                </Box>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  width: '50%',
                  flexDirection: 'column',
                  gap: '0.375rem;',
                  alignItems: 'flex-start'
                }}
              >
                <Typography variant="BodyMBold" sx={{ color: palette.grey['700'] }}>
                  Location
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    padding: ' 0.25rem 0rem',
                    alignItems: 'center',
                    gap: '0.25rem',
                    flex: ' 1 0 0'
                  }}
                >
                  <Typography
                    sx={{ fontSize: '0.875rem', color: palette.grey['700'] }}
                    data-testid="ResourceDetailsLocation"
                  >
                    {resourceDetails?.location}
                  </Typography>
                </Box>
              </Box>
            </Box>

            <Box
              sx={{
                display: 'flex',
                width: '100%',
                flexDirection: 'row',
                alignItems: 'flex-start',
                marginBottom: '1rem',
                opacity: isEditingAssignment ? 0.3 : 1
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  width: '50%',
                  flexDirection: 'column',
                  gap: '0.375rem;',
                  alignItems: 'flex-start'
                }}
              >
                <Typography variant="BodyMBold" sx={{ color: palette.grey['700'] }}>
                  Job title
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    padding: ' 0.25rem 0rem',
                    alignItems: 'center',
                    gap: '0.25rem',
                    flex: ' 1 0 0'
                  }}
                >
                  <Typography
                    sx={{ fontSize: '0.875rem', color: palette.grey['700'] }}
                    data-testid="ResourceDetailsJobTitle"
                  >
                    {resourceDetails?.jobTitle}
                  </Typography>
                </Box>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  width: '50%',
                  flexDirection: 'column',
                  gap: '0.375rem;',
                  alignItems: 'flex-start'
                }}
              >
                <Typography variant="BodyMBold" sx={{ color: palette.grey['700'] }}>
                  Labour workcode
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    padding: ' 0.25rem 0rem',
                    alignItems: 'center',
                    gap: '0.25rem',
                    flex: ' 1 0 0'
                  }}
                >
                  <Typography
                    sx={{ fontSize: '0.875rem', color: palette.grey['700'] }}
                    data-testid="ResourceDetailsWorkCode"
                  >
                    {resourceDetails?.workCode}
                  </Typography>
                </Box>
              </Box>
            </Box>

            <Box
              sx={{
                display: 'flex',
                width: '100%',
                flexDirection: 'row',
                alignItems: 'flex-start',
                marginBottom: '1rem',
                opacity: isEditingAssignment ? 0.3 : 1
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  width: '50%',
                  flexDirection: 'column',
                  gap: '0.375rem;',
                  alignItems: 'flex-start'
                }}
              >
                <Typography variant="BodyMBold" sx={{ color: palette.grey['700'] }}>
                  Hours requested
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    padding: ' 0.25rem 0rem',
                    alignItems: 'center',
                    gap: '0.25rem',
                    flex: ' 1 0 0'
                  }}
                >
                  <Typography
                    sx={{ fontSize: '0.875rem', color: palette.grey['700'] }}
                    data-testid="AllocationDetailsFormHoursRequested"
                  >
                    {assignment.totalDays * assignment.hoursPerDay}
                  </Typography>
                </Box>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  width: '50%',
                  flexDirection: 'column',
                  gap: '0.375rem;',
                  alignItems: 'flex-start'
                }}
              >
                <Typography variant="BodyMBold" sx={{ color: palette.grey['700'] }}>
                  Agency
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    padding: ' 0.25rem 0rem',
                    alignItems: 'center',
                    gap: '0.25rem',
                    flex: ' 1 0 0'
                  }}
                >
                  <Typography
                    sx={{ fontSize: '0.875rem', color: palette.grey['700'] }}
                    data-testid="AllocationDetailsFormAgency"
                  >
                    {resourceDetails?.agencyCode} : {resourceDetails?.agencyName}
                  </Typography>
                </Box>
              </Box>
            </Box>

            <Box
              sx={{
                display: 'flex',
                width: '100%',
                flexDirection: 'row',
                alignItems: 'flex-start',
                marginBottom: '1rem',
                opacity: isEditingAssignment ? 0.3 : 1
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  width: '50%',
                  flexDirection: 'column',
                  gap: '0.375rem;',
                  alignItems: 'flex-start'
                }}
              >
                <Typography variant="BodyMBold" sx={{ color: palette.grey['700'] }}>
                  Total period requested
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    padding: ' 0.25rem 0rem',
                    alignItems: 'center',
                    gap: '0.25rem',
                    flex: ' 1 0 0'
                  }}
                >
                  <Typography
                    sx={{ fontSize: '0.875rem', color: palette.grey['700'] }}
                    data-testid="AllocationDetailsFormTotalPeriod"
                  >
                    {assignment.totalDays} - Days
                  </Typography>
                </Box>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  width: '50%',
                  flexDirection: 'column',
                  gap: '0.375rem;',
                  alignItems: 'flex-start'
                }}
              >
                <Typography variant="BodyMBold" sx={{ color: palette.grey['700'] }}>
                  Project manager
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    padding: ' 0.25rem 0rem',
                    alignItems: 'center',
                    gap: '0.25rem',
                    flex: ' 1 0 0'
                  }}
                >
                  <Typography
                    sx={{ fontSize: '0.875rem', color: palette.grey['700'] }}
                    data-testid="AllocationDetailsFormProjectManager"
                  >
                    {assignment.projectManagerName}
                  </Typography>
                </Box>
              </Box>
            </Box>

            <AssignmentUpdate
              assignment={assignment}
              resourceDetails={resourceDetails}
              updateAssignment={handleUpdateAssignment}
              isEditingAssignment={setIsEditingAssignment}
              isLoading={loadingUpdateAssignment}
            />
          </>
        )}
        {isRejectRequest && renderRejectionBody()}
      </DialogContent>
      <Divider />

      <DialogActions sx={{ width: '100%', justifyContent: 'space-between', padding: '1rem' }}>
        {!isRejectRequest && userProfile === UserProfile.RESOURCE_MANAGER && (
          <Box sx={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
            <Button sx={{ mx: 1 }} color="error" onClick={rejectRequestHandler} disabled={isEditingAssignment}>
              Reject Request
            </Button>
            <Button sx={{ mx: 1 }} disabled={isEditingAssignment} onClick={approveRequestHandler}>
              Approve Request
            </Button>
          </Box>
        )}
        {isRejectRequest && (
          <Box sx={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              sx={{ mx: 1 }}
              variant="outlined"
              onClick={goBackHandler}
              startIcon={<Image src={backIcon} height={20} width={20} alt="Assignment icon" />}
            >
              Go back
            </Button>
            <Button sx={{ mx: 1 }} color="error" onClick={rejectRequestConfirmationHandler}>
              Confirm rejection
            </Button>
          </Box>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default memo(AllocationRequestForm)
