import React, { useCallback } from 'react'
import { Box, Checkbox, LinearProgress, ListItem, ListItemButton, ListItemText, Typography } from 'lion-ui'
import { useReactiveVar } from '@apollo/client'
import { activeFiltersVar } from '@gql/client/localState'
import { getIconByCategory } from '@utils/filterSearch'
import type { FilterListProps } from './FilterList.props'
import type { LabelItem, LabelItemWithSubItems } from '@typeDefs/LabelItem'
import { FilterListsLoader } from '@components/Loaders/FilterListsLoader/FilterListsLoader'

const FilterList: React.FC<FilterListProps> = ({ items, category, isLoading }) => {
  const activeFilters = useReactiveVar(activeFiltersVar)
  function subItemsContainIds(subItems: LabelItem[], subItemIds: string[]): boolean {
    return subItemIds.every((id) => subItems.some((subItem) => subItem.id === id))
  }
  function hasSubItems(filter: LabelItem | LabelItemWithSubItems): filter is LabelItemWithSubItems {
    return 'subItems' in filter && Array.isArray(filter.subItems)
  }
  const isItemChecked = useCallback(
    (itemId: string) => {
      const categoryFilters = activeFilters[category]
      if (!categoryFilters) return false

      return categoryFilters.some((filter) => {
        if ('subItems' in filter && Array.isArray(filter.subItems)) {
          return filter.subItems.some((subItem) => subItem.id === itemId)
        }
        return filter.id === itemId
      })
    },
    [activeFilters, category]
  )

  const updateFilterSelection = useCallback(
    (item: LabelItem | LabelItemWithSubItems) => {
      const isChecked = isItemChecked(item.id)

      const removeFilters = (
        filters: LabelItem[] | LabelItemWithSubItems[],
        itemId: string,
        subItemIds: string[] = []
      ) =>
        filters.filter(
          (filter) => filter.id !== itemId && (!hasSubItems(filter) || !subItemsContainIds(filter.subItems, subItemIds))
        )

      const addFilters = (filters: LabelItem[], item: LabelItem, subItems: LabelItem[] = []) => [
        ...filters,
        { ...item, category, icon: getIconByCategory(category) },
        ...subItems.map((subItem) => ({ ...subItem, category, icon: getIconByCategory(category) }))
      ]

      let updatedFilters = activeFilters[category] || []

      if ('subItems' in item && Array.isArray(item.subItems)) {
        const subItemIds = item.subItems.map((subItem) => subItem.id)
        updatedFilters = isChecked
          ? removeFilters(updatedFilters, item.id, subItemIds)
          : addFilters(updatedFilters, item, item.subItems)
      } else {
        updatedFilters = isChecked ? removeFilters(updatedFilters, item.id) : addFilters(updatedFilters, item)
      }

      activeFiltersVar({ ...activeFilters, [category]: updatedFilters })
    },
    [activeFilters, category, isItemChecked]
  )

  const renderListItem = (item: LabelItem) => (
    <ListItem
      key={`${item.category}-${item.id}--list-item`}
      disablePadding
      secondaryAction={
        <Checkbox
          edge="end"
          // eslint-disable-next-line @typescript-eslint/naming-convention
          inputProps={{ 'aria-label': item.name }}
          onChange={() => {
            updateFilterSelection(item)
          }}
          data-testid={`checkbox-${item.id}`}
          checked={isItemChecked(item.id)}
        />
      }
    >
      <ListItemButton
        onClick={() => {
          updateFilterSelection(item)
        }}
        sx={{
          paddingBottom: 1,
          paddingTop: 1
        }}
      >
        <ListItemText primary={item.name} />
      </ListItemButton>
    </ListItem>
  )

  const renderSubItems = (item: LabelItemWithSubItems) => (
    <Box key={`${item.category}-${item.id}--subitems`} sx={{ padding: 0 }}>
      <Typography variant="body1">{item.name}</Typography>
      {item.subItems.map(renderListItem)}
    </Box>
  )

  return (
    <Box sx={{ padding: '0.5rem 2rem', overflowY: 'auto', maxHeight: '74vh' }}>
      {items.length === 0 && isLoading && <FilterListsLoader numOfRows={12} />}

      {items.length !== 0 && isLoading && <LinearProgress />}
      {items.map((item) => {
        if ('subItems' in item) {
          return item.subItems.length > 0 ? renderSubItems(item) : null
        }
        return renderListItem(item)
      })}
      {items.length === 0 && !isLoading && (
        <Box sx={{ padding: '1rem 2rem' }}>
          <Typography variant="body1">No items to show</Typography>
        </Box>
      )}
    </Box>
  )
}

export default React.memo(FilterList)
