import Image from 'next/image'

import { Box, Button, Typography } from 'lion-ui'
import appError from '@assets/icons/app-error.svg'

export const ErrorFallback = ({
  title,
  subtitle,
  action
}: {
  title: string
  subtitle: string
  action?: () => void
}) => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: action ? '90vh' : '',
        gap: '1rem'
      }}
    >
      <h1>{title}</h1>
      <Image
        width="500"
        height={500}
        src={appError ?? 'default.svg'}
        alt="A broken robot depicting an error in the application"
        priority
      />
      <Typography sx={{ textAlign: 'center' }}>{subtitle}</Typography>
      {action && (
        <Button variant="contained" color="primary" type="button" onClick={action}>
          Try again?
        </Button>
      )}
    </Box>
  )
}
