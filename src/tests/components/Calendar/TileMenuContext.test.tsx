import { render, screen, fireEvent } from '@testing-library/react'
import TileContextMenu from '@components/Calendar/TileContextMenu/TileContextMenu'
import { mockAssignment } from '../../__mocks__/assignments.mock'
import { UPDATE_ASSIGNMENT_MUTATION } from '@gql/mutations/assignment.mutation'
import { ApolloClient, InMemoryCache } from '@apollo/client'
import { MockedProvider } from '@apollo/client/testing'

let apolloClientInit: ApolloClient<unknown>
const cache = new InMemoryCache()

const mocks = [
  {
    request: {
      query: UPDATE_ASSIGNMENT_MUTATION,
      variables: {
        assignment: {
          externalId: '123',
          hoursPerDay: 8,
          startDate: '2024-10-31',
          dueDate: '2024-12-30',
          userId: undefined,
          userLoggedInExternalId: '10082922',
          isSplit: true
        }
      }
    },
    result: {
      data: {
        updateAssignment: true
      }
    }
  }
]

describe('TileContextMenu', () => {
  const mockProps = {
    anchorPosition: { mouseX: 0, mouseY: 0 },
    handleClose: jest.fn(),
    assignment: mockAssignment[0],
    onOpenRemoveModal: jest.fn()
  }

  beforeEach(() => {
    apolloClientInit = new ApolloClient({
      cache
    })
    apolloClientInit.writeQuery({
      query: UPDATE_ASSIGNMENT_MUTATION,
      variables: {
        assignment: {
          externalId: '123',
          hoursPerDay: 8,
          startDate: '2024-10-31',
          dueDate: '2024-12-30',
          userId: undefined,
          userLoggedInExternalId: '10082922',
          isSplit: true
        }
      },
      data: {
        updateAssignment: true
      }
    })
    jest.clearAllMocks()
  })

  it('should render the "Split" menu item when assignment width is greater than or equal to 2', () => {
    const propsWithLargeAssignment = {
      ...mockProps,
      assignment: { ...mockProps.assignment, width: 2 }
    }
    render(
      <MockedProvider mocks={mocks} cache={cache} addTypename={false}>
        <TileContextMenu {...propsWithLargeAssignment} />
      </MockedProvider>
    )
    expect(screen.getByText('Split')).toBeInTheDocument()
  })

  it('should not render the "Split" menu item when assignment width is less than 2', () => {
    const propsWithSmallAssignment = {
      ...mockProps,
      assignment: { ...mockProps.assignment, width: 1 }
    }
    render(
      <MockedProvider mocks={mocks} cache={cache} addTypename={false}>
        <TileContextMenu {...propsWithSmallAssignment} />
      </MockedProvider>
    )
    expect(screen.queryByText('Split')).not.toBeInTheDocument()
  })

  it('should call onOpenRemoveModal when "Remove allocation" is clicked', () => {
    render(
      <MockedProvider mocks={mocks} cache={cache} addTypename={false}>
        <TileContextMenu {...mockProps} />
      </MockedProvider>
    )
    fireEvent.click(screen.getByText('Remove allocation'))
    expect(mockProps.onOpenRemoveModal).toHaveBeenCalled()
  })

  it('should call handleClose when "Split" is clicked', () => {
    const propsWithLargeAssignment = {
      ...mockProps,
      assignment: { ...mockProps.assignment, width: 2 }
    }
    render(
      <MockedProvider mocks={mocks} cache={cache} addTypename={false}>
        <TileContextMenu {...propsWithLargeAssignment} />
      </MockedProvider>
    )
    fireEvent.click(screen.getByText('Split'))
    expect(mockProps.handleClose).toHaveBeenCalled()
  })

  it('should not render the context menu when anchorPosition is null', () => {
    const propsWithNullAnchor = {
      ...mockProps,
      anchorPosition: null
    }
    render(
      <MockedProvider mocks={mocks} cache={cache} addTypename={false}>
        <TileContextMenu {...propsWithNullAnchor} />
      </MockedProvider>
    )
    expect(screen.queryByText('Remove allocation')).not.toBeInTheDocument()
  })
})
