import { render, screen } from '@testing-library/react'
import { MockedProvider } from '@apollo/client/testing'

import CalendarUtilization from '@components/Calendar/CalendarUtilization/CalendarUtilization'
import CALENDAR_QUERY from '@gql/queries/calendar.query'

import { activeDatesVar } from '@gql/client/localState'
import apolloClientInit from '@gql/client/apolloClientInit'
import { mockAssignment } from '../../__mocks__/assignments.mock'
import { type Resource } from '@typeDefs/Resource'
import dayjs from 'dayjs'

describe('CalendarUtilization', () => {
  const resource: Resource = {
    id: '1',
    name: '<PERSON>',
    location: 'New York',
    position: 'Production Artist',
    jobTitle: 'Production Artist (L1)',
    profitCenter: 'The Pub Chg/Se',
    altairNumber: 'L01035',
    totalCapacity: 80,
    minimumAgencyHoursPerDay: 8,
    timeOffDetails: [],
    holidays: [],
    requiresAssignApproval: true,
  }
  beforeEach(() => {
    activeDatesVar({ startDate: '2024-07-15', endDate: '2024-07-28' })
    apolloClientInit.writeQuery({
      query: CALENDAR_QUERY,
      variables: {
        params: activeDatesVar()
      },
      data: {
        getCalendarData: [
          {
            month: 'July',
            days: [
              {
                dayShortName: 'Mo',
                dayMiddleName: 'Mon',
                dayNumber: 15,
                date: '2024-07-15',
                isWeekend: false
              },
              {
                dayShortName: 'Tu',
                dayMiddleName: 'Tue',
                dayNumber: 16,
                date: '2024-07-16',
                isWeekend: false
              },
              {
                dayShortName: 'We',
                dayMiddleName: 'Wed',
                dayNumber: 17,
                date: '2024-07-17',
                isWeekend: false
              },
              {
                dayShortName: 'Th',
                dayMiddleName: 'Thu',
                dayNumber: 18,
                date: '2024-07-18',
                isWeekend: false
              },
              {
                dayShortName: 'Fr',
                dayMiddleName: 'Fri',
                dayNumber: 19,
                date: '2024-07-19',
                isWeekend: false
              },
              {
                dayShortName: 'Sa',
                dayMiddleName: 'Sat',
                dayNumber: 20,
                date: '2024-07-20',
                isWeekend: true
              },
              {
                dayShortName: 'Su',
                dayMiddleName: 'Sun',
                dayNumber: 21,
                date: '2024-07-21',
                isWeekend: true
              },
              {
                dayShortName: 'Mo',
                dayMiddleName: 'Mon',
                dayNumber: 22,
                date: '2024-07-22',
                isWeekend: false
              },
              {
                dayShortName: 'Tu',
                dayMiddleName: 'Tue',
                dayNumber: 23,
                date: '2024-07-23',
                isWeekend: false
              },
              {
                dayShortName: 'We',
                dayMiddleName: 'Wed',
                dayNumber: 24,
                date: '2024-07-24',
                isWeekend: false
              },
              {
                dayShortName: 'Th',
                dayMiddleName: 'Thu',
                dayNumber: 25,
                date: '2024-07-25',
                isWeekend: false
              },
              {
                dayShortName: 'Fr',
                dayMiddleName: 'Fri',
                dayNumber: 26,
                date: '2024-07-26',
                isWeekend: false
              },
              {
                dayShortName: 'Sa',
                dayMiddleName: 'Sat',
                dayNumber: 27,
                date: '2024-07-27',
                isWeekend: true
              },
              {
                dayShortName: 'Su',
                dayMiddleName: 'Sun',
                dayNumber: 28,
                date: '2024-07-28',
                isWeekend: true
              }
            ]
          }
        ]
      }
    })
  })

  it('renders without errors', () => {
    const { container } = render(
      <MockedProvider addTypename={false} cache={apolloClientInit.cache}>
        <CalendarUtilization assignments={mockAssignment} resource={resource} />
      </MockedProvider>
    )
    expect(container).toBeTruthy()
  })

  it('displays the correct number of cells', () => {
    render(
      <MockedProvider addTypename={false} cache={apolloClientInit.cache}>
        <CalendarUtilization assignments={mockAssignment} resource={resource} />
      </MockedProvider>
    )
    const assignmentsWithEightHours = screen.getAllByText(/0h/)
    expect(assignmentsWithEightHours.length).toBe(3)
    const assignmentsWithTwoHours = screen.getAllByText(/2h/)
    expect(assignmentsWithTwoHours.length).toBe(1)
  })

  it('displays the sum hours by day', () => {
    const assignments = [
      {
        ...mockAssignment[0],
        dueDate: '2024-07-18',
        startDate: '2024-07-18',
        width: 1,
        x: 3,
        y: 0
      },
      {
        ...mockAssignment[1],
        dueDate: '2024-07-18',
        hoursPerDay: 6,
        height: 6,
        startDate: '2024-07-16'
      }
    ]
    render(
      <MockedProvider addTypename={false} cache={apolloClientInit.cache}>
        <CalendarUtilization assignments={assignments} resource={resource} />
      </MockedProvider>
    )

    const assignmentsWithEightHours = screen.getAllByText(/2h/)
    expect(assignmentsWithEightHours.length).toBe(2)
    const assignmentsWithTenHours = screen.getAllByText(/0h/)
    expect(assignmentsWithTenHours.length).toBe(1)
  })

  it('displays the sum hours by day if exceed the working hours', () => {
    const assignments = [
      {
        ...mockAssignment[0],
        dueDate: '2024-07-18',
        startDate: '2024-07-18',
        x: 3,
        y: 0
      },
      {
        ...mockAssignment[1],
        dueDate: '2024-07-18',
        startDate: '2024-07-16'
      }
    ]
    render(
      <MockedProvider addTypename={false} cache={apolloClientInit.cache}>
        <CalendarUtilization assignments={assignments} resource={resource} />
      </MockedProvider>
    )

    const assignmentsWithEightHours = screen.getAllByText(/0h/)
    expect(assignmentsWithEightHours.length).toBe(2)
    const assignmentsWithTenHours = screen.getAllByText(/-2h/)
    expect(assignmentsWithTenHours.length).toBe(1)
  })

  it('correctly identifies if a calendar day is in time off details', () => {
    const resourceWithTimeOff = {
      ...resource,
      timeOffDetails: [
        { absenceDate: '2024-07-18', user: 'John Doe', timeOffHours: '8', absenceStatus: 'Approved' },
        { absenceDate: '2024-07-20', user: 'John Doe', timeOffHours: '8', absenceStatus: 'Approved' }
      ]
    }

    render(
      <MockedProvider addTypename={false} cache={apolloClientInit.cache}>
        <CalendarUtilization assignments={mockAssignment} resource={resourceWithTimeOff} />
      </MockedProvider>
    )

    const isDayInTimeOff = (calendarDay: string) =>
      resourceWithTimeOff.timeOffDetails.some(({ absenceDate }) => dayjs(calendarDay).isSame(absenceDate, 'day'))

    expect(isDayInTimeOff('2024-07-18')).toBe(true)
    expect(isDayInTimeOff('2024-07-20')).toBe(true)
    expect(isDayInTimeOff('2024-07-19')).toBe(false)
  })
})
