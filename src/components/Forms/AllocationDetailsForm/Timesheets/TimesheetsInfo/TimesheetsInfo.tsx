import { Box, Typography } from 'lion-ui'
import { type TimesheetsInfoProps } from './TimesheetsInfo.props'
import TSPlusIcon from '@components/Icons/timesheetIcons/TSPlusIcon'
import TSMinusIcon from '@components/Icons/timesheetIcons/TSMinusIcon'
import TSXIcon from '@components/Icons/timesheetIcons/TSXIcon'
import TSCheckIcon from '@components/Icons/timesheetIcons/TSCheckIcon'
import dayjs from 'dayjs'
import addApostrophe from '@utils/nameApostropheAdd'

const TimesheetsInfo = ({ timesheet }: TimesheetsInfoProps) => {
  const { hours, user, timesheetDate } = timesheet
  const timesheetStatus = timesheet.timesheetStatus.toLowerCase()
  let message = <></>

  if (timesheetStatus === 'submitted') {
    message = (
      <>
        {user} has{' '}
        <Typography component="span" fontWeight={700} fontSize={12}>
          submitted {hours} hours{' '}
        </Typography>
        for approval.
      </>
    )
  } else if (timesheetStatus === 'unsubmitted') {
    message = (
      <>
        {user} has{' '}
        <Typography component="span" fontWeight={700} fontSize={12}>
          entered {hours} hours{' '}
        </Typography>
        but hasn&apos;t submitted them yet.
      </>
    )
  } else if (timesheetStatus === 'rejected') {
    message = (
      <>
        {addApostrophe(user)}{' '}
        <Typography component="span" fontWeight={700} fontSize={12}>
          {hours} hours{' '}
        </Typography>
        entry has been{' '}
        <Typography component="span" fontWeight={700} fontSize={12}>
          rejected
        </Typography>
        .
      </>
    )
  } else if (timesheetStatus === 'approved') {
    message = (
      <>
        {addApostrophe(user)}{' '}
        <Typography component="span" fontWeight={700} fontSize={12}>
          {hours} hours{' '}
        </Typography>
        entry has been{' '}
        <Typography component="span" fontWeight={700} fontSize={12}>
          approved
        </Typography>
        .
      </>
    )
  }

  return (
    <Box
      sx={{
        display: 'flex',
        flexWrap: 'nowrap',
        gap: 2,
        alignItems: 'center',
        width: '100%'
      }}
    >
      {timesheetStatus === 'submitted' && <TSPlusIcon width="32" />}
      {timesheetStatus === 'unsubmitted' && <TSMinusIcon width="32" />}
      {timesheetStatus === 'rejected' && <TSXIcon width="32" />}
      {timesheetStatus === 'approved' && <TSCheckIcon width="32" />}

      <Box sx={{ display: 'flex', flexWrap: 'nowrap', justifyContent: 'space-between', width: '100%', paddingY: 5 }}>
        <Typography fontSize={12}>{message}</Typography>
        <Typography fontSize={12} fontWeight={700}>
          {dayjs(timesheetDate).format('DD MMM')}
        </Typography>
      </Box>
    </Box>
  )
}

export default TimesheetsInfo
