import { gql } from '@apollo/client'

const resourceFields = `
  hasNextPage
  hasPreviousPage
  totalCount
  totalPages
  pageNumber
  items {
    id
    integrationId
    name
    location
    position
    jobTitle
    profitCenter
    altairNumber
    totalCapacity
    minimumAgencyHoursPerDay
    workCode
    agencyName
    agencyCode
    timeOffDetails {
      user
      absenceDate
      timeOffHours
      absenceStatus
    }
    holidays {
      holidayDate
      holidayName
      holidayCode
    }
    requiresAssignApproval
  }
`

const SINGLE_RESOURCE_QUERY = gql`
  query getResourceById($resourceId: ID) {
    getResourceById(resourceId: $resourceId) {
      id
      name
      location
      position
      jobTitle
      profitCenter
      altairNumber
      totalCapacity
      minimumAgencyHoursPerDay
      agencyName
    }
  }
`

const RESOURCES_QUERY = gql`
  query Resources($params: ResourcesParams) {
    resources(params: $params) {
      ${resourceFields}
    }
  }
`
const RESOURCES_FILTERS_QUERY = gql`
  query ResourcesByFilters($params: ResourcesByFiltersParams) {
    resourcesByFilters(params: $params) {
      ${resourceFields}
    }
  }
`

const RESOURCE_ORG_STRUCTURE_QUERY = gql`
  query GetResourceOrgStructure($resourceId: ID) {
    getResourceOrgStructure(resourceId: $resourceId) {
      agencyName
      costCenterName
      locationName
      agencyCode
      costCenterCode
    }
  }
`

const ORG_STRUCTURE_AGENCY_COST_CENTER_QUERY = gql`
  query GetOrgStructureByAgencyCodeCostCenterCode($params: OrgParams) {
    getOrgStructureByAgencyCodeCostCenterCode(params: $params) {
      agencyName
      costCenterName
      locationName
    }
  }
`

const ORG_STRUCTURE_AGENCIES_COST_CENTERS_QUERY = gql`
  query GetOrgStructureByAgencyCodesCostCentersCode($params: [OrgParams]) {
    getOrgStructureByAgencyCodesCostCenterCodes(params: $params) {
      agencyName
      costCenterName
      locationName
    }
  }
`

const SEARCH_RESOURCE_AGENCY_QUERY = gql`
  query SearchAgencyOrgStructure($searchTerm: String) {
    searchAgencyOrgStructure(searchTerm: $searchTerm) {
      agencyCode
      agencyName
    }
  }
`

const AGENCY_LOCATION_QUERY = gql`
  query Query($agencyCode: String!) {
    getLocationByAgencyOrgStructure(agencyCode: $agencyCode)
  }
`

const AGENCY_LOCATIONS_QUERY = gql`
  query Query($agencyCodes: [String]) {
    getLocationsByAgenciesOrgStructure(agencyCodes: $agencyCodes)
  }
`

const COST_CENTER_QUERY = gql`
  query GetCostCenterByLocationOrgStructure($agencyCode: String!, $city: String) {
    getCostCenterByLocationOrgStructure(agencyCode: $agencyCode, city: $city) {
      costCenterCode
      costCenterName
    }
  }
`

const COST_CENTERS_QUERY = gql`
  query GetCostCentersByLocationsOrgStructure($agencyCodes: [String], $cities: [String]) {
    getCostCentersByLocationsOrgStructure(agencyCodes: $agencyCodes, cities: $cities) {
      costCenterCode
      costCenterName
    }
  }
`

const HOLIDAYS_AND_TIMEOFFDETAILS_QUERY = gql`
  query GetHolidaysAndTimeOffDetailsByResourceId($params: HolidayAndTimeOffParams) {
    getHolidaysAndTimeOffDetailsByResourceId(params: $params) {
      timeOffDetails {
        user
        absenceDate
        timeOffHours
        absenceStatus
      }
      holidays {
        holidayDate
        holidayName
        holidayCode
      }
    }
  }
`

const RESOURCES_AGENCY_QUERY = gql`
  query GetOrgStructureByResourceAssignments($params: GetOrgStructureByResourceAssignmentsParams) {
    getOrgStructureByResourceAssignments(params: $params) {
      agencies {
        agencyCode
        agencyName
      }
      costCenters {
        costCenterCode
        costCenterName
      }
      locations
    }
  }
`

export {
  SINGLE_RESOURCE_QUERY,
  RESOURCES_QUERY,
  RESOURCES_FILTERS_QUERY,
  RESOURCE_ORG_STRUCTURE_QUERY,
  SEARCH_RESOURCE_AGENCY_QUERY,
  AGENCY_LOCATION_QUERY,
  AGENCY_LOCATIONS_QUERY,
  COST_CENTER_QUERY,
  HOLIDAYS_AND_TIMEOFFDETAILS_QUERY,
  ORG_STRUCTURE_AGENCIES_COST_CENTERS_QUERY,
  ORG_STRUCTURE_AGENCY_COST_CENTER_QUERY,
  COST_CENTERS_QUERY,
  RESOURCES_AGENCY_QUERY
}
