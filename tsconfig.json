{"compilerOptions": {"target": "es2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": "./src", "paths": {"@components/*": ["components/*"], "@utils/*": ["utils/*"], "@redux/*": ["redux/*"], "@hooks/*": ["hooks/*"], "@typeDefs/*": ["typeDefs/*"], "@styles/*": ["styles/*"], "@constants/*": ["constants/*"], "@theme/*": ["theme/*"], "@assets/*": ["assets/*"], "@gql/*": ["gql/*"], "@model/*": ["model/*"], "@context/*": ["context/*"], "@auth/*": ["auth/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "**/*.test.tsx", "src/**/*.tsx", "next.config.js", "src/pages/api/auth/[...nextauth].ts"], "exclude": ["node_modules"]}