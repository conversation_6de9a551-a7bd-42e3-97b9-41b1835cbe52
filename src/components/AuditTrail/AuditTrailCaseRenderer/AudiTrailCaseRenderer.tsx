import type React from 'react'
import { Box } from 'lion-ui'
import { AuditTrailCases } from '@constants/AuditTrailCases'
import type AuditTrailCaseRendererProps from './AuditTrailCaseRenderer.props'
import UpdateHours from '@components/AuditTrail/AuditTrailContentCases/UpdateHours'
import UpdateDuration from '@components/AuditTrail/AuditTrailContentCases/UpdateDuration'
import DeleteAssignment from '@components/AuditTrail/AuditTrailContentCases/DeleteAssignment'
import UpdateDurationAndHours from '@components/AuditTrail/AuditTrailContentCases/UpdateDurationAndHours'
import UpdateResource from '../AuditTrailContentCases/UpdateResource'
import UpdateResourceHours from '../AuditTrailContentCases/UpdateResourceHours'
import UpdateResourceDates from '../AuditTrailContentCases/UpdateResourceDates'
import UpdateResourceDatesHours from '../AuditTrailContentCases/UpdateResourceDatesHours'
import UpdateAssignmentStatus from '../AuditTrailContentCases/UpdateAssignmentStatus'

const AudiTrailCaseRenderer = ({ entry, isOpen, isCollapsed }: AuditTrailCaseRendererProps): React.JSX.Element => {
  switch (entry.action) {
    case AuditTrailCases.UPDATE_HOURS:
      return <UpdateHours entry={entry} isOpen={isOpen} isCollapsed={isCollapsed} />

    case AuditTrailCases.UPDATE_START_DATE:
      return <UpdateDuration entry={entry} isOpen={isOpen} isCollapsed={isCollapsed} />
    case AuditTrailCases.UPDATE_END_DATE:
      return <UpdateDuration entry={entry} isOpen={isOpen} isCollapsed={isCollapsed} />
    case AuditTrailCases.UPDATE_START_END_DATES:
      return <UpdateDuration entry={entry} isOpen={isOpen} isCollapsed={isCollapsed} />
    case AuditTrailCases.UPDATE_START_DATE_HOURS:
      return <UpdateDurationAndHours entry={entry} isOpen={isOpen} isCollapsed={isCollapsed} />
    case AuditTrailCases.UPDATE_END_DATE_HOURS:
      return <UpdateDurationAndHours entry={entry} isOpen={isOpen} isCollapsed={isCollapsed} />
    case AuditTrailCases.UPDATE_START_END_DATES_HOURS:
      return <UpdateDurationAndHours entry={entry} isOpen={isOpen} isCollapsed={isCollapsed} />
    case AuditTrailCases.DELETE:
      return <DeleteAssignment entry={entry} isOpen={isOpen} isCollapsed={isCollapsed} />
    case AuditTrailCases.UPDATE_RESOURCE:
      return <UpdateResource entry={entry} isOpen={isOpen} isCollapsed={isCollapsed} />
    case AuditTrailCases.UPDATE_RESOURCE_HOURS:
      return <UpdateResourceHours entry={entry} isOpen={isOpen} isCollapsed={isCollapsed} />
    case AuditTrailCases.UPDATE_RESOURCE_DATES:
      return <UpdateResourceDates entry={entry} isOpen={isOpen} isCollapsed={isCollapsed} />
    case AuditTrailCases.UPDATE_RESOURCE_DATES_HOURS:
      return <UpdateResourceDatesHours entry={entry} isOpen={isOpen} isCollapsed={isCollapsed} />
    case AuditTrailCases.UPDATE_ASSIGNMENT_STATUS:
      return <UpdateAssignmentStatus entry={entry} isOpen={isOpen} isCollapsed={isCollapsed} />
    default:
      return <Box>CASE NOT COVERED</Box>
  }
}

export default AudiTrailCaseRenderer
