import * as msal from '@azure/msal-browser'
import { getAuthEnvConfig } from '@utils/environments'

const { clientId, redirectUri, tenantId } = getAuthEnvConfig()

const msalConfig: msal.Configuration = {
  auth: {
    clientId,
    authority: `https://login.microsoftonline.com/${tenantId}`,
    redirectUri
  },
  cache: {
    cacheLocation: 'sessionStorage',
    storeAuthStateInCookie: true
  },
  system: {
    loggerOptions: {
      loggerCallback(logLevel, message, containsPii) {
        if (containsPii) return
        switch (logLevel) {
          case msal.LogLevel.Error:
            console.error(message)
            break
          case msal.LogLevel.Info:
            console.info(message)
            break
          case msal.LogLevel.Verbose:
            console.debug(message)
            break
          case msal.LogLevel.Warning:
            console.warn(message)
            break
        }
      },
      logLevel: msal.LogLevel.Verbose
    }
  }
}

const msalInstance = new msal.PublicClientApplication(msalConfig)

export { msalInstance }
