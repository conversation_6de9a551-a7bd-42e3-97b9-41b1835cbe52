import getDateRange from '../../utils/getDateRange'

describe('getDateRange function', () => {
  it('should return correct date range for a given number of weeks and base date', () => {
    const baseDate = '2023-04-04'
    const numOfWeeks = 2

    const expectedStartDate = '2023-04-03'
    const expectedEndDate = '2023-04-16'

    const { startDate, endDate } = getDateRange(numOfWeeks, baseDate)

    expect(startDate).toBe(expectedStartDate)
    expect(endDate).toBe(expectedEndDate)
  })

  it('should handle cases when base date is a Sunday', () => {
    // Example base date is a Sunday
    const baseDate = '2023-04-02'
    const numOfWeeks = 1

    const expectedStartDate = '2023-03-27'
    const expectedEndDate = '2023-04-02'

    const { startDate, endDate } = getDateRange(numOfWeeks, baseDate)

    expect(startDate).toBe(expectedStartDate)
    expect(endDate).toBe(expectedEndDate)
  })

  it('should correctly calculate range when numOfWeeks is more than 1 and base date is at the week start', () => {
    const baseDate = '2023-04-03'
    const numOfWeeks = 3

    const expectedStartDate = '2023-04-03'
    const expectedEndDate = '2023-04-23'

    const { startDate, endDate } = getDateRange(numOfWeeks, baseDate)

    expect(startDate).toBe(expectedStartDate)
    expect(endDate).toBe(expectedEndDate)
  })
})
