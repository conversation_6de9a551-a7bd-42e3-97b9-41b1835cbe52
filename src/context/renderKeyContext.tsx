// RenderKeyContext.tsx
import { createContext, useMemo, useState } from 'react'
import type { <PERSON>actN<PERSON>, Dispatch, SetStateAction } from 'react'

interface RenderKeyContextProps {
  children: ReactNode
}

interface RenderKeyContextState {
  renderKey: number
  setRenderKey: Dispatch<SetStateAction<number>>
}

const defaultValue: RenderKeyContextState = {
  renderKey: Date.now(),
  setRenderKey: () => {} // Provide a default empty function
}

const RenderKeyContext = createContext<RenderKeyContextState>(defaultValue)

export const RenderKeyContextProvider = ({ children }: RenderKeyContextProps) => {
  const [renderKey, setRenderKey] = useState<number>(Date.now())

  const value = useMemo(() => ({ renderKey, setRenderKey }), [renderKey, setRenderKey])

  return <RenderKeyContext.Provider value={value}>{children}</RenderKeyContext.Provider>
}

export default RenderKeyContext
