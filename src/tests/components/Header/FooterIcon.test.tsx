import { render } from '@testing-library/react'
import FooterIcon from '@components/Header/FooterIcon/FooterIcon'

describe('FooterIcon', () => {
  it('renders without crashing', () => {
    const { container } = render(<FooterIcon />)
    expect(container).toBeInTheDocument()
  })

  it('renders children correctly', () => {
    const { getByText } = render(<FooterIcon>Test Child</FooterIcon>)
    expect(getByText('Test Child')).toBeInTheDocument()
  })
})
