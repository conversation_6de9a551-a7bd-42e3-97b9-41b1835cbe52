import { activeSortVar } from '@gql/client/localState'
import { RESOURCES_QUERY } from '@gql/queries/resources.query'

export const mockResourcesQuery = {
  query: RESOURCES_QUERY,
  variables: {
    params: {
      userId: '10082922',
      pageNumber: 1,
      pageSize: 50,
      sort: { field: activeSortVar().field, order: activeSortVar().order }
    }
  },
  data: {
    resources: {
      items: [
        {
          id: 'resource-1',
          name: 'Resource one',
          totalCapacity: 0,
          minimumAgencyHoursPerDay: 8
        }
      ],
      totalPages: 1
    }
  }
}
