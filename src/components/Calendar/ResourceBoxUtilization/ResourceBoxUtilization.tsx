import { palette, Typography } from 'lion-ui'

// Type imports
import { type ResourceBoxUtilizationProps } from './ResourceBoxUtilization.props'

// Hooks import
import useAssignments from '@hooks/useAssignments'

const ResourceBoxUtilization = ({ totalCapacity, assignments }: ResourceBoxUtilizationProps) => {
  const { totalAssignedHours } = useAssignments(assignments)
  const availableCapacity = Math.round(totalCapacity - totalAssignedHours)

  const getStyles = (availableCapacity: number) => {
    if (availableCapacity > 0) {
      return {
        backgroundColor: palette['Success-Surface'].main,
        color: palette['Success-Dark'].dark
      }
    } else {
      return {
        backgroundColor: palette['Error-Surface'].main,
        color: palette['Error-Dark'].dark
      }
    }
  }

  const styles = getStyles(availableCapacity)

  return (
    <Typography
      variant="OverlineM"
      sx={{
        paddingX: '0.75rem',
        paddingY: 1,
        textAlign: 'center',
        backgroundColor: styles.backgroundColor,
        color: styles.color,
        borderRadius: '6px',
        fontWeight: 500,
        height: '1.75rem'
      }}
      data-testid="resource-box-utilization"
    >
      {availableCapacity}H
    </Typography>
  )
}

export default ResourceBoxUtilization
