/* eslint-disable testing-library/no-node-access */
import { render, screen, fireEvent } from '@testing-library/react'

import FormHeader from '@components/Forms/Common/FormHeader/FormHeader'
import { palette } from 'lion-ui'

describe('FormHeader Component', () => {
  const defaultProps = {
    title: 'Test Title',
    projectName: 'Test Project',
    taskName: 'Test Task',
    state: 'In-Progress',
    stateStyle: 'inprogress',
    closeIconLabel: 'Close Icon Modal',
    onClose: jest.fn()
  }

  it('should render the title', () => {
    render(<FormHeader {...defaultProps} />)
    expect(screen.getByText('Test Title')).toBeInTheDocument()
  })

  it('should render the project name and task name', () => {
    render(<FormHeader {...defaultProps} />)
    expect(screen.getByText('Test Project')).toBeInTheDocument()
    expect(screen.getByText('Test Task')).toBeInTheDocument()
  })

  it('should render the state chip with correct styles', () => {
    render(<FormHeader {...defaultProps} />)
    const chip = screen.getByText('In-Progress')
    expect(chip).toBeInTheDocument()
    if (chip.parentElement?.parentElement) {
      expect(chip.parentElement.parentElement).toHaveStyle('background-color: rgb(173, 213, 203)')
      expect(chip.parentElement.parentElement).toHaveStyle(`color:${palette['Success-Dark'].dark}`)
    }
  })

  it('should call onClose when close button is clicked', () => {
    render(<FormHeader {...defaultProps} />)
    const closeButton = screen.getByLabelText('Close Icon Modal')
    fireEvent.click(closeButton)
    expect(defaultProps.onClose).toHaveBeenCalled()
  })
})
