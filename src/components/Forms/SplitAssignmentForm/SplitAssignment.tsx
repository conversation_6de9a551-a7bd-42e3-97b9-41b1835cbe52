// External Libraries
import { Box, palette } from 'lion-ui'

// React and Apollo Client
import { useReactiveVar } from '@apollo/client'

// Type Definitions
import { type TileStatus } from '@typeDefs/Tile'
// Constants
import { TaskStates } from '@constants/taskStatus'

// Styles
import getCalendarItemStyle, { getTileStatus } from '@components/Calendar/CalendarDragItem/CalendarDragItem.style'

// Local State
import { resourceTypeVar, showSplitAssignmentFormVar } from '@gql/client/localState'

const SplitAssignment = ({ tileWidth, tileLeft }: { tileWidth?: number; tileLeft: number }) => {
  const assignment = useReactiveVar(showSplitAssignmentFormVar)
  const resourceType = useReactiveVar(resourceTypeVar)
  const taskStateKey = assignment?.taskState?.toLowerCase?.() ?? ''
  const tileStatus = getTileStatus(
    assignment?.assignmentState ?? '',
    (TaskStates as Record<string, string>)[taskStateKey] ?? '',
    resourceType ?? ''
  )
  const normalizeTileStatus = (status: string) => status.replace(/-/g, '').toLowerCase()

  return (
    <Box
      data-testid="split-assignment-box"
      sx={{
        padding: '0.5rem',
        position: 'absolute',
        top: '5rem',
        pointerEvents: 'none',
        left: `${tileLeft}px`,
        width: tileWidth ? `${tileWidth}px` : '100%'
      }}
    >
      <Box
        sx={{
          ...getCalendarItemStyle(normalizeTileStatus(tileStatus) as TileStatus),
          textAlign: 'right',
          padding: '0.5rem',
          pointerEvents: 'none',
          // eslint-disable-next-line @typescript-eslint/naming-convention
          '&::before':
            tileStatus === 'Rejected' || tileStatus === 'Approved'
              ? {
                  content: '""',
                  position: 'absolute',
                  left: '8px',
                  top: '8.5px',
                  height: '70%',
                  width: '6px',
                  backgroundColor: `${palette['500-Accent-4'].main}`,
                  borderRadius: '8px 0 0 8px'
                }
              : null
        }}
      >
        <span>{assignment?.hoursPerDay}h</span>
      </Box>
    </Box>
  )
}

export default SplitAssignment
