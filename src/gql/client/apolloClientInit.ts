import { ApolloClient, InM<PERSON>oryCache, HttpLink, from } from '@apollo/client'
import type { ServerError, ServerParseError } from '@apollo/client'
import fetch from 'cross-fetch'
import { setContext } from '@apollo/client/link/context'
import { onError } from '@apollo/client/link/error'
import { typePolicies, userProfileVar, employeeCodeVar } from '@gql/client/localState'
import { getAccessToken } from '@auth/msalUtil'

const acquireAccessToken = async (): Promise<string | undefined> => {
  return await getAccessToken()
}

const httpLink = new HttpLink({
  uri: `${process.env.NEXT_PUBLIC_BFF_URL}/`,
  fetch
})

const authLink = setContext(async (_, { headers }) => {
  const token = await getAccessToken()

  if (token == null || token === '') {
    return { headers }
  }

  const employeeCode = employeeCodeVar()
  const authHeaders: Record<string, string> = {
    ...headers,
    authorization: `Bearer ${token}`,
    profile: userProfileVar()
  }

  // Add x-empCode header if employeeCode is available
  if (employeeCode) {
    authHeaders['x-empCode'] = employeeCode
  }

  return {
    headers: authHeaders
  }
})

const unauthorizedResponseLink = onError(({ networkError, graphQLErrors }) => {
  console.log('networkError', networkError)
  console.log('graphQLErrors', graphQLErrors)

  const isServerError = (networkError as ServerError)?.statusCode === 401
  const isParseError = (networkError as ServerParseError)?.statusCode === 401

  if (networkError != null && (isServerError || isParseError)) {
    alert('no token')
    void acquireAccessToken()
  }

  if (graphQLErrors != null && graphQLErrors.some((error) => error.message === '401: Unauthorized')) {
    void acquireAccessToken()
  }
})

const link = from([unauthorizedResponseLink, authLink, httpLink])

const apolloClientInit = new ApolloClient({
  link,
  cache: new InMemoryCache({
    typePolicies
  })
})

export default apolloClientInit
