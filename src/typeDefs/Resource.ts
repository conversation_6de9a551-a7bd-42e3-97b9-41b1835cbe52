import { type Holiday } from './Holiday'
import { type TimeOffDetails } from './Timesheets'

export interface Resource {
  id: string
  name: string
  location: string
  position: string
  jobTitle: string
  profitCenter: string
  altairNumber: string
  totalCapacity: number
  minimumAgencyHoursPerDay: number
  workCode?: string
  agencyName?: string
  agencyCode?: string
  timeOffDetails: TimeOffDetails[]
  holidays: Holiday[]
  requiresAssignApproval: boolean
}

export interface ResourceOrgStructure {
  agencyName: string
  costCenterName: string
  locationName: string
  agencyCode: string
  costCenterCode: string
}

export interface ResourceHolidayAndTimeOffDetails {
  timeOffDetails: TimeOffDetails[]
  holidays: Holiday[]
}
