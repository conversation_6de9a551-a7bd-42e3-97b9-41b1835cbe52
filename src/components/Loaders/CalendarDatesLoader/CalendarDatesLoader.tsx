import { Box, Skeleton } from 'lion-ui'
import type CalendarDatesLoaderProps from './CalendarDatesLoader.props'

export const CalendarDatesLoader = (props: CalendarDatesLoaderProps) => {
  const { numOfCols } = props
  const rows = []

  for (let i = 0; i < numOfCols; i++) {
    rows.push(<Skeleton key={i} animation="wave" height={40} sx={{ width: `calc(50% / ${numOfCols})` }} />)
  }

  return (
    <Box
      data-testid="calendar-dates-loader"
      sx={{ display: 'flex', justifyContent: 'space-between', padding: '0 0.75rem 0 0.75rem' }}
    >
      {rows}
    </Box>
  )
}
