// React imports
import { useEffect, useState } from 'react'

// Dayjs import
import dayjs from 'dayjs'

// Lion ui imports
import { FormControl, LionAdvancedDateRangePicker, Stack } from 'lion-ui'

// GQL imports
import { activeDatesVar, numOfWeeksVar } from '@gql/client/localState'
import { useReactiveVar } from '@apollo/client'

// Helper function to create a new date from a string
const createDateFromString = (dateString: string): Date => new Date(`${dateString}T00:00:00`)

const DatePicker = () => {
  const [isSetFromDatePicker, setIsSetFromDatePicker] = useState(false)
  const activeDates = useReactiveVar(activeDatesVar)
  const numOfWeeks = useReactiveVar(numOfWeeksVar)

  const [dateState, setDateState] = useState<[Date, Date]>([
    createDateFromString(activeDates.startDate),
    createDateFromString(activeDates.endDate)
  ])

  const handleDateChange = (event: Array<{ startDate: Date; endDate: Date }> | undefined | Record<string, unknown>) => {
    if (event && Array.isArray(event) && event.length > 0) {
      const { startDate, endDate } = event[0]
      const calendarStartDate = dayjs(startDate).format('YYYY-MM-DD')
      const calendarEndDate = dayjs(endDate).format('YYYY-MM-DD')
      activeDatesVar({
        startDate: calendarStartDate,
        endDate: calendarEndDate
      })
      setIsSetFromDatePicker(true)
    }
  }

  useEffect(() => {
    if (!isSetFromDatePicker) {
      setDateState([createDateFromString(activeDates.startDate), createDateFromString(activeDates.endDate)])
    } else {
      setIsSetFromDatePicker(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeDates])

  return (
    <FormControl fullWidth>
      <Stack spacing={1}>
        <LionAdvancedDateRangePicker
          onDateChange={handleDateChange}
          buttonSize="small"
          startDate={dateState[0]}
          endDate={dateState[1]}
          dateRangeSelector={numOfWeeks === 4 ? 'fourWeeks' : 'twoWeeks'}
          selectMoreThanOneMonth={true}
          styles={{ padding: 2, fontSize: '0.5rem' }}
          buttonId={'Calendar.Header.DatePicker.Button'}
        />
      </Stack>
    </FormControl>
  )
}

export default DatePicker
