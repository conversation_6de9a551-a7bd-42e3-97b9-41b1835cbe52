/* eslint-disable @typescript-eslint/naming-convention */

import type { InitialOptionsTsJest } from 'ts-jest/dist/types'

const config: InitialOptionsTsJest = {
  preset: 'ts-jest/presets/js-with-ts-esm',
  setupFilesAfterEnv: ['<rootDir>/setupTests.ts'],
  transform: {
    '.+.(css|styl|less|sass|scss)$': 'jest-css-modules-transform'
  },
  moduleNameMapper: {
    '\\.(svg)$': 'jest-transform-stub',
    '^@assets/(.*)$': '<rootDir>/src/assets/$1',
    '^@components/(.*)$': '<rootDir>/src/components/$1',
    '^@constants/(.*)$': '<rootDir>/src/constants/$1',
    '^@contexts/(.*)$': '<rootDir>/src/contexts/$1',
    '^@hooks/(.*)$': '<rootDir>/src/hooks/$1',
    '^@interfaces/(.*)$': '<rootDir>/src/interfaces/$1',
    '^@layouts/(.*)$': '<rootDir>/src/layouts/$1',
    '^@pages/(.*)$': '<rootDir>/src/pages/$1',
    '^@services/(.*)$': '<rootDir>/src/services/$1',
    '^@styles/(.*)$': '<rootDir>/src/styles/$1',
    '^@tests/(.*)$': '<rootDir>/src/tests/$1',
    '^@utils/(.*)$': '<rootDir>/src/utils/$1',
    '^@gql/(.*)$': '<rootDir>/src/gql/$1',
    '^@auth/(.*)$': '<rootDir>/src/auth/$1',
    '^@redux/(.*)$': '<rootDir>/src/redux/$1',
    '^@context/(.*)$': '<rootDir>/src/context/$1',
    '^@typeDefs/(.*)$': '<rootDir>/src/typeDefs/$1'
  },
  transformIgnorePatterns: ['node_modules/(?!(gridstack)/)'],
  moduleFileExtensions: ['js', 'jsx', 'ts', 'tsx', 'json', 'node'],
  testEnvironment: 'jsdom',
  globals: {
    'ts-jest': {
      tsconfig: 'tsconfig.test.json',
      useESM: true
    }
  },
  testPathIgnorePatterns: ['<rootDir>/node_modules/', '<rootDir>/.next/']
}

export default config
