import { type Pagination } from './common/Pagination'
import { type NonWorkingDate } from './NonWorkingDate'

export interface Assignment {
  id: string
  startDate: string
  dueDate: string
  hoursPerDay: number
  blockedByTimesheet: boolean
  state: string
  userId: string
  projectId: string
  taskId: string
  activeNonWorkingDays: string[]
  x: number
  y: number
  width: number
  height: number
  totalDays: number
  taskName: string
  projectName: string
  externalLink: string
  altairJobNumber?: string
  isPlaceholder: boolean
  assignmentState: string
  projectManagerName: string
  taskState: string
  assignmentNotes: string
  taskNotes: string
  taskStartDate: string
  taskDueDate: string
  projectAgencyCode: string
  projectBrandCode: string
  projectAgencyName?: string
  projectIntegrationId: string
  userIntegrationId: string
  agencyName: string
  costCenterName: string
  locationName: string
  agencyCode: string
  costCenterCode: string
  assignmentIcaStatus: string
}

export interface AssignmentResponse extends Pagination {
  items: Assignment[]
}

export interface AssignmentApolloResponse extends Assignment {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  __typename?: string
}

export interface UpdateAssignmentParams {
  externalId: string
  startDate: string
  dueDate: string
  hoursPerDay: number
  userId: string
  userLoggedInExternalId: string
  updateAssignmentDate: string
  agencyName?: string
  locationName?: string
  costCenterName?: string
  agencyCode?: string
  costCenterCode?: string
  isSplit: boolean
  nonWorkingDates?: NonWorkingDate[]
}
