import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import AuditTrailContentEntry from '@components/AuditTrail/AuditTrailContentEntry/AuditTrailContentEntry'
import mockAuditTrailEntry from '../../__mocks__/auditTrailEntry.mock'
import getLocaleDateFormat from '@utils/getLocaleDateFormat'

jest.mock('@components/Icons/CloseIcon/CloseIcon', () => {
  const MockCloseIcon = () => <div data-testid="close-icon" />
  MockCloseIcon.displayName = 'MockCloseIcon'
  return MockCloseIcon
})
jest.mock('@components/Icons/ChevronIcon/ChevronIcon', () => {
  const MockChevronIcon = () => <div data-testid="chevron-icon" />
  MockChevronIcon.displayName = 'MockChevronIcon'
  return MockChevronIcon
})
jest.mock('@components/AuditTrail/AuditTrailCaseRenderer/AudiTrailCaseRenderer', () => {
  const MockAuditTrailCaseRenderer = () => <div data-testid="audit-trail-case-renderer" />
  MockAuditTrailCaseRenderer.displayName = 'MockAuditTrailCaseRenderer'
  return MockAuditTrailCaseRenderer
})

describe('AuditTrailContentEntry Component', () => {
  const mockOnDelete = jest.fn()
  const mockEntry = mockAuditTrailEntry

  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('renders all elements correctly', () => {
    render(<AuditTrailContentEntry onDelete={mockOnDelete} entry={mockEntry} />)

    // Check for the date
    expect(screen.getByText(getLocaleDateFormat(mockEntry.date))).toBeInTheDocument()

    // Check for the time
    expect(screen.getByText(mockEntry.time)).toBeInTheDocument()

    // Check for the ChevronIcon
    expect(screen.getByTestId('chevron-icon')).toBeInTheDocument()

    // Check for the AuditTrailCaseRenderer
    expect(screen.getAllByTestId('audit-trail-case-renderer')).toHaveLength(2) // Rendered in both summary and details
  })
})
