import { render, screen } from '@testing-library/react'
import SeeDetails from '@components/Forms/Common/SeeDetails/SeeDetails'

describe('SeeDetails Component', () => {
  test('renders SeeDetails with text', () => {
    render(<SeeDetails externalLink="https://example.com" showText={true} />)
    expect(screen.getByText('See details')).toBeInTheDocument()
    expect(screen.getByRole('link')).toHaveAttribute('href', 'https://example.com')
  })

  test('renders SeeDetails without text', () => {
    render(<SeeDetails externalLink="https://example.com" showText={false} />)
    expect(screen.queryByText('See details')).not.toBeInTheDocument()
    expect(screen.getByRole('link')).toHaveAttribute('href', 'https://example.com')
  })

  test('renders SeeDetails with default link when no externalLink is provided', () => {
    render(<SeeDetails externalLink="" showText={true} />)
    expect(screen.getByRole('link')).toHaveAttribute('href', '')
  })

  test('renders SeeDetails with correct aria-label', () => {
    render(<SeeDetails externalLink="https://example.com" showText={true} />)
    expect(screen.getByRole('button', { name: /See details/i })).toBeInTheDocument()
  })
})
