import { useEffect, useState } from 'react'
import { Box, Button, palette, Tab, Tabs } from 'lion-ui'
import { type FormTabsProps } from './FormTabs.props'
import AssignmentNotesIcon from '@components/Icons/AssignmentNotesIcon/AssignmentNotesIcon'
import TaskNotesIcon from '@components/Icons/TaskNotesIcon/TaskNotesIcon'
import ReadMoreIcon from '@components/Icons/ReadMoreIcon/ReadMoreIcon'
import CustomTabPanel from './CustomTabPanel'
import { sanitizeNotes, sanitizeNotesPreview } from '@utils/sanitizeNotes'
import { userProfileVar } from '@gql/client/localState'
import { UserProfile } from '@constants/userProfile'

const FormTabs = ({ taskNotes, assignmentNotes, openNotesView, isDisplayingNotes }: FormTabsProps) => {
  const sanitizedAssignmentNotes = sanitizeNotes(assignmentNotes)
  const sanitizedTaskNotes = sanitizeNotes(taskNotes)
  const [value, setValue] = useState(() => {
    if (assignmentNotes) return 0
    if (taskNotes) return 1
    return 0
  })

  const userProfile = userProfileVar()

  useEffect(() => {
    if (userProfile === UserProfile.LIGHT_USER) {
      setValue(1)
    }
  }, [userProfile])

  function a11yProps(index: number) {
    return {
      id: `simple-tab-${index}`,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'aria-controls': `simple-tabpanel-${index}`
    }
  }

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue)
  }

  return (
    <>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', width: '100%' }}>
        <Tabs value={value} onChange={handleChange} aria-label="Notes tabs component" sx={{ maxHeight: '3.8rem' }}>
          {userProfile !== UserProfile.LIGHT_USER && (
            <Tab
              icon={
                <Box>
                  <AssignmentNotesIcon width="18" isActive={value === 0 && !!assignmentNotes} />
                </Box>
              }
              iconPosition={'start'}
              label="Assignment Notes"
              {...a11yProps(0)}
              disabled={!assignmentNotes}
              data-testid="assignment-notes-tab"
            />
          )}
          <Tab
            icon={
              <Box>
                <TaskNotesIcon width="18" isActive={value === 1 && !!taskNotes} />
              </Box>
            }
            iconPosition={'start'}
            label="Task Notes"
            {...a11yProps(1)}
            disabled={!taskNotes}
            data-testid="task-notes-tab"
          />
        </Tabs>
      </Box>

      <CustomTabPanel value={value} index={0}>
        {!assignmentNotes && <Box>There are no notes available for this task</Box>}
        {assignmentNotes && (
          <Box
            sx={{
              width: '100%',
              paddingY: 2,
              paddingX: 3,
              border: isDisplayingNotes ? 'none' : '1px solid',
              borderColor: palette.secondary.main,
              borderRadius: '0.5rem',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}
          >
            <Box
              sx={{
                width: isDisplayingNotes ? '100%' : '28rem',
                textOverflow: 'ellipsis',
                overflow: 'hidden',
                whiteSpace: isDisplayingNotes ? 'wrap' : 'nowrap',
                fontSize: '0.875rem'
              }}
            >
              {isDisplayingNotes
                ? sanitizedAssignmentNotes.map(
                    (segment, index) => segment.trim() && <p key={`${segment}-${a11yProps(index).id}`}>{segment}</p>
                  )
                : sanitizeNotesPreview(assignmentNotes)}
            </Box>
            {!isDisplayingNotes && (
              <Button
                startIcon={<ReadMoreIcon width="16px" />}
                variant="text"
                onClick={() => {
                  openNotesView(true)
                }}
                sx={{
                  height: '28px',
                  width: '122px',
                  backgroundColor: palette.secondary.main
                }}
              >
                Read More
              </Button>
            )}
          </Box>
        )}
      </CustomTabPanel>

      <CustomTabPanel value={value} index={1}>
        {!taskNotes && <Box>There are no notes available for this task</Box>}
        {taskNotes && (
          <Box
            sx={{
              width: '100%',
              paddingY: 2,
              paddingX: 3,
              border: isDisplayingNotes ? 'none' : '1px solid',
              borderColor: palette.secondary.main,
              borderRadius: '0.5rem',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}
          >
            <Box
              sx={{
                width: isDisplayingNotes ? '100%' : '28rem',
                textOverflow: 'ellipsis',
                overflow: 'hidden',
                whiteSpace: isDisplayingNotes ? 'wrap' : 'nowrap',
                fontSize: '0.875rem'
              }}
            >
              {isDisplayingNotes
                ? sanitizedTaskNotes.map(
                    (segment, index) => segment.trim() && <p key={`${segment}-${a11yProps(index).id}`}>{segment}</p>
                  )
                : sanitizeNotesPreview(taskNotes)}
            </Box>
            {!isDisplayingNotes && (
              <Button
                startIcon={<ReadMoreIcon width="16px" />}
                variant="text"
                onClick={() => {
                  openNotesView(true)
                }}
                sx={{
                  height: '28px',
                  width: '122px',
                  backgroundColor: palette.secondary.main
                }}
              >
                Read More
              </Button>
            )}
          </Box>
        )}
      </CustomTabPanel>
    </>
  )
}

export default FormTabs
