import { Box, Skeleton } from 'lion-ui'
import type CalendarBodyLoaderProps from './CalendarBodyLoader.props'

export const CalendarBodyLoader = (props: CalendarBodyLoaderProps) => {
  const { numOfRows } = props
  const rows = []

  for (let i = 0; i < numOfRows; i++) {
    rows.push(
      <Box key={i} sx={{ display: 'flex', gap: 2, padding: 2 }}>
        <Box sx={{ marginRight: '1rem', borderRight: '1px solid #E0E0E0', paddingRight: '1rem' }}>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Skeleton animation="wave" width={200} height={40} />
            <Skeleton animation="wave" width={50} height={40} />
            <Skeleton animation="wave" width={20} height={40} />
          </Box>
          <Box sx={{ display: 'flex', gap: 8, alignItems: 'end' }}>
            <Box sx={{ display: 'flex', gap: 2, flexDirection: 'column' }}>
              <Skeleton animation="wave" width={150} height={20} />
              <Skeleton animation="wave" width={150} height={20} />
              <Skeleton animation="wave" width={150} height={20} />
            </Box>

            <Box sx={{ display: 'flex', gap: 2, flexDirection: 'column' }}>
              <Skeleton animation="wave" width={75} height={20} />
              <Skeleton animation="wave" width={75} height={20} />
            </Box>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', gap: 8, justifyContent: 'space-evenly', width: '100%' }}>
          <Skeleton animation="wave" height={140} sx={{ width: '45%' }} />
          <Skeleton animation="wave" height={140} sx={{ width: '45%' }} />
        </Box>
      </Box>
    )
  }

  return <>{rows}</>
}
