// UI library imports
import { Box } from 'lion-ui'

// Component imports
import CalendarUtilizationCell from '@components/Calendar/CalendarUtilizationCell/CalendarUtilizationCell'

// Types
import { type CalendarUtilizationProps } from './CalendarUtilization.props'

// Hooks import
import useCalendarDataCache from '@hooks/useCalendarDataCache'
import useAssignments from '@hooks/useAssignments'
import dayjs from 'dayjs'

const CalendarUtilization = ({ assignments, resource }: CalendarUtilizationProps) => {
  const { calendarDays } = useCalendarDataCache()
  const { calculateAssignedHoursByDay } = useAssignments(assignments)
  const { timeOffDetails, holidays } = resource

  const isCalendarDayMatching = (calendarDay: string, dates: Array<{ date: string }> | undefined): boolean => {
    return !!dates?.some(({ date }) => dayjs(calendarDay).isSame(date, 'day'))
  }

  const isCalendarDayInTimeOffDetails = (calendarDay: string) =>
    isCalendarDayMatching(
      calendarDay,
      timeOffDetails?.map(({ absenceDate }) => ({ date: absenceDate }))
    )

  const isCalendarDayHoliday = (calendarDay: string) => {
    return isCalendarDayMatching(
      calendarDay,
      holidays?.map(({ holidayDate }) => ({ date: holidayDate }))
    )
  }

  return (
    <Box
      sx={{
        position: 'absolute',
        width: '100%',
        height: '100%',
        top: 0,
        left: 0,
        zIndex: 0,
        display: 'flex'
      }}
    >
      {calendarDays?.map((calendarDay) => {
        return (
          <CalendarUtilizationCell
            key={calendarDay.date}
            hours={calculateAssignedHoursByDay(calendarDay)}
            numOfDaysInView={calendarDays.length}
            nonWorkingDay={
              calendarDay.isWeekend ||
              isCalendarDayInTimeOffDetails(calendarDay.date) ||
              isCalendarDayHoliday(calendarDay.date)
            }
            minimumAgencyHoursPerDay={resource.minimumAgencyHoursPerDay}
          />
        )
      })}
    </Box>
  )
}

export default CalendarUtilization
