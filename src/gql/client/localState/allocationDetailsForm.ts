// Apollo client imports
import { makeVar } from '@apollo/client'

import { type Assignment } from '@typeDefs/Assignments'
import { type Resource } from '@typeDefs/Resource'

export const showAllocationDetailsFormVar = makeVar<Assignment | undefined>(undefined)
export const resourceDetailsVar = makeVar<Resource | undefined>(undefined)
export const resourceTypeVar = makeVar<string | undefined>(undefined)
