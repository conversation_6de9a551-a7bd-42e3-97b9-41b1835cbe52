import { render, renderHook, screen } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import type ResourceBoxProps from '@components/Calendar/ResourceBox/ResourceBox.props'
import ResourceBox from '@components/Calendar/ResourceBox/ResourceBox'
import { activeDatesVar } from '@gql/client/localState'
import apolloClientInit from '@gql/client/apolloClientInit'
import CALENDAR_QUERY from '@gql/queries/calendar.query'
import { ApolloProvider } from '@apollo/client'

describe('ResourceBox Component', () => {
  const mockProps: ResourceBoxProps = {
    resource: {
      id: '1',
      name: '<PERSON>',
      location: 'New York',
      position: 'Production Artist',
      jobTitle: 'Production Artist (L1)',
      profitCenter: 'The Pub Chg/Se',
      altairNumber: '1111111',
      workCode: 'L01035',
      totalCapacity: 80,
      minimumAgencyHoursPerDay: 8,
      agencyCode: '001',
      agencyName: 'Agency Name',
      timeOffDetails: [],
      holidays: [],
      requiresAssignApproval: true
    },
    assignments: [],
    type: 'resource'
  }

  const mockResource = {
    totalCapacity: 40,
    timeOffDetails: [{ timeOffHours: 8 }, { timeOffHours: 4 }, { timeOffHours: 2 }]
  }

  beforeEach(() => {
    activeDatesVar({ startDate: '2024-07-15', endDate: '2024-07-28' })
    apolloClientInit.writeQuery({
      query: CALENDAR_QUERY,
      variables: {
        params: activeDatesVar()
      },
      data: {
        getCalendarData: []
      }
    })
  })

  test('renders ResourceBox with correct data', () => {
    render(
      <ApolloProvider client={apolloClientInit}>
        <ResourceBox {...mockProps} />
      </ApolloProvider>
    )
    expect(screen.getByText('Aaron Fisher')).toBeInTheDocument()
    expect(screen.getByText('New York')).toBeInTheDocument()
    expect(screen.getByText('Production Artist')).toBeInTheDocument()
    expect(screen.getByText('Production Artist (L1)')).toBeInTheDocument()
    expect(screen.getByText('L01035')).toBeInTheDocument()
  })

  test('renders ResourceBoxUtilization when type is resource', () => {
    render(
      <ApolloProvider client={apolloClientInit}>
        <ResourceBox {...mockProps} />
      </ApolloProvider>
    )
    expect(screen.getByText(`${mockProps.resource.totalCapacity}H`)).toBeInTheDocument()
  })

  test('does not render ResourceBoxUtilization when type is not resource', () => {
    const mockPropsWithDifferentType = {
      ...mockProps,
      type: 'non-resource'
    }
    render(
      <ApolloProvider client={apolloClientInit}>
        <ResourceBox {...mockPropsWithDifferentType} />
      </ApolloProvider>
    )
    expect(screen.queryByText('80')).not.toBeInTheDocument()
  })

  test('renders agency code and name correctly', () => {
    render(
      <ApolloProvider client={apolloClientInit}>
        <ResourceBox {...mockProps} />
      </ApolloProvider>
    )
    expect(screen.getByText(`${mockProps.resource.agencyCode} : ${mockProps.resource.agencyName}`)).toBeInTheDocument()
  })

  it('should calculate available capacity correctly when timeOffDetails are provided', () => {
    const calculateAvailableCapacity = (resource: typeof mockResource) => {
      const totalTimeOff = (resource.timeOffDetails || []).reduce((sum, detail) => sum + detail.timeOffHours, 0)
      return resource.totalCapacity - totalTimeOff
    }
    const { result } = renderHook(() => calculateAvailableCapacity(mockResource))
    expect(result.current).toBe(26)
  })

  test('renders job title and position correctly', () => {
    render(
      <ApolloProvider client={apolloClientInit}>
        <ResourceBox {...mockProps} />
      </ApolloProvider>
    )
    expect(screen.getByText('Production Artist (L1)')).toBeInTheDocument()
    expect(screen.getByText('Production Artist')).toBeInTheDocument()
  })

  test('renders location and work code correctly', () => {
    render(
      <ApolloProvider client={apolloClientInit}>
        <ResourceBox {...mockProps} />
      </ApolloProvider>
    )
    expect(screen.getByText('New York')).toBeInTheDocument()
    expect(screen.getByText('L01035')).toBeInTheDocument()
  })

  test('renders agency code and name with correct formatting', () => {
    render(
      <ApolloProvider client={apolloClientInit}>
        <ResourceBox {...mockProps} />
      </ApolloProvider>
    )
    expect(screen.getByText('001 : Agency Name')).toBeInTheDocument()
  })

  test('renders empty state when resource has no timeOffDetails', () => {
    const mockPropsWithoutTimeOff = {
      ...mockProps,
      resource: {
        ...mockProps.resource,
        timeOffDetails: []
      }
    }
    render(
      <ApolloProvider client={apolloClientInit}>
        <ResourceBox {...mockPropsWithoutTimeOff} />
      </ApolloProvider>
    )
    expect(screen.getByText('Aaron Fisher')).toBeInTheDocument()
    expect(screen.queryByText('0')).not.toBeInTheDocument()
  })
})
