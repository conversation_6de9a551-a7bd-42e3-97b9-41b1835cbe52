import { type IconProps } from '../Icon.props'

const AssignmentNotesIcon = ({ height, width, isActive }: IconProps) => {
  return (
    <svg width={width} height={height} viewBox="0 0 20 22" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M3.30731 21.1498C2.62343 21.1498 2.04284 20.9111 1.56556 20.4338C1.08826 19.9565 0.849609 19.3759 0.849609 18.6921V5.30751C0.849609 4.62362 1.08826 4.04304 1.56556 3.56575C2.04284 3.08845 2.62343 2.8498 3.30731 2.8498H7.00731C7.28679 2.28827 7.68487 1.80622 8.20153 1.40366C8.71818 1.00109 9.31753 0.799805 9.99958 0.799805C10.6816 0.799805 11.281 1.00109 11.7976 1.40366C12.3143 1.80622 12.7124 2.28827 12.9919 2.8498H16.6919C17.3757 2.8498 17.9563 3.08845 18.4336 3.56575C18.9109 4.04304 19.1496 4.62362 19.1496 5.30751V18.6921C19.1496 19.3759 18.9109 19.9565 18.4336 20.4338C17.9563 20.9111 17.3757 21.1498 16.6919 21.1498H3.30731ZM9.99958 4.17093C10.1996 4.17093 10.3704 4.1001 10.5121 3.95843C10.6538 3.81676 10.7246 3.64593 10.7246 3.44593C10.7246 3.24593 10.6538 3.0751 10.5121 2.93343C10.3704 2.79176 10.1996 2.72093 9.99958 2.72093C9.79958 2.72093 9.62875 2.79176 9.48708 2.93343C9.34542 3.0751 9.27458 3.24593 9.27458 3.44593C9.27458 3.64593 9.34542 3.81676 9.48708 3.95843C9.62875 4.1001 9.79958 4.17093 9.99958 4.17093ZM2.99958 17.7921C3.93292 16.8921 4.98708 16.1838 6.16208 15.6671C7.33708 15.1504 8.61625 14.8921 9.99958 14.8921C11.3829 14.8921 12.6621 15.1504 13.8371 15.6671C15.0121 16.1838 16.0663 16.8921 16.9996 17.7921V5.30751C16.9996 5.23057 16.9675 5.16005 16.9034 5.09593C16.8393 5.03183 16.7688 4.99978 16.6919 4.99978H3.30731C3.23038 4.99978 3.15985 5.03183 3.09573 5.09593C3.03163 5.16005 2.99958 5.23057 2.99958 5.30751V17.7921ZM9.99958 13.7883C11.0126 13.7883 11.8742 13.4332 12.5844 12.723C13.2945 12.0129 13.6496 11.1513 13.6496 10.1383C13.6496 9.12522 13.2945 8.26364 12.5844 7.5535C11.8742 6.84336 11.0126 6.48828 9.99958 6.48828C8.98655 6.48828 8.12496 6.84336 7.41481 7.5535C6.70468 8.26364 6.34961 9.12522 6.34961 10.1383C6.34961 11.1513 6.70468 12.0129 7.41481 12.723C8.12496 13.4332 8.98655 13.7883 9.99958 13.7883ZM5.19186 18.9998H14.8073V18.7883C14.1176 18.2164 13.3679 17.7825 12.5583 17.4863C11.7486 17.1901 10.8958 17.0421 9.99958 17.0421C9.11625 17.0421 8.26817 17.1885 7.45533 17.4815C6.6425 17.7745 5.88801 18.2036 5.19186 18.769V18.9998ZM9.99663 11.6383C9.5845 11.6383 9.23195 11.4908 8.93898 11.1959C8.64603 10.901 8.49956 10.5475 8.49956 10.1353C8.49956 9.72318 8.64702 9.37063 8.94193 9.07768C9.23685 8.78473 9.59038 8.63826 10.0025 8.63826C10.4147 8.63826 10.7672 8.7857 11.0602 9.0806C11.3531 9.37552 11.4996 9.72906 11.4996 10.1412C11.4996 10.5534 11.3522 10.9059 11.0572 11.1989C10.7623 11.4918 10.4088 11.6383 9.99663 11.6383Z"
        fill={isActive ? '#202020' : '#808080'}
      />
    </svg>
  )
}

export default AssignmentNotesIcon
