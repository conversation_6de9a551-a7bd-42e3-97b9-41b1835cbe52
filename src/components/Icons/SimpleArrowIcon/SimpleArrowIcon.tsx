import { type IconProps } from '../Icon.props'

const SimpleArrowIcon = ({ height, width, rotation }: IconProps) => {
  return (
    <svg
      style={{ transform: `rotate(${rotation}deg)` }}
      width={height}
      height={width}
      viewBox="0 0 18 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="arrow-up-alt">
        <path
          id="Vector"
          d="M8.99799 2.84556C8.90318 2.84556 8.8061 2.86637 8.70675 2.90798C8.60741 2.94958 8.52774 3.00731 8.46774 3.08117L3.37916 8.16975C3.22046 8.30638 3.14111 8.48455 3.14111 8.70426C3.14111 8.92397 3.22046 9.11048 3.37916 9.26378C3.52535 9.4154 3.7074 9.49122 3.92531 9.49122C4.14323 9.49122 4.32857 9.4154 4.48134 9.26378L8.22969 5.52602V14.1249C8.22969 14.341 8.30514 14.5248 8.45603 14.6762C8.60691 14.8277 8.78925 14.9034 9.00304 14.9034C9.21683 14.9034 9.4013 14.83 9.55646 14.6833C9.71163 14.5365 9.78921 14.3531 9.78921 14.1331V5.52602L13.5376 9.26378C13.6767 9.4154 13.8552 9.49122 14.0731 9.49122C14.291 9.49122 14.4835 9.41639 14.6504 9.26674C14.7895 9.11542 14.859 8.92954 14.859 8.7091C14.859 8.48866 14.7895 8.30888 14.6504 8.16975L9.55116 3.08117C9.47408 3.00731 9.38711 2.94958 9.29025 2.90798C9.19338 2.86637 9.09596 2.84556 8.99799 2.84556Z"
          fill="#202020"
        />
      </g>
    </svg>
  )
}

export default SimpleArrowIcon
