import { gql } from '@apollo/client'

const AUDIT_TRAIL_QUERY = gql`
  query GetAuditTrailForLoggedInUser($userLoggedInExternalId: String) {
    getAuditTrailForLoggedInUser(userLoggedInExternalId: $userLoggedInExternalId) {
      action
      date
      id
      time
      details {
        newAssignment {
          externalId
          hoursPerDay
          startDate
          dueDate
          assignmentStatus
          resourceName
          resourceId
          taskName
          projectName
          isPlaceholder
        }
        oldAssignment {
          externalId
          hoursPerDay
          startDate
          dueDate
          assignmentStatus
          resourceName
          resourceId
          taskName
          projectName
          isPlaceholder
        }
      }
    }
  }
`

export default AUDIT_TRAIL_QUERY
