import { gql } from '@apollo/client'

const TASK_BY_PROJECT_QUERY = gql`
  query GetTasksByProjectId($projectId: String) {
    getTasksByProjectId(projectId: $projectId) {
      id
      taskId
      label: taskName
      taskName
      taskStatus
      taskStartDate
      taskEndDate
    }
  }
`

const TASKS_BY_PM_GROUPED_BY_PROJECT_QUERY = gql`
  query GetPMTasksByGroupOfProjects($projectIds: [String], $workCode: String) {
    getPMTasksByGroupOfProjects(projectIds: $projectIds, workCode: $workCode) {
      id
      name
      tasks {
        taskId
        taskName
      }
    }
  }
`

const TASKS_BY_RM_GROUPED_BY_PROJECT_QUERY = gql`
  query GetRMTasksByGroupOfProjects($projectIds: [String], $workCode: String) {
    getRMTasksByGroupOfProjects(projectIds: $projectIds, workCode: $workCode) {
      id
      name
      tasks {
        taskId
        taskName
      }
    }
  }
`

export { TASK_BY_PROJECT_QUERY, TASKS_BY_PM_GROUPED_BY_PROJECT_QUERY, TASKS_BY_RM_GROUPED_BY_PROJECT_QUERY }
