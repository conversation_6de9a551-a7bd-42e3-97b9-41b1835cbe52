import { Box, IconButton, LinearProgress, palette, Typography } from 'lion-ui'
import { type TimesheetsInfoCtaProps } from './TimesheetsInfoCta.props'
import SimpleArrowIcon from '@components/Icons/SimpleArrowIcon/SimpleArrowIcon'
import TSMinusIcon from '@components/Icons/timesheetIcons/TSMinusIcon'

const TimesheetsInfoCta = ({ timesheetDataExists, openTimesheetsView, isLoading }: TimesheetsInfoCtaProps) => {
  const handleOpenTimesheetsView = () => {
    openTimesheetsView(true)
  }

  return (
    <Box
      sx={{
        border: 1,
        borderColor: palette.secondary.main,
        width: '100%',
        borderRadius: '0.5rem',
        padding: isLoading ? '1rem 0 0 0' : 5,
        backgroundColor: palette.grey['50'],
        margin: '1rem 0'
      }}
    >
      {isLoading && (
        <>
          <Typography sx={{ margin: '0 0 1rem 1rem' }} fontSize={'1rem'}>
            Loading timesheet data...
          </Typography>
          <LinearProgress />
        </>
      )}

      {!timesheetDataExists && !isLoading && <Box>There is no timesheet data to display</Box>}

      {timesheetDataExists && (
        <>
          <Box
            sx={{
              display: 'flex',
              flexWrap: 'nowrap',
              gap: 2,
              alignItems: 'center',
              justifyContent: 'space-between'
            }}
          >
            <Box sx={{ display: 'flex', flexWrap: 'nowrap', gap: 2, alignItems: 'center' }}>
              <TSMinusIcon width="32" />
              <Typography fontWeight={600} fontSize={'1rem'}>
                Latest timesheet history
              </Typography>
            </Box>

            <IconButton onClick={handleOpenTimesheetsView}>
              <SimpleArrowIcon width="24" height="24" rotation="90" />
            </IconButton>
          </Box>
        </>
      )}
    </Box>
  )
}

export default TimesheetsInfoCta
