import { render } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import SplitAssignment from '@components/Forms/SplitAssignmentForm/SplitAssignment'
import { useReactiveVar } from '@apollo/client'

// Mock the useReactiveVar hook
jest.mock('@apollo/client', () => {
  const actualApolloClient = jest.requireActual('@apollo/client')
  return {
    ...actualApolloClient,
    useReactiveVar: jest.fn()
  }
})

describe('SplitAssignment Component', () => {
  const mockAssignment = {
    assignmentState: 'Approved',
    taskState: 'Completed',
    hoursPerDay: 8
  }

  beforeEach(() => {
    ;(useReactiveVar as jest.Mock).mockReturnValue(mockAssignment)
  })

  it('should render without crashing', () => {
    const { container } = render(<SplitAssignment tileLeft={0} />)
    expect(container).toBeInTheDocument()
  })

  // Mock the useReactiveVar hook
  jest.mock('@apollo/client', () => {
    const actualApolloClient = jest.requireActual('@apollo/client')
    return {
      ...actualApolloClient,
      useReactiveVar: jest.fn()
    }
  })

  describe('SplitAssignment Component', () => {
    const mockAssignment = {
      assignmentState: 'Approved',
      taskState: 'Completed',
      hoursPerDay: 8
    }

    beforeEach(() => {
      ;(useReactiveVar as jest.Mock).mockReturnValue(mockAssignment)
    })

    it('should render without crashing', () => {
      const { container } = render(<SplitAssignment tileLeft={0} />)
      expect(container).toBeInTheDocument()
    })

    it('should display the correct hours per day', () => {
      const { getByText } = render(<SplitAssignment tileLeft={100} />)
      expect(getByText('8h')).toBeInTheDocument()
    })

    it('should handle missing tileWidth prop', () => {
      const { getByTestId } = render(<SplitAssignment tileLeft={100} />)
      const box = getByTestId('split-assignment-box')
      expect(box).toHaveStyle('width: 100%')
    })

    it('should handle provided tileWidth prop', () => {
      const { getByTestId } = render(<SplitAssignment tileLeft={100} tileWidth={200} />)
      const box = getByTestId('split-assignment-box')
      expect(box).toHaveStyle('width: 200px')
    })

    it('should position the box correctly based on tileLeft prop', () => {
      const { getByTestId } = render(<SplitAssignment tileLeft={150} />)
      const box = getByTestId('split-assignment-box')
      expect(box).toHaveStyle('left: 150px')
    })

    it('should apply the correct styles based on tileStatus', () => {
      const { getByTestId } = render(<SplitAssignment tileLeft={100} />)
      const box = getByTestId('split-assignment-box')
      expect(box).toHaveStyle('background-color: #someColor') // Replace with actual color based on tileStatus
    })
  })
})
