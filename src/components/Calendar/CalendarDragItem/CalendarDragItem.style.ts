// Lion ui imports
import { palette } from 'lion-ui'

// Context import
import type { ApprovalStatusBorder, TileStatus, TileStyle } from '@typeDefs/Tile'

// Constants imports
import { AssignmentStatus } from '@constants/taskStatus'

const commonBorderRadius = '8px'
export const commonDashedBorder = (color: string): string => `1px dashed ${color}`
export const commonBeforeStyle = (color: string): ApprovalStatusBorder => ({
  content: '""',
  position: 'absolute',
  left: 0,
  top: 0,
  height: '100%',
  width: '4px',
  backgroundColor: color,
  borderRadius: commonBorderRadius
})

/**
 * Determines the tile status based on the assignment state and task state.
 *
 * The function first checks if the provided assignment state is one of the available assignment statuses.
 * If it is, the function returns the assignment state. If not, it returns the task state.
 *
 * @param {string} assignmentState - The state of the assignment. Expected values are 'requested', 'rejected', 'approved', 'cancelled'.
 * @param {string} taskState - The state of the task.
 * @returns {string} - The determined tile status, which is either the assignment state or the task state.
 */
export const getTileStatus = (assignmentState: string, taskState: string, resourceType: string): string => {
  // Take status colors from: Assignment or Task
  // Look into possible status in the correct order to determine the tile status.
  const normalizedAssignmentState = assignmentState.toLowerCase() as AssignmentStatus
  if (resourceType === 'placeholder') {
    return taskState
  }
  if (Object.values(AssignmentStatus).includes(normalizedAssignmentState)) {
    return assignmentState
  }
  // if assignment status wasn't found, return task status.
  return taskState
}

export const getAssignmentStatus = (assignmentState: string, taskState: string, resourceType: string): string => {
  // Take status colors from: Assignment or Task
  // Look into possible status in the correct order to determine the tile status.
  const normalizedAssignmentState = assignmentState.toLowerCase() as AssignmentStatus
  if (resourceType === 'placeholder') {
    return taskState
  }
  if (Object.values(AssignmentStatus).includes(normalizedAssignmentState) || taskState.toLowerCase() === 'external') {
    console.log('Assignment state found:', normalizedAssignmentState)
    return assignmentState
  }
  // if assignment status wasn't found, return task status.
  return taskState
}

/**
 * Generates the style for a calendar tiles item based on its status.
 *
 * @param status - The status of the tile.
 * @returns An object representing the style of the calendar tile.
 *
 * @remarks
 * The color codes defined here are coming from `lion-ui` palette. Some colors that do not come from the palette are defined in this specific project.
 * The status of the tile can be determined using the `getTileStatus` function.
 * The style object returned by this function can be used to style other components/elements based on their status and override unnecessary styles.
 */
const getCalendarItemStyle = (status: TileStatus): TileStyle => {
  const resizableBorderRadius = {
    borderRadius: commonBorderRadius, // eslint-disable-next-line @typescript-eslint/naming-convention
    '.ui-resizable-sw': {
      opacity: 0,
      bottom: '0 !important',
      left: '0 !important'
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    '.ui-resizable-se': {
      opacity: 0,
      bottom: '0 !important',
      right: '0 !important'
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    '.ui-resizable-e': {
      right: '0 !important'
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    '.ui-resizable-w': {
      left: '0 !important'
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    '.ui-resizable-s': {
      bottom: '0 !important'
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    '.ui-resizable-handle': {
      display: 'unset !important'
    }
  }

  const styles: Record<TileStatus, TileStyle> = {
    active: {
      backgroundColor: '#ADD5CB',
      color: palette['Success-Dark'].dark,
      cursor: 'pointer',
      ...resizableBorderRadius
    },
    inprogress: {
      backgroundColor: '#ADD5CB',
      color: palette['Success-Dark'].dark,
      cursor: 'pointer',
      ...resizableBorderRadius
    },
    approved: {
      backgroundColor: '#E7F3F0',
      color: palette['Success-Dark'].dark,
      border: commonDashedBorder(palette['700-Accent-4'].main),
      // eslint-disable-next-line @typescript-eslint/naming-convention
      '&::before': commonBeforeStyle(palette['500-Accent-4'].main),
      cursor: 'pointer',
      ...resizableBorderRadius
    },
    cancelled: {
      backgroundColor: palette['Error-Light'].main,
      color: palette['Error-Dark'].main,
      cursor: 'pointer',
      ...resizableBorderRadius
    },
    requested: {
      backgroundColor: palette['200-Accent-4'].main,
      color: palette['Warning-Dark'].main,
      backgroundImage: 'repeating-linear-gradient(125deg, #bdab7833, #bdab7833 1px, transparent 1px, transparent 5px)',
      border: commonDashedBorder(palette['Warning-Dark'].main),
      cursor: 'pointer',
      ...resizableBorderRadius
    },
    rejected: {
      backgroundColor: palette['50-Accent-3'].main,
      color: '#800942',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      '&::before': commonBeforeStyle(palette['500-Accent-4'].main),
      cursor: 'pointer',
      ...resizableBorderRadius
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    onhold: {
      backgroundColor: palette.grey['700'],
      color: palette.common.white,
      cursor: 'pointer',
      ...resizableBorderRadius
    },
    completed: {
      backgroundColor: palette['Success-Dark'].main,
      color: palette.common.white,
      cursor: 'pointer',
      ...resizableBorderRadius
    },
    complete: {
      backgroundColor: palette['Success-Dark'].main,
      color: palette.common.white,
      cursor: 'pointer',
      ...resizableBorderRadius
    },
    new: {
      backgroundColor: palette.grey['100'],
      color: palette.grey['700'],
      border: commonDashedBorder(palette.grey['400']),
      cursor: 'pointer',
      ...resizableBorderRadius
    },
    draft: {
      backgroundColor: palette.grey['100'],
      color: palette.grey['700'],
      border: commonDashedBorder(palette.grey['400']),
      cursor: 'pointer',
      ...resizableBorderRadius
    },
    proposed: {
      backgroundColor: palette['Stroke-Grey'].main,
      color: palette.grey['700'],
      cursor: 'pointer',
      ...resizableBorderRadius
    },
    filtered: {
      backgroundColor: palette.grey['200'],
      color: palette.grey['300'],
      cursor: 'not-allowed',
      border: 'unset',
      ...resizableBorderRadius
    },
    external: {
      backgroundColor: palette.grey['100'],
      color: palette.grey['300'],
      cursor: 'not-allowed',
      border: `1px solid ${palette.grey['200']}`,
      ...resizableBorderRadius
    },
    tentative: {
      backgroundColor: palette['800-Accent-1'].main,
      color: '#370275',
      border: commonDashedBorder('#370275'),
      backgroundImage: `repeating-linear-gradient(125deg, ${palette['100-Accent-2'].main}, ${palette['100-Accent-2'].main} 1px, transparent 1px, transparent 5px)`,
      cursor: 'pointer',
      ...resizableBorderRadius
    },
    default: {
      backgroundColor: 'white',
      cursor: 'pointer',
      ...resizableBorderRadius
    }
  }

  return styles[status] || styles.default
}
export default getCalendarItemStyle
