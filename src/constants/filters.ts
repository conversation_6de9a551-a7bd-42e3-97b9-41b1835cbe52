// Icons
import profileIcon from '@assets/icons/profile.svg'
import groupIcon from '@assets/icons/people.svg'
import projectsIcon from '@assets/icons/project.svg'
import tasksIcon from '@assets/icons/tasks.svg'
import brandsIcon from '@assets/icons/license.svg'
import placeholdersIcon from '@assets/icons/placeholdersIcon.svg'
import visibilityIcon from '@assets/icons/visibility.svg'

export enum Filters {
  RESOURCES = 'resources',
  GROUPS = 'usersGroups',
  PROJECTS = 'projects',
  TASKS = 'tasks',
  PLACEHOLDERS = 'placeholders',
  BRANDS = 'brands',
  TASK_STATES = 'taskStates',
  EXPAND_PLACEHOLDERS = 'expandPlaceholders'
}

export const availableFilters = [
  { label: 'Resources', id: Filters.RESOURCES, icon: profileIcon },
  { label: 'Placeholders', id: Filters.PLACEHOLDERS, icon: placeholdersIcon },
  { label: 'My Teams', id: Filters.GROUPS, icon: groupIcon },
  { label: 'Projects', id: Filters.PROJECTS, icon: projectsIcon },
  { label: 'Tasks', id: Filters.TASKS, icon: tasksIcon },
  { label: 'Task Status', id: Filters.TASK_STATES, icon: tasksIcon },
  { label: 'Brands', id: Filters.BRANDS, icon: brandsIcon },
  { label: 'Expand Placeholders', id: Filters.EXPAND_PLACEHOLDERS, icon: visibilityIcon }
]
