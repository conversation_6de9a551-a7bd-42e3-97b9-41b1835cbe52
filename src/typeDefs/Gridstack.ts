import { type Assignment } from '@typeDefs/Assignments'
import { type Resource } from '@typeDefs/Resource'

export interface GridStackEvent {
  target: {
    gridstackNode: {
      grid: {
        el: HTMLElement
      }
    }
  }
}

export interface GridStackNode {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  dataset: any
  x: number
  y: number
  w: number
  h: number
  el: HTMLElement
  id: string
}

export interface ItemDropped {
  x: number
  y: number
  w: number
  h: number
  el: {
    dataset: {
      gsId: string
      index?: string
    }
    offsetParent?: HTMLElement
  }
}

export interface DragRowType {
  id: string
  assignments: Assignment[]
  resource: Resource
  type: string
}
