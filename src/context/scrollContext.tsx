import { createContext, useState, useContext, useMemo } from 'react'
import type { ReactNode } from 'react'
import type React from 'react'

interface ScrollContextType {
  isScrolling: boolean
  setIsScrolling: React.Dispatch<React.SetStateAction<boolean>>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  itemInHold: any
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setItemInHold: React.Dispatch<React.SetStateAction<any>>
  dragging: boolean
  setDragging: React.Dispatch<React.SetStateAction<boolean>>
}

export const ScrollContext = createContext<ScrollContextType | undefined>(undefined)

interface ScrollProviderProps {
  children: ReactNode
}

export const ScrollProvider: React.FC<ScrollProviderProps> = ({ children }) => {
  const [isScrolling, setIsScrolling] = useState<boolean>(false)
  const [itemInHold, setItemInHold] = useState(null)
  const [dragging, setDragging] = useState(false)

  const value = useMemo(
    () => ({
      isScrolling,
      setIsScrolling,
      itemInHold,
      setItemInHold,
      dragging,
      setDragging
    }),
    [isScrolling, itemInHold, dragging]
  )

  return <ScrollContext.Provider value={value}>{children}</ScrollContext.Provider>
}

export const useScroll = (): ScrollContextType => {
  const context = useContext(ScrollContext)
  if (context === undefined) {
    throw new Error('useScroll must be used within a ScrollProvider')
  }
  return context
}
