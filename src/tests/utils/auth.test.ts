import { initializeEmployeeCodeFromUrl, addEmployeeCode } from '@utils/auth'
import { employeeCodeVar, userInfoVar } from '@gql/client/localState'

// Mock the reactive variables
jest.mock('@gql/client/localState', () => ({
  employeeCodeVar: jest.fn(),
  userInfoVar: jest.fn(),
  businessRolesVar: jest.fn(),
  businessRoleVar: jest.fn(),
  usernameVar: jest.fn(),
  wflionLoginIDVar: jest.fn()
}))

describe('Auth Utils', () => {
  const mockEmployeeCodeVar = employeeCodeVar as jest.MockedFunction<typeof employeeCodeVar>
  const mockUserInfoVar = userInfoVar as jest.MockedFunction<typeof userInfoVar>

  beforeEach(() => {
    jest.clearAllMocks()
    // Mock window.location
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    delete (window as any).location
    window.location = {
      href: 'http://localhost:3000'
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } as any
  })

  describe('initializeEmployeeCodeFromUrl', () => {
    it('should extract employeeCode from URL query parameters and hijack activeUser.altairNumber', () => {
      window.location.href = 'http://localhost:3000?employeeCode=EMP123'
      const mockUser = {
        altairNumber: 'ORIGINAL123',
        name: 'Test User',
        id: '1',
        location: 'Test Location',
        position: 'Test Position',
        jobTitle: 'Test Job',
        businessRole: 'Test Role',
        businessRoleId: '1',
        workCode: 'TEST123',
        resourceManagerId: '1',
        resourceManagerName: 'Test Manager'
      }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-argument
      mockUserInfoVar.mockReturnValue(mockUser as any)

      initializeEmployeeCodeFromUrl()

      expect(mockEmployeeCodeVar).toHaveBeenCalledWith('EMP123')
      expect(mockUserInfoVar).toHaveBeenCalledWith({
        ...mockUser,
        altairNumber: 'EMP123'
      })
    })

    it('should extract employeeCode from custom URL', () => {
      const customUrl = 'http://example.com/app?employeeCode=EMP456&other=value'

      initializeEmployeeCodeFromUrl(customUrl)

      expect(mockEmployeeCodeVar).toHaveBeenCalledWith('EMP456')
    })

    it('should not call employeeCodeVar or hijack user if no employeeCode in URL', () => {
      window.location.href = 'http://localhost:3000?other=value'

      initializeEmployeeCodeFromUrl()

      expect(mockEmployeeCodeVar).not.toHaveBeenCalled()
      expect(mockUserInfoVar).not.toHaveBeenCalled()
    })

    it('should handle invalid URLs gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()

      initializeEmployeeCodeFromUrl('invalid-url')

      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to parse URL for employeeCode:',
        expect.objectContaining({
          name: 'TypeError',
          message: expect.stringContaining('Invalid URL')
        })
      )
      expect(mockEmployeeCodeVar).not.toHaveBeenCalled()

      consoleSpy.mockRestore()
    })

    it('should return early if window is undefined (SSR)', () => {
      const originalWindow = global.window
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      delete (global as any).window

      initializeEmployeeCodeFromUrl()

      expect(mockEmployeeCodeVar).not.toHaveBeenCalled()

      global.window = originalWindow
    })
  })

  describe('addEmployeeCode', () => {
    it('should set employeeCode using reactive variable', () => {
      addEmployeeCode({ employeeCode: 'TEST123' })

      expect(mockEmployeeCodeVar).toHaveBeenCalledWith('TEST123')
    })
  })

  describe('URL employeeCode integration', () => {
    it('should prioritize employeeCode from URL over userLLid for SINGLE_RESOURCE_QUERY', () => {
      // This test verifies the logic: employeeCode || userLLid
      const employeeCodeFromUrl = 'EMP123'
      const userLLidFromToken = 'USER456'

      // When employeeCode is present, it should be used
      expect(employeeCodeFromUrl || userLLidFromToken).toBe('EMP123')

      // When employeeCode is empty, userLLid should be used
      const emptyEmployeeCode = ''
      expect(emptyEmployeeCode || userLLidFromToken).toBe('USER456')
    })
  })
})
