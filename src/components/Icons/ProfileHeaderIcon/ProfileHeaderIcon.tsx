import { type IconProps } from '../Icon.props'

const ProfileHeaderIcon = ({ height, width }: IconProps) => {
  return (
    <svg width={width} height={height} viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="20" cy="20" r="19" fill="#DBEAF9" stroke="white" strokeWidth="2" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20 22C16.6863 22 14 24.6863 14 28C14 28.5523 13.5523 29 13 29C12.4477 29 12 28.5523 12 28C12 23.5817 15.5817 20 20 20C24.4183 20 28 23.5817 28 28C28 28.5523 27.5523 29 27 29C26.4477 29 26 28.5523 26 28C26 24.6863 23.3137 22 20 22Z"
        fill="#1077D6"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20 13C18.8953 13 18 13.8953 18 15C18 16.1047 18.8953 17 20 17C21.1047 17 22 16.1047 22 15C22 13.8953 21.1047 13 20 13ZM16 15C16 12.7907 17.7907 11 20 11C22.2093 11 24 12.7907 24 15C24 17.2093 22.2093 19 20 19C17.7907 19 16 17.2093 16 15Z"
        fill="#1077D6"
      />
    </svg>
  )
}

export default ProfileHeaderIcon
