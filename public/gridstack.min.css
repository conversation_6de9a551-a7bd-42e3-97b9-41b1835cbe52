.grid-stack {
  position: relative;
  min-height: 110px;
}
.grid-stack-rtl {
  direction: ltr;
}
.grid-stack-rtl > .grid-stack-item {
  direction: rtl;
}
.grid-stack-placeholder > .placeholder-content {
  background-color: rgba(0, 0, 0, 0.1);
  margin: 0;
  position: absolute;
  width: auto;
  z-index: 0 !important;
}
.grid-stack > .grid-stack-item {
  position: absolute;
  padding: 0;
}
.grid-stack > .grid-stack-item > .grid-stack-item-content {
  margin: 0;
  position: absolute;
  width: auto;
  overflow-x: hidden;
  overflow-y: hidden;
  min-height: 20px;
}
.grid-stack > .grid-stack-item.size-to-content:not(.size-to-content-max) > .grid-stack-item-content {
  overflow-y: hidden;
}
.grid-stack-item > .ui-resizable-handle {
  position: absolute;
  font-size: 0.1px;
  display: block;
  -ms-touch-action: none;
  touch-action: none;
}
/* .grid-stack-item.ui-resizable-autohide > .ui-resizable-handle,
.grid-stack-item.ui-resizable-disabled > .ui-resizable-handle {
   display: none; 
} */
.grid-stack-item > .ui-resizable-ne,
.grid-stack-item > .ui-resizable-nw,
.grid-stack-item > .ui-resizable-se,
.grid-stack-item > .ui-resizable-sw {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" stroke="%23666" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 20 20"><path d="m10 3 2 2H8l2-2v14l-2-2h4l-2 2"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0;
}
.grid-stack-item > .ui-resizable-ne {
  transform: translate(0, 10px) rotate(45deg);
}
.grid-stack-item > .ui-resizable-sw {
  transform: rotate(45deg);
}
.grid-stack-item > .ui-resizable-nw {
  transform: translate(0, 10px) rotate(-45deg);
}
.grid-stack-item > .ui-resizable-se {
  transform: rotate(-45deg);
}
.grid-stack-item > .ui-resizable-nw {
  cursor: nw-resize;
  width: 20px;
  height: 20px;
  top: 0;
}
.grid-stack-item > .ui-resizable-n {
  cursor: n-resize;
  height: 10px;
  top: 0;
  left: 25px;
  right: 25px;
}
.grid-stack-item > .ui-resizable-ne {
  cursor: ne-resize;
  width: 20px;
  height: 20px;
  top: 0;
}
.grid-stack-item > .ui-resizable-e {
  cursor: e-resize;
  width: 10px;
  top: 15px;
  bottom: 15px;
}
.grid-stack-item > .ui-resizable-se {
  cursor: se-resize;
  width: 20px;
  height: 20px;
  bottom: 10px !important;
}
.grid-stack-item > .ui-resizable-s {
  cursor: s-resize;
  height: 10px;
  left: 25px;
  right: 25px;
}

.displaceBottomHandler > .ui-resizable-s {
  bottom: 0 !important;
}

.grid-stack-item > .ui-resizable-sw {
  cursor: sw-resize;
  width: 20px;
  height: 20px;
  bottom: 10px !important;
}
.grid-stack-item > .ui-resizable-w {
  cursor: w-resize;
  width: 10px;
  top: 15px;
  bottom: 15px;
}
.grid-stack-item.ui-draggable-dragging > .ui-resizable-handle {
  display: none !important;
}
.grid-stack-item.ui-draggable-dragging {
  will-change: left, top;
  cursor: move;
}
.grid-stack-item.ui-resizable-resizing {
  will-change: width, height;
}
.ui-draggable-dragging,
.ui-resizable-resizing {
  z-index: 10000;
}
.ui-draggable-dragging > .grid-stack-item-content,
.ui-resizable-resizing > .grid-stack-item-content {
  box-shadow: 1px 4px 6px rgba(0, 0, 0, 0.2);
  opacity: 0.8;
}
.grid-stack-animate,
.grid-stack-animate .grid-stack-item {
  transition:
    left 0.3s,
    top 0.3s,
    height 0.3s,
    width 0.3s;
}
.grid-stack-animate .grid-stack-item.grid-stack-placeholder,
.grid-stack-animate .grid-stack-item.ui-draggable-dragging,
.grid-stack-animate .grid-stack-item.ui-resizable-resizing {
  transition:
    left 0s,
    top 0s,
    height 0s,
    width 0s;
}
.grid-stack > .grid-stack-item[gs-y='0'] {
  top: 0;
}
.grid-stack > .grid-stack-item[gs-x='0'] {
  left: 0;
}
.gs-12 > .grid-stack-item {
  width: 8.333%;
}
.gs-12 > .grid-stack-item[gs-x='1'] {
  left: 8.333%;
}
.gs-12 > .grid-stack-item[gs-w='2'] {
  width: 16.667%;
}
.gs-12 > .grid-stack-item[gs-x='2'] {
  left: 16.667%;
}
.gs-12 > .grid-stack-item[gs-w='3'] {
  width: 25%;
}
.gs-12 > .grid-stack-item[gs-x='3'] {
  left: 25%;
}
.gs-12 > .grid-stack-item[gs-w='4'] {
  width: 33.333%;
}
.gs-12 > .grid-stack-item[gs-x='4'] {
  left: 33.333%;
}
.gs-12 > .grid-stack-item[gs-w='5'] {
  width: 41.667%;
}
.gs-12 > .grid-stack-item[gs-x='5'] {
  left: 41.667%;
}
.gs-12 > .grid-stack-item[gs-w='6'] {
  width: 50%;
}
.gs-12 > .grid-stack-item[gs-x='6'] {
  left: 50%;
}
.gs-12 > .grid-stack-item[gs-w='7'] {
  width: 58.333%;
}
.gs-12 > .grid-stack-item[gs-x='7'] {
  left: 58.333%;
}
.gs-12 > .grid-stack-item[gs-w='8'] {
  width: 66.667%;
}
.gs-12 > .grid-stack-item[gs-x='8'] {
  left: 66.667%;
}
.gs-12 > .grid-stack-item[gs-w='9'] {
  width: 75%;
}
.gs-12 > .grid-stack-item[gs-x='9'] {
  left: 75%;
}
.gs-12 > .grid-stack-item[gs-w='10'] {
  width: 83.333%;
}
.gs-12 > .grid-stack-item[gs-x='10'] {
  left: 83.333%;
}
.gs-12 > .grid-stack-item[gs-w='11'] {
  width: 91.667%;
}
.gs-12 > .grid-stack-item[gs-x='11'] {
  left: 91.667%;
}
.gs-12 > .grid-stack-item[gs-w='12'] {
  width: 100%;
}
.gs-1 > .grid-stack-item {
  width: 100%;
}

/* Truncate based on tile size */
.grid-stack-item-content {
  container: tileItemContent / size;
}

.customize-on-small-tile {
  container: tile / size;
}

.tile-details {
  container: details / size;
}

.tile-status.tile-status-at-1 {
  opacity: 0;
}

/* Height conditions */
@container tileItemContent (height > 45px) {
  .tile-details {
    flex-direction: column;
    justify-content: space-between;
  }
}
/* Each 15px of height equals 1h */
@container tile (height <= 40px) {
  .is-task-visible {
    display: none;
  }
  .is-cross-agency-visible {
    display: none;
  }
  .is-altair-number-visible {
    display: none;
  }
}
@container tile (height > 40px) {
  .is-task-visible {
    display: none;
  }
  .is-cross-agency-visible {
    display: block;
  }
  .is-altair-number-visible {
    display: none;
  }
}

@container tile (height > 50px) {
  .is-task-visible {
    display: block;
  }
  .is-cross-agency-visible {
    display: block;
  }
  .is-altair-number-visible {
    display: none;
  }
}

@container tile (height > 60px) {
  .is-task-visible {
    display: block;
  }
  .is-cross-agency-visible {
    display: block;
  }
  .is-altair-number-visible {
    display: block;
  }
}

@container details (height <= 40px) {
  .tile-status,
  .tile-status.tile-status-at-2 {
    display: none;
  }
}

@container details (height > 40px) {
  .tile-cross-arrow {
    display: none;
  }
}

@container details (height > 45px) {
  .tile-status.tile-status-at-2 {
    display: block;
    max-width: 4vw;
  }
}

/* Overrides */
.audit-trail-accordion-summary > .MuiAccordionSummary-content {
  margin: 0 !important;
}

.rdrDayToday:not(.rdrDayPassive) .rdrInRange ~ .rdrDayNumber span:after {
  display: none !important;
}

.rdrDayToday:not(.rdrDayPassive) .rdrInRange {
  border-radius: 100% !important;
  border: 1px solid rgb(0, 0, 0) !important;
  top: 0px;
}

.rdrDayToday .rdrDayNumber span:after {
  display: none !important;
}
.rdrDayToday:not(:has(.rdrInRange)) {
  border-radius: 100% !important;
  border: 1px solid rgb(0, 0, 0) !important;
  margin-top: 2px;
  height: 45px !important;
  font-weight: bolder !important;
  background-color: transparent !important;
}

.rdrDayToday:has(.rdrInRange) {
  background-color: rgb(200, 225, 249);
  height: 44px;
  top: 4px;
}

.rdrDayToday .rdrStartEdge,
.rdrDayToday .rdrEndEdge {
  border-radius: 100%;
  top: 1px;
  left: 1px;
  height: 41px;
  width: 42px;
}
.rdrDayToday .rdrEndEdgeLeft {
  top: 1px;
  left: -1px;
}

.rdrDayToday .rdrStartEdgeRight {
  top: 1px;
}
