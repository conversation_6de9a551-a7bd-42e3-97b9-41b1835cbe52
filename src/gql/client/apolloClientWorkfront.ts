import { ApolloClient, InMemoryCache, HttpLink, from } from '@apollo/client'
import type { ServerError, ServerParseError } from '@apollo/client'
import fetch from 'cross-fetch'
import { onError } from '@apollo/client/link/error'
import { typePolicies } from '@gql/client/localState'

/**
 * Apollo Client for Workfront pages without authentication
 * This client is used specifically for Workfront Register and Plugin pages
 * to avoid unnecessary authentication attempts that cause performance issues
 */

const httpLink = new HttpLink({
  uri: `${process.env.NEXT_PUBLIC_BFF_URL}/`,
  fetch
})

const errorLink = onError(({ networkError, graphQLErrors }) => {
  console.log('Workfront Apollo Client - networkError', networkError)
  console.log('Workfront Apollo Client - graphQLErrors', graphQLErrors)

  const isServerError = (networkError as ServerError)?.statusCode === 401
  const isParseError = (networkError as ServerParseError)?.statusCode === 401

  if (networkError != null && (isServerError || isParseError)) {
    console.warn('Workfront Apollo Client - Unauthorized request detected')
  }

  if (graphQLErrors != null && graphQLErrors.some((error) => error.message === '401: Unauthorized')) {
    console.warn('Workfront Apollo Client - GraphQL unauthorized error detected')
  }
})

const link = from([errorLink, httpLink])

const apolloClientWorkfront = new ApolloClient({
  link,
  cache: new InMemoryCache({
    typePolicies
  })
})

export default apolloClientWorkfront
