import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import AuditTrailContent from '@components/AuditTrail/AuditTrailContent/AuditTrailContent'
import { MockedProvider } from '@apollo/client/testing'
import AUDIT_TRAIL_QUERY from '@gql/queries/AuditTrailEntries.query'
import { CLEAR_AUDIT_TRAIL_MUTATION } from '@gql/mutations/auditTrail.mutation'
import mockAuditTrailEntry from '../../__mocks__/auditTrailEntry.mock'

jest.mock('@components/Icons/AuditTrailIcon/AuditTrailIcon', () => {
  const MockAuditTrailIcon = () => <div data-testid="audit-trail-icon" />
  MockAuditTrailIcon.displayName = 'MockAuditTrailIcon'
  return MockAuditTrailIcon
})
jest.mock('@components/Icons/CloseIcon/CloseIcon', () => {
  const MockCloseIcon = () => <div data-testid="close-icon" />
  MockCloseIcon.displayName = 'MockCloseIcon'
  return MockCloseIcon
})
jest.mock('@components/Icons/FilterIllustrationIcon/FilterIllustrationIcon', () => {
  const MockFilterIllustrationIcon = () => <div data-testid="filter-illustration-icon" />
  MockFilterIllustrationIcon.displayName = 'MockFilterIllustrationIcon'
  return MockFilterIllustrationIcon
})

const mocks = [
  {
    request: {
      query: AUDIT_TRAIL_QUERY,
      variables: { userLoggedInExternalId: '123' }
    },
    result: {
      data: {
        getAuditTrailForLoggedInUser: [mockAuditTrailEntry]
      }
    }
  },
  {
    request: {
      query: CLEAR_AUDIT_TRAIL_MUTATION,
      variables: { userLoggedInExternalId: '123' }
    },
    result: {
      data: { clearAuditTrail: true }
    }
  }
]

describe('AuditTrailContent Component', () => {
  const mockOnClose = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('renders drawer elements correctly', () => {
    render(
      <MockedProvider mocks={mocks} addTypename={false}>
        <AuditTrailContent onClose={mockOnClose} />
      </MockedProvider>
    )
    expect(screen.getByText('Recent history')).toBeInTheDocument()
    // Check for the AuditTrailIcon
    expect(screen.getByTestId('audit-trail-icon')).toBeInTheDocument()

    // Check for the title
    expect(screen.getByText('Recent history')).toBeInTheDocument()

    // Check for the CloseIcon
    expect(screen.getAllByTestId('close-icon')[0]).toBeInTheDocument()

    // Check for the table headers
    expect(screen.getByText('Date')).toBeInTheDocument()
    expect(screen.getByText('Time')).toBeInTheDocument()
    expect(screen.getByText('Action')).toBeInTheDocument()
  })

  test('calls onClose when the close button is clicked', () => {
    render(
      <MockedProvider mocks={mocks} addTypename={false}>
        <AuditTrailContent onClose={mockOnClose} />
      </MockedProvider>
    )
    const closeButton = screen.getByLabelText('Close audit trail')
    fireEvent.click(closeButton)

    expect(mockOnClose).toHaveBeenCalledTimes(1)
  })

  test('renders loading state when data is being fetched', () => {
    jest.mock('@apollo/client', () => ({
      ...jest.requireActual('@apollo/client'),
      useQuery: jest.fn(() => ({
        data: null,
        loading: true,
        error: null
      }))
    }))

    render(
      <MockedProvider mocks={mocks} addTypename={false}>
        <AuditTrailContent onClose={mockOnClose} />
      </MockedProvider>
    )

    expect(screen.getAllByTestId('audit-trail-loader')[0]).toBeInTheDocument()
  })

  test('renders empty state when auditTrailDataLoading is false and no data exists', async () => {
    const emptyMocks = [
      {
        request: {
          query: AUDIT_TRAIL_QUERY,
          variables: { userLoggedInExternalId: '123' }
        },
        result: {
          data: {
            getAuditTrailForLoggedInUser: []
          }
        }
      }
    ]

    render(
      <MockedProvider mocks={emptyMocks} addTypename={false}>
        <AuditTrailContent onClose={mockOnClose} />
      </MockedProvider>
    )

    expect(await screen.findByText('No recent activity recorded yet.')).toBeInTheDocument()
    expect(screen.getByTestId('filter-illustration-icon')).toBeInTheDocument()
  })

  test('calls mutation to clear audit trail when clear button is clicked', async () => {
    render(
      <MockedProvider mocks={mocks} addTypename={false}>
        <AuditTrailContent onClose={mockOnClose} />
      </MockedProvider>
    )

    const clearButton = screen.getByLabelText('Clear audit trail')
    fireEvent.click(clearButton)

    // Wait for the mutation to complete
    await new Promise((resolve) => setTimeout(resolve, 0))

    expect(screen.getByText('Recent history')).toBeInTheDocument()
  })

  test('renders loading state when auditTrailDataLoading is true', () => {
    render(
      <MockedProvider mocks={[]} addTypename={false}>
        <AuditTrailContent onClose={mockOnClose} />
      </MockedProvider>
    )

    expect(screen.getAllByTestId('audit-trail-loader')[0]).toBeInTheDocument()
  })
})
