import { Box } from 'lion-ui'
import { type CustomTabPanelProps } from './FormTabs.props'

// eslint-disable-next-line @typescript-eslint/naming-convention
function CustomTabPanel(props: CustomTabPanelProps) {
  const { children, value, index, ...other } = props

  return (
    <Box
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
      sx={{ width: '100%' }}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </Box>
  )
}

export default CustomTabPanel
