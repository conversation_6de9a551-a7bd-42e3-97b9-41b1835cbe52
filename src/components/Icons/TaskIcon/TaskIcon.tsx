import { type IconProps } from '../Icon.props'

const TaskIcon = ({ height, width, rotation, fillColor }: IconProps) => {
  return (
    <svg
      style={{ transform: `rotate(${rotation}deg)` }}
      width={height}
      height={width}
      viewBox="0 0 16 17"
      fill={fillColor}
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="task">
        <path
          d="M2.45655 16.0895C2.01971 16.0895 1.65058 15.9363 1.34917 15.6299C1.04775 15.3236 0.897034 14.9569 0.897034 14.53V3.44059C0.897034 3.01072 1.04775 2.64157 1.34917 2.33312C1.65058 2.02468 2.01971 1.87046 2.45655 1.87046H5.95871C6.09621 1.44002 6.34716 1.08105 6.71158 0.793555C7.07598 0.506055 7.50546 0.362305 8.00002 0.362305C8.49458 0.362305 8.92406 0.506055 9.28846 0.793555C9.65288 1.08105 9.90383 1.44002 10.0413 1.87046H13.5435C13.9832 1.87046 14.3549 2.02468 14.6584 2.33312C14.9619 2.64157 15.1136 3.01072 15.1136 3.44059V14.53C15.1136 14.9569 14.9619 15.3236 14.6584 15.6299C14.3549 15.9363 13.9832 16.0895 13.5435 16.0895H2.45655ZM2.45655 14.53H13.5435V3.44059H2.45655V14.53ZM4.91036 12.699H8.79322C8.95388 12.699 9.09242 12.6402 9.20883 12.5227C9.32523 12.4051 9.38343 12.2652 9.38343 12.1028C9.38343 11.9405 9.32523 11.7994 9.20883 11.6796C9.09242 11.5597 8.95388 11.4998 8.79322 11.4998H4.91036C4.73751 11.4998 4.5928 11.5597 4.47624 11.6795C4.35968 11.7992 4.3014 11.942 4.3014 12.1076C4.3014 12.2679 4.35968 12.4066 4.47624 12.5235C4.5928 12.6405 4.73751 12.699 4.91036 12.699ZM4.91036 9.58487H11.1084C11.2691 9.58487 11.4076 9.52512 11.524 9.40562C11.6404 9.28613 11.6986 9.14377 11.6986 8.97853C11.6986 8.8133 11.6404 8.67319 11.524 8.55819C11.4076 8.44319 11.2691 8.38569 11.1084 8.38569H4.91036C4.73751 8.38569 4.5928 8.44429 4.47624 8.56147C4.35968 8.67866 4.3014 8.81987 4.3014 8.98511C4.3014 9.15034 4.35968 9.2916 4.47624 9.40892C4.5928 9.52622 4.73751 9.58487 4.91036 9.58487ZM4.91036 6.46258H11.1084C11.2691 6.46258 11.4076 6.40381 11.524 6.28627C11.6404 6.16874 11.6986 6.02879 11.6986 5.86644C11.6986 5.70409 11.6404 5.563 11.524 5.44316C11.4076 5.32332 11.2691 5.2634 11.1084 5.2634H4.91036C4.73751 5.2634 4.5928 5.32329 4.47624 5.44307C4.35968 5.56284 4.3014 5.70555 4.3014 5.8712C4.3014 6.03153 4.35968 6.17017 4.47624 6.28714C4.5928 6.4041 4.73751 6.46258 4.91036 6.46258ZM8.00002 3.07618C8.19952 3.07618 8.36937 3.00608 8.50957 2.86588C8.64976 2.72568 8.71985 2.55583 8.71985 2.35633C8.71985 2.15684 8.64976 1.987 8.50957 1.8468C8.36937 1.7066 8.19952 1.6365 8.00002 1.6365C7.80052 1.6365 7.63067 1.7066 7.49047 1.8468C7.35028 1.987 7.28019 2.15684 7.28019 2.35633C7.28019 2.55583 7.35028 2.72568 7.49047 2.86588C7.63067 3.00608 7.80052 3.07618 8.00002 3.07618Z"
          fill="#202020"
        />
      </g>
    </svg>
  )
}

export default TaskIcon
