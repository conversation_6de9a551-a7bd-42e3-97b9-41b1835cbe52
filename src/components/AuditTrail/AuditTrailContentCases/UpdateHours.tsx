import type React from 'react'
import { Box, palette, Typography } from 'lion-ui'
import type AuditTrailContentCaseProps from './AuditTrailContentCase.props'
import UserIcon from '@components/Icons/UserIcon/UserIcon'
import TaskIcon from '@components/Icons/TaskIcon/TaskIcon'
import AuditTrailGoToDetails from '@components/AuditTrail/AuditTrailGoToDetails/AuditTrailGoToDetails'
import PlaceholderIcon from '@components/Icons/PlaceholderIcon/PlaceholderIcon'
import getLocaleDateFormat from '@utils/getLocaleDateFormat'
import ScheduleIcon from '@components/Icons/ScheduleIcon/ScheduleIcon'

const UpdateHours = ({ entry, isOpen, isCollapsed }: AuditTrailContentCaseProps): React.JSX.Element => {
  const formattedNewStartDate = getLocaleDateFormat(entry.details.newAssignment.startDate)
  const formattedNewDueDate = getLocaleDateFormat(entry.details.newAssignment.dueDate)

  return (
    <Box sx={{ color: '#4D4D4D' }}>
      {!isCollapsed && (
        <Box sx={{ paddingLeft: '10rem', width: '35rem' }}>
          {entry.details.oldAssignment.isPlaceholder ? (
            <PlaceholderIcon width="12" height="12" />
          ) : (
            <UserIcon width="12" height="12" />
          )}{' '}
          <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
            {entry.details.oldAssignment.resourceName}
          </Typography>{' '}
          in <TaskIcon width="12" height="12" />{' '}
          <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
            {entry.details.oldAssignment.taskName}
          </Typography>{' '}
          under{' '}
          <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
            {entry.details.oldAssignment.projectName}
          </Typography>{' '}
          from{' '}
          <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
            {entry.details.oldAssignment.hoursPerDay} hours
          </Typography>{' '}
          to{' '}
          <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
            {entry.details.newAssignment.hoursPerDay} hours
          </Typography>{' '}
          on <ScheduleIcon width="14" height="14" />{' '}
          <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
            {formattedNewStartDate} - {formattedNewDueDate}
          </Typography>
          <AuditTrailGoToDetails
            assignmentId={entry.details.newAssignment.externalId}
            resourceId={entry.details.newAssignment.resourceId}
          />
        </Box>
      )}

      {isCollapsed && (
        <Box>
          {!isOpen && (
            <Box
              sx={{
                display: 'inline-block',
                maxWidth: '25rem',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }}
            >
              Changed hours per day for{' '}
              {entry.details.oldAssignment.isPlaceholder ? (
                <PlaceholderIcon width="12" height="12" />
              ) : (
                <UserIcon width="12" height="12" />
              )}{' '}
              <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
                {entry.details.oldAssignment.resourceName}
              </Typography>{' '}
              in <TaskIcon width="12" height="12" />{' '}
              <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
                {entry.details.oldAssignment.taskName}
              </Typography>{' '}
              under{' '}
              <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
                {entry.details.oldAssignment.projectName}
              </Typography>{' '}
              from{' '}
              <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
                {entry.details.oldAssignment.hoursPerDay} hours
              </Typography>{' '}
              to{' '}
              <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
                {entry.details.newAssignment.hoursPerDay} hours
              </Typography>{' '}
              on <ScheduleIcon width="14" height="14" />{' '}
              <Typography sx={{ fontWeight: 'bold', color: palette.primary.main, display: 'inline' }}>
                {formattedNewStartDate} - {formattedNewDueDate}
              </Typography>
            </Box>
          )}

          {isOpen && (
            <Box
              sx={{
                display: 'inline-block',
                maxWidth: '21rem',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }}
            >
              Changed hours per day for
            </Box>
          )}
        </Box>
      )}
    </Box>
  )
}

export default UpdateHours
