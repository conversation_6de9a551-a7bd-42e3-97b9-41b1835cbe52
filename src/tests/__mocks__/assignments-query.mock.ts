import { ASSIGNMENTS_QUERY } from '@gql/queries/assignments.query'
import { mockAssignment } from './assignments.mock'
import { activeDatesVar } from '@gql/client/localState'
import { CREATE_ASSIGNMENT_MUTATION } from '@gql/mutations/assignment.mutation'

export const mockAssignmentQuery = {
  query: ASSIGNMENTS_QUERY,
  variables: {
    params: {
      startDate: '2023-01-01',
      endDate: '2023-01-07',
      workCode: '10082922',
      userIds: []
    }
  },
  data: {
    assignments: mockAssignment
  }
}

export const mockAssignmentMutation = {
  query: CREATE_ASSIGNMENT_MUTATION,
  variables: {
    params: {
      taskExternalId: undefined,
      userAssignedExternalId: undefined,
      userLoggedInExternalId:'10082922',
      startDate: '2024-11-25',
      dueDate: '2024-11-25',
      hoursPerDay: 8,
      calendarStartDate: activeDatesVar().startDate,
      calendarDueDate: activeDatesVar().endDate
    }
  },

  data: {
    createAssignment: mockAssignment[0]
  }
}
