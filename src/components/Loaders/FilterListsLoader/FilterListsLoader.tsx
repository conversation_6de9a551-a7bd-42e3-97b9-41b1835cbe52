import { Box, Skeleton } from 'lion-ui'
import type FilterListsLoaderProps from './FilterListsLoader.props'

export const FilterListsLoader = (props: FilterListsLoaderProps) => {
  const { numOfRows } = props
  const rows = []

  for (let i = 0; i < numOfRows; i++) {
    rows.push(
      <Box data-testid="audit-trail-loader" key={i} sx={{ display: 'flex', gap: 2, padding: 2 }}>
        <Box sx={{ display: 'flex', gap: 8, justifyContent: 'space-evenly', width: '100%' }}>
          <Skeleton animation="wave" height={35} sx={{ width: '95%' }} />
          <Skeleton animation="wave" height={35} sx={{ width: '3%' }} />
        </Box>
      </Box>
    )
  }

  return <>{rows}</>
}
