import { render, screen } from '@testing-library/react'
import { ApolloClient, ApolloProvider, InMemoryCache, useQuery } from '@apollo/client'
import ResourceAdditionalDetails from '@components/Forms/Common/ResourceAdditionalDetails/ResourceAdditionalDetails'

jest.mock('@apollo/client', () => {
  const actual = jest.requireActual('@apollo/client')
  return {
    ...actual,
    useQuery: jest.fn(),
    gql: jest.fn(() => ({})) // Mock gql function
  }
})

let apolloClientInit: ApolloClient<unknown>
const cache = new InMemoryCache()

describe('ResourceAdditionalDetails Component', () => {
  const mockUpdateResourceOrgStructure = jest.fn()

  beforeEach(() => {
    apolloClientInit = new ApolloClient({
      cache
    })
  })
  afterEach(() => {
    jest.clearAllMocks()
  })

  test('renders loading state', () => {
    ;(useQuery as jest.Mock).mockReturnValue({
      data: null,
      loading: true,
      error: null
    })

    render(
      <ApolloProvider client={apolloClientInit}>
        <ResourceAdditionalDetails assignToId="123" updateResourceOrgStructure={mockUpdateResourceOrgStructure} />
      </ApolloProvider>
    )

    expect(screen.getByRole('progressbar')).toBeInTheDocument()
  })

  test('renders data correctly', () => {
    ;(useQuery as jest.Mock).mockReturnValue({
      data: {
        getResourceOrgStructure: {
          agencyName: 'Test Agency',
          locationName: 'Test Location',
          costCenterCode: '**********',
          costCenterName: 'Test Cost Center',
          agencyCode: '1176'
        }
      },
      loading: false,
      error: null
    })

    render(
      <ApolloProvider client={apolloClientInit}>
        <ResourceAdditionalDetails assignToId="123" updateResourceOrgStructure={mockUpdateResourceOrgStructure} />
      </ApolloProvider>
    )

    expect(screen.getByText('1176 : Test Agency')).toBeInTheDocument()
    expect(screen.getByText('Test Location')).toBeInTheDocument()
    expect(screen.getByText('********** : Test Cost Center')).toBeInTheDocument()
  })

  test('calls updateResourceOrgStructure with correct data', () => {
    const mockData = {
      getResourceOrgStructure: {
        agencyName: 'Test Agency',
        locationName: 'Test Location',
        costCenterName: 'Test Cost Center'
      }
    }
    ;(useQuery as jest.Mock).mockReturnValue({
      data: mockData,
      loading: false,
      error: null
    })

    render(
      <ApolloProvider client={apolloClientInit}>
        <ResourceAdditionalDetails assignToId="123" updateResourceOrgStructure={mockUpdateResourceOrgStructure} />
      </ApolloProvider>
    )

    expect(mockUpdateResourceOrgStructure).toHaveBeenCalledWith({
      data: mockData.getResourceOrgStructure,
      loading: false
    })
  })

  test('skips query when assignToId is not provided', () => {
    ;(useQuery as jest.Mock).mockReturnValue({
      data: null,
      loading: false,
      error: null
    })

    render(
      <ApolloProvider client={apolloClientInit}>
        <ResourceAdditionalDetails assignToId="" updateResourceOrgStructure={mockUpdateResourceOrgStructure} />
      </ApolloProvider>
    )

    expect(screen.queryByText('Additional Details')).not.toBeInTheDocument()
    expect(mockUpdateResourceOrgStructure).not.toHaveBeenCalled()
  })
})
