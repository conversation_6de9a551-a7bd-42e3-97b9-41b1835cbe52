import { type IconProps } from '../Icon.props'

const CheckIcon = ({ height, width }: IconProps) => {
  return (
    <svg width={width} height={height} viewBox="0 0 14 11" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M13.4223 0.744078C13.7477 1.06951 13.7477 1.59715 13.4223 1.92259L5.08893 10.2559C4.76349 10.5814 4.23586 10.5814 3.91042 10.2559L0.577085 6.92259C0.251649 6.59715 0.251649 6.06951 0.577085 5.74408C0.902522 5.41864 1.43016 5.41864 1.7556 5.74408L4.49967 8.48816L12.2438 0.744078C12.5692 0.418641 13.0968 0.418641 13.4223 0.744078Z"
        fill="white"
      />
    </svg>
  )
}

export default CheckIcon
