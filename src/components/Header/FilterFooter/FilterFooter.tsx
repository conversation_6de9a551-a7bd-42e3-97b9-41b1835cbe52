// External library imports
import { Box, Button, Grid } from 'lion-ui'

// Local file imports
import type FilterFooterProps from './FilterFooter.props'
import SimpleArrowIcon from '@components/Icons/SimpleArrowIcon/SimpleArrowIcon'
import {
  placeholderSettingsSaveVar,
  businessUnitsEmptyVar,
  placeholderSettingsResetVar,
  isApplyFiltersVar
} from '@gql/client/localState'
import { useReactiveVar } from '@apollo/client'

const FilterFooter = (props: FilterFooterProps) => {
  const businessUnitsEmpty = useReactiveVar(businessUnitsEmptyVar)
  return (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        justifyContent: 'start',
        alignItems: 'center',
        padding: '0.5rem 1.25rem 0.25rem 1.25rem'
      }}
    >
      {!props.isExpandPlaceholder && (
        <Grid item xs={12} sx={{ width: '20%', display: 'flex', justifyContent: 'right' }}>
          <Button
            disabled={props.enableFilter}
            variant="contained"
            color="primary"
            onClick={() => {
              props.filterAction()
              isApplyFiltersVar(true)
            }}
          >
            Apply Filters
          </Button>
        </Grid>
      )}
      {props.isExpandPlaceholder && (
        <Grid item xs={12} sx={{ width: '100%', display: 'flex', justifyContent: 'space-beween' }}>
          <Button variant="outlined" color="primary" onClick={props.expandPlaceholdersBackAction}>
            <SimpleArrowIcon width="20" height="20" rotation="-90" />
            Go Back
          </Button>
          <Button
            sx={{ marginLeft: 'auto', marginRight: '1rem' }}
            variant="contained"
            color="secondary"
            onClick={() => placeholderSettingsResetVar(true)}
          >
            Reset to Defaults
          </Button>
          <Button
            disabled={businessUnitsEmpty}
            variant="contained"
            color="primary"
            onClick={() => {
              placeholderSettingsSaveVar(true)
              isApplyFiltersVar(false)
            }}
          >
            Save
          </Button>
        </Grid>
      )}
    </Box>
  )
}

export default FilterFooter
