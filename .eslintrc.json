{"env": {"browser": true, "es2021": true}, "extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "standard-with-typescript", "plugin:react/jsx-runtime", "prettier", "plugin:testing-library/react", "plugin:jest-dom/recommended"], "plugins": ["@typescript-eslint", "react"], "overrides": [{"env": {"node": true}, "files": [".eslintrc.{js,cjs}", "jest.config.ts"], "parserOptions": {"sourceType": "script"}}, {"files": ["*.tsx"], "rules": {"@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/unbound-method": "off"}}], "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "rules": {"semi": [2, "never"], "@typescript-eslint/strict-boolean-expressions": "off", "@typescript-eslint/method-signature-style": "off", "camelcase": "off", "react/prop-types": "off", "@typescript-eslint/naming-convention": ["error", {"selector": "function", "format": ["camelCase", "UPPER_CASE"]}, {"selector": "class", "format": ["PascalCase"]}, {"selector": "classProperty", "format": ["camelCase"]}, {"selector": "interface", "format": ["PascalCase"]}, {"selector": "parameter", "format": ["camelCase", "PascalCase"], "leadingUnderscore": "allow"}, {"selector": "property", "format": ["camelCase"]}, {"selector": "objectLiteralProperty", "format": ["UPPER_CASE", "camelCase"]}, {"selector": "method", "format": ["camelCase"]}], "testing-library/prefer-screen-queries": "off"}, "ignorePatterns": ["public/gridstack-all.js"]}