import { render, screen } from '@testing-library/react'
import CalendarDay from '@components/Calendar/CalendarDay/CalendarDay'
import '@testing-library/jest-dom'
import isTodayDate from '@utils/isTodayDate'
import { palette } from 'lion-ui'

jest.mock('@utils/isTodayDate', () => jest.fn())

describe('CalendarDay component', () => {
  const mockDay = {
    date: '2023-04-01',
    dayShortName: 'Sa',
    dayMiddleName: 'Sat',
    dayNumber: 1,
    isWeekend: false
  }

  const color = palette['500-Accent-1'].main

  beforeEach(() => {
    jest.resetAllMocks()
  })

  it('renders without crashing', () => {
    render(<CalendarDay day={mockDay} month="April" index={0} isNewMonth={false} />)
    expect(screen.getByText('April')).toBeInTheDocument()
  })

  it('displays the day and day number', () => {
    render(<CalendarDay day={mockDay} month="April" index={1} isNewMonth={false} />)
    const dayElement = screen.getByText('Sat')
    const numberElement = screen.getByText('1')

    expect(dayElement).toBeInTheDocument()
    expect(numberElement).toBeInTheDocument()
  })

  it('does not highlight days that are not today', () => {
    ;(isTodayDate as jest.Mock).mockImplementation(() => false)
    render(<CalendarDay day={mockDay} month="April" index={3} isNewMonth={false} />)
    const dayBox = screen.getAllByTestId('Calendar.Header.Navigation.Day.Item')[0]
    expect(dayBox).not.toHaveStyle(`border-bottom: 2px solid ${color};`)
  })

  it('highlights today with a special border', () => {
    ;(isTodayDate as jest.Mock).mockImplementation(() => true)
    render(<CalendarDay day={mockDay} month="April" index={3} isNewMonth={false} />)
    const dayBox = screen.getAllByTestId('Calendar.Header.Navigation.Day.Item')[0]

    expect(dayBox).toHaveStyle(`border-bottom: 2px solid ${color};`)
  })

  it('shows the month name on the first day of the view', () => {
    render(<CalendarDay day={mockDay} month="April" index={0} isNewMonth={true} />)
    expect(screen.getByText('April')).toBeInTheDocument()
  })
})
