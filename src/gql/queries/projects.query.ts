import { gql } from '@apollo/client'

const PROJECTS_BY_PROJECT_MANAGER_QUERY = gql`
  query GetProjectsByProjectManagerWorkCode($workCode: String) {
    getProjectsByProjectManagerWorkCode(workCode: $workCode) {
      id
      label: name
      name
      clientId
      brandId
      integrationId
    }
  }
`
const PROJECTS_BY_PROJECT_ID_QUERY = gql`
  query getProjectsByProjectId($projectId: String) {
    getProjectsByProjectId(projectId: $projectId) {
      id
      name
      clientId
      brandId
      costCenterCode
      agencyCode
    }
  }
`

  
const PROJECTS_BY_RESOURCE_MANAGER_QUERY = gql`
  query GetProjectsByResourceManagerId($workCode: String) {
    getProjectsByResourceManagerId(workCode: $workCode) {
      id
      label: name
      name
      integrationId
      brandId
    }
  }
`
export { PROJECTS_BY_PROJECT_MANAGER_QUERY, PROJECTS_BY_RESOURCE_MANAGER_QUERY,PROJECTS_BY_PROJECT_ID_QUERY }
