import { useCallback, useMemo } from 'react'

// Utils imports
import { calculatesHoursByDay, getAssignmentsMappedByDate } from '@utils/utilizationHelpers'

// Hooks imports
import useCalendarDataCache from '@hooks/useCalendarDataCache'

// Type imports
import { type Assignment } from '@typeDefs/Assignments'
import { type CalendarDayData } from '@typeDefs/Calendar'

/**
 * Custom hook that calculates the total assigned hours and provides a function to calculate assigned hours by day.
 *
 * @param assignments - The list of assignments.
 * @returns An object containing the total assigned hours  and a function to calculate assigned hours by day.
 */

const useAssignments = (
  assignments: Assignment[]
): {
  totalAssignedHours: number
  calculateAssignedHoursByDay: (calendarDay: CalendarDayData) => number
} => {
  const { calendarDays } = useCalendarDataCache()
  const assignmentsMap = useMemo(() => getAssignmentsMappedByDate(assignments), [assignments])

  const totalAssignedHours = useMemo(
    () =>
      calendarDays?.reduce((sum, calendarDay) => {
        const totalHoursByDay = calculatesHoursByDay(calendarDay, assignmentsMap)
        return sum + totalHoursByDay
      }, 0),
    [assignmentsMap, calendarDays]
  )

  const calculateAssignedHoursByDay = useCallback(
    (calendarDay: CalendarDayData) => {
      return calculatesHoursByDay(calendarDay, assignmentsMap)
    },
    [assignmentsMap]
  )

  return { totalAssignedHours, calculateAssignedHoursByDay }
}

export default useAssignments
