import USERS_GROUPS_QUERY from '@gql/queries/usersGroups.query'

export const usersGroupsMock = [
  {
    request: {
      query: USERS_GROUPS_QUERY,
      variables: {
        params: {
          resourceManager: 1,
          pageNumber: 1,
          pageSize: 10
        }
      }
    },
    result: {
      data: {
        usersGroups: {
          // Mock the expected response data here
          // Example:
          groups: [
            { id: 1, name: 'Group 1' },
            { id: 2, name: 'Group 2' }
          ]
        }
      }
    }
  }
]
