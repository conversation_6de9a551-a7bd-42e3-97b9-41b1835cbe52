import { type IconProps } from '../Icon.props'

const PmIcon = ({ height, width, rotation }: IconProps) => {
  return (
    <svg
      style={{ transform: `rotate(${rotation}deg)` }}
      width={height}
      height={width}
      viewBox="0 0 18 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="pm icon">
        <path
          id="Vector"
          d="M11.6535 9.84493C11.1398 9.84493 10.7141 9.67727 10.3764 9.34195C10.0388 9.00662 9.86994 8.58487 9.86994 8.0767C9.86994 7.56854 10.0376 7.14563 10.3729 6.80795C10.7083 6.47028 11.13 6.30145 11.6382 6.30145C12.1463 6.30145 12.5677 6.47011 12.9022 6.80743C13.2367 7.14476 13.404 7.56272 13.404 8.06133C13.404 8.57506 13.2369 9.00075 12.9027 9.33842C12.5685 9.67609 12.1521 9.84493 11.6535 9.84493ZM8.85689 13.8333C8.65549 13.8333 8.48944 13.7689 8.35874 13.64C8.22804 13.5111 8.16269 13.3461 8.16269 13.145V12.803C8.16269 12.4675 8.23528 12.1802 8.38044 11.9412C8.52561 11.7021 8.72959 11.5142 8.99238 11.3774C9.39947 11.1809 9.82903 11.0298 10.2811 10.9243C10.7331 10.8187 11.1874 10.7659 11.6439 10.7659C12.1004 10.7659 12.548 10.8187 12.9867 10.9243C13.4255 11.0298 13.8568 11.1819 14.2808 11.3804C14.5353 11.5165 14.7389 11.7037 14.8916 11.9423C15.0443 12.1808 15.1207 12.4677 15.1207 12.8032V13.145C15.1207 13.3461 15.0536 13.5111 14.9193 13.64C14.7851 13.7689 14.6177 13.8333 14.417 13.8333H8.85689ZM6.54011 7.82682C5.71654 7.82682 5.04569 7.56775 4.52756 7.0496C4.00943 6.53147 3.75036 5.86062 3.75036 5.03705C3.75036 4.21348 4.00943 3.54357 4.52756 3.0273C5.04569 2.51102 5.71654 2.25288 6.54011 2.25288C7.36368 2.25288 8.03674 2.51067 8.55929 3.02623C9.08185 3.5418 9.34313 4.21364 9.34313 5.04177C9.34313 5.86219 9.08185 6.53147 8.55929 7.0496C8.03674 7.56775 7.36368 7.82682 6.54011 7.82682ZM1.58224 13.8333C1.38963 13.8333 1.22735 13.7689 1.09539 13.64C0.963439 13.5111 0.897461 13.3473 0.897461 13.1486V12.0174C0.897461 11.5367 1.00253 11.1123 1.21268 10.7442C1.42282 10.3761 1.72564 10.1037 2.12113 9.92692C2.92709 9.57515 3.68656 9.31436 4.39953 9.14455C5.11249 8.97475 5.82629 8.88985 6.54093 8.88985C6.79891 8.88985 7.06183 8.90012 7.32971 8.92065C7.59759 8.94118 7.77669 8.96014 7.86703 8.97753V9.75183C7.73599 9.83097 7.63609 9.9089 7.56731 9.98563C7.49852 10.0624 7.43007 10.1592 7.36195 10.2761C7.24746 10.2761 7.12512 10.2777 6.99493 10.2808C6.86473 10.2839 6.71219 10.2855 6.53731 10.2855C5.91517 10.2855 5.31338 10.3455 4.73194 10.4656C4.15051 10.5856 3.5081 10.8147 2.80471 11.1529C2.64288 11.222 2.51558 11.3374 2.42283 11.4993C2.33007 11.6612 2.28369 11.8251 2.28369 11.991V12.4471H7.21631V13.8333H1.58224ZM6.54011 6.44057C6.94163 6.44057 7.27657 6.30687 7.54493 6.03948C7.81328 5.77209 7.94746 5.43899 7.94746 5.04018C7.94746 4.63877 7.81429 4.30334 7.54796 4.03388C7.28163 3.76443 6.94538 3.6297 6.53923 3.6297C6.14085 3.6297 5.80748 3.76457 5.53913 4.03432C5.27077 4.30406 5.13659 4.63779 5.13659 5.03552C5.13659 5.43582 5.27013 5.77007 5.53721 6.03827C5.80429 6.30647 6.13859 6.44057 6.54011 6.44057Z"
          fill="white"
        />
      </g>
    </svg>
  )
}

export default PmIcon
