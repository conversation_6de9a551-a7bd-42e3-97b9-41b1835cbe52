// React imports
import { useEffect, useState, useRef } from 'react'

// lion ui import
import { Box } from 'lion-ui'

// Apollo import
import { useReactiveVar } from '@apollo/client'

// Graphql import
import { numOfWeeksVar, userProfileVar } from '@gql/client/localState'
import { UserProfile } from '@constants/userProfile'

// Constants import
import { numsOfDaysPerWeek } from '@constants/timeOptions'

// Types import
import { GridStackOptions } from '@constants/gridStackOptions'
import type CalendarItemProps from './CalendarDragItem.props'
import { type TileStatus } from '@typeDefs/Tile'

// Styles import
import getCalendarItemStyle, { getTileStatus } from './CalendarDragItem.style'
import CalendarDragItemContent from '@components/Calendar/CalendarDragItemContent/CalendarDragItemContent'

const CalendarDragItem = ({ assignment, openModal, isLoading, resourceType }: CalendarItemProps) => {
  const { id, x, y, width, height, taskState, assignmentState, blockedByTimesheet } = assignment

  const divRef = useRef<HTMLDivElement>(null)
  const userProfile = useReactiveVar(userProfileVar)
  const currentColumns = useReactiveVar(numOfWeeksVar) * numsOfDaysPerWeek
  const [currTileWidth, setCurrTileWidth] = useState(1)
  const isLightUser = userProfile === UserProfile.LIGHT_USER
  const disableTile =
    assignmentState !== 'rejected' &&
    (isLightUser ||
      taskState?.toLowerCase() === 'external' ||
      taskState?.toLowerCase() === 'complete' ||
      blockedByTimesheet)
      ? 'true'
      : 'false'

  const tileStatus = getTileStatus(assignmentState, taskState, resourceType)

  const heightHandler = () => {
    if (height < 1) {
      return 5
    } else {
      return Math.round(Math.abs(height) * 4)
    }
  }
  const tileHeight = heightHandler()
  const bottomResizeHandlerCSS = height < 3 ? 'displaceBottomHandler' : ''

  useEffect(() => {
    const div = divRef.current
    setCurrTileWidth(parseInt(div?.getAttribute('gs-w')?.toString() ?? '1'))
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [divRef.current?.getAttribute('gs-w'), tileHeight])

  return (
    <Box
      className={`grid-stack-item p-relative gs-${currentColumns} ${bottomResizeHandlerCSS}`}
      data-testid="calendar-drag-item"
      component={'div'}
      position={'relative'}
      data-gs-id={id}
      gs-x={x}
      gs-y={y}
      gs-w={width}
      gs-h={tileHeight}
      gs-max-h={GridStackOptions.MAX_HEIGHT}
      gs-min-h={GridStackOptions.MIN_HEIGHT}
      gs-no-move={disableTile}
      gs-no-resize={disableTile}
      ref={divRef}
      sx={{ minHeight: '24px' }}
    >
      <Box
        className="grid-stack-item-content"
        sx={{
          ...getCalendarItemStyle(tileStatus.toLowerCase() as TileStatus),
          cursor: disableTile === 'true' ? 'not-allowed' : 'grab',
          userSelect: 'none'
        }}
      >
        <CalendarDragItemContent
          assignment={assignment}
          resourceType={resourceType}
          isLoading={isLoading}
          isLocked={disableTile === 'true'}
          openModal={openModal}
          currTileWidth={currTileWidth}
          currTileHeight={tileHeight}
        />
      </Box>
    </Box>
  )
}

export default CalendarDragItem
