// External library imports
import {
  Autocomplete,
  Box,
  Button,
  Checkbox,
  Chip,
  CircularProgress,
  Divider,
  FormLabel,
  palette,
  <PERSON>Field,
  Tooltip,
  Typography
} from 'lion-ui'

// Local file imports
import type AllocationDetailsFilterProps from './AllocationDetailsFilter.props'
import { useEffect, useState } from 'react'
import { useQuery, useReactiveVar } from '@apollo/client'
import { type Agency } from '@typeDefs/Agency'
import { AGENCY_LOCATIONS_QUERY, COST_CENTERS_QUERY, RESOURCES_AGENCY_QUERY } from '@gql/queries/resources.query'
import { gplError } from '@utils/gplError'
import { debounce } from 'lodash'
import { type CostCenter } from '@typeDefs/CostCenter'

import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank'
import CheckBoxIcon from '@mui/icons-material/CheckBox'
import { PLACEHOLDERS_AGENCY_QUERY } from '@gql/queries/placeholders.query'
import { userInfoVar } from '@gql/client/localState'

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />
const checkedIcon = <CheckBoxIcon fontSize="small" />

const AllocationDetailsFilter = (props: AllocationDetailsFilterProps) => {
  const activeUser = useReactiveVar(userInfoVar)

  const [agencySearchTerm, setAgencySearchTerm] = useState<string>('')
  const [agencyInputValue, setAgencyInputValue] = useState<string>('')
  const [locationSearchTerm, setLocationSearchTerm] = useState<string>('')
  const [costCenterSearchTerm, setCostCenterSearchTerm] = useState<string>('')

  const [organizationStructureData, setOrganizationStructureData] = useState<{
    agencies: Agency[]
    costCenters: CostCenter[]
    locations: string[]
  }>({ agencies: [], costCenters: [], locations: [] })

  const [costCenters, setCostCenters] = useState<CostCenter[]>([])
  const [locations, setLocations] = useState<string[]>([])

  const agenciesQuery = props.type === 'placeholders' ? PLACEHOLDERS_AGENCY_QUERY : RESOURCES_AGENCY_QUERY
  const responseObject =
    props.type === 'placeholders' ? 'getOrgStructureByPlaceholderAssignments' : 'getOrgStructureByResourceAssignments'

  const debouncedSetSearchTerm = debounce((value: string) => {
    setAgencySearchTerm(value)
  }, 300)

  const handleBusinessUnitChange = (value: Agency[] | null) => {
    if (value === null || value.length === 0) {
      setLocations(organizationStructureData.locations ?? [])
      setCostCenters(organizationStructureData.costCenters ?? [])
    }

    props.setAllocationDetailsFiltersFormData((prev) => ({
      ...prev,
      businessUnits: value,
      locations: null,
      costCenters: null
    }))
    setLocationSearchTerm('')
  }

  const handleCostCenterChange = (value: CostCenter[] | null) => {
    props.setAllocationDetailsFiltersFormData((prev) => ({
      ...prev,
      costCenters: value
    }))
  }

  const {
    data: organizationStructureDataResponse,
    error: orgStructurePlaceholdersAssignmentsError,
    loading: orgStructurePlaceholdersAssignmentsLoading,
    networkStatus: orgStructurePlaceholdersAssignmentsNetworkStatus
  } = useQuery(agenciesQuery, {
    variables: {
      params: {
        userLoggedInExternalId: activeUser?.altairNumber
      }
    },
    onError: (error) => {
      gplError(error, orgStructurePlaceholdersAssignmentsError)
    }
  })

  const {
    data: locationsData,
    error: locationError,
    loading: locationLoading
  } = useQuery<{ getLocationsByAgenciesOrgStructure: string[] }>(AGENCY_LOCATIONS_QUERY, {
    variables: {
      agencyCodes: props.allocationDetailsFiltersFormData?.businessUnits?.map((bu) => bu.agencyCode) ?? []
    },
    onError: (error) => {
      gplError(error, locationError)
    },
    skip:
      orgStructurePlaceholdersAssignmentsNetworkStatus !== 7 || // 7 means the query is complete
      !organizationStructureData.locations ||
      organizationStructureData.locations.length === 0 ||
      !props.allocationDetailsFiltersFormData.businessUnits ||
      props.allocationDetailsFiltersFormData.businessUnits.length === 0
  })

  const { error: costCentersError, loading: costCentersLoading } = useQuery<{
    getCostCentersByLocationsOrgStructure: CostCenter[]
  }>(COST_CENTERS_QUERY, {
    variables: {
      agencyCodes: props.allocationDetailsFiltersFormData.businessUnits?.map((bu) => bu.agencyCode) ?? [],
      cities: props.allocationDetailsFiltersFormData.locations
    },
    onError: (error) => {
      gplError(error, costCentersError)
    },
    onCompleted: (data) => {
      if (data.getCostCentersByLocationsOrgStructure) {
        const costCentersAlreadyRetrieved = data.getCostCentersByLocationsOrgStructure.filter((costCenter) => {
          return organizationStructureData.costCenters.some((cc) => cc.costCenterCode === costCenter.costCenterCode)
        })

        const latestLocationInArray =
          organizationStructureData.costCenters[organizationStructureData.costCenters.length - 1]
        setCostCenters([...costCentersAlreadyRetrieved, latestLocationInArray])
      }
    },
    skip:
      !organizationStructureData.costCenters ||
      organizationStructureData.costCenters.length === 0 ||
      !props.allocationDetailsFiltersFormData.businessUnits ||
      props.allocationDetailsFiltersFormData.businessUnits.length === 0
  })

  const handleClearFilters = () => {
    props.setAllocationDetailsFiltersFormData({
      businessUnits: null,
      locations: null,
      costCenters: null
    })
    setAgencySearchTerm('')
    setAgencyInputValue('')
    setCostCenterSearchTerm('')
    setLocationSearchTerm('')
    // setGetLocations(true)
  }

  useEffect(() => {
    if (organizationStructureDataResponse) {
      const agencies = organizationStructureDataResponse[responseObject].agencies ?? []
      const costCenters: CostCenter[] = organizationStructureDataResponse[responseObject].costCenters ?? []
      const locations: string[] = organizationStructureDataResponse[responseObject].locations ?? []

      setTimeout(() => {
        setOrganizationStructureData({
          agencies,
          costCenters,
          locations
        })
      }, 0)

      setLocations(locations)
      setCostCenters(costCenters)
    }
  }, [organizationStructureDataResponse, responseObject])

  useEffect(() => {
    if (locationsData?.getLocationsByAgenciesOrgStructure) {
      const availableLocations = (organizationStructureData.locations ?? []).map((location) => location.toLowerCase())
      const filteredLocations = locationsData.getLocationsByAgenciesOrgStructure.filter((location) => {
        return availableLocations.includes(location.toLowerCase())
      })

      const latestLocationInArray = organizationStructureData.locations[organizationStructureData.locations.length - 1]
      setLocations([...filteredLocations, latestLocationInArray])
    }
  }, [locationsData, organizationStructureData.locations])

  const totalSelectedFilters =
    (props.allocationDetailsFiltersFormData.businessUnits?.length ?? 0) +
    (props.allocationDetailsFiltersFormData.locations?.length ?? 0) +
    (props.allocationDetailsFiltersFormData.costCenters?.length ?? 0)

  return (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'start',
        alignContent: 'center',
        alignItems: 'center',
        borderRight: `1px solid ${palette.secondary.main}`
      }}
    >
      <Box
        sx={{
          width: '100%',
          backgroundColor: palette.grey[50],
          padding: '1rem',
          borderBottom: `1px solid ${palette.secondary.main}`
        }}
      >
        <Typography variant="h3">Filters</Typography>
        <Typography>{totalSelectedFilters} Selected</Typography>
      </Box>

      <Box sx={{ width: '100%', padding: '0.75rem 1rem' }}>
        <FormLabel
          sx={{
            fontWeight: 'bold',
            color: palette.primary.dark,
            padding: '0.7rem 0',
            fontSize: '0.875rem'
          }}
          htmlFor="businessUnit"
        >
          Agency
        </FormLabel>
        <Autocomplete
          multiple
          disableCloseOnSelect
          sx={{ marginTop: '0.5rem' }}
          fullWidth
          disablePortal
          id="businessUnit"
          data-testid="businessUnit"
          options={[...(organizationStructureData.agencies ?? [])]}
          getOptionLabel={(option) => `${option?.agencyCode}: ${option?.agencyName}`}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder="Search for a Agency..."
              InputProps={{
                ...params.InputProps,
                endAdornment: (
                  <>
                    {orgStructurePlaceholdersAssignmentsLoading ? (
                      <CircularProgress size={20} sx={{ color: '#999', marginRight: '8px' }} />
                    ) : null}
                    {params.InputProps.endAdornment}
                  </>
                )
              }}
            />
          )}
          size="small"
          isOptionEqualToValue={(option, value) => option.agencyCode === value.agencyCode}
          value={props.allocationDetailsFiltersFormData.businessUnits ?? []}
          inputValue={agencyInputValue}
          onInputChange={(_e, newInputValue, reason) => {
            if (reason === 'input') {
              setAgencyInputValue(newInputValue)
              debouncedSetSearchTerm(newInputValue)
            }
          }}
          onChange={(_e, newValue) => {
            handleBusinessUnitChange(newValue)
          }}
          noOptionsText={agencySearchTerm.length < 3 ? 'Type at least 3 characters' : ''}
          loading={orgStructurePlaceholdersAssignmentsLoading}
          renderOption={(props, option, { selected }) => {
            const { key, ...optionProps } = props
            if (option.agencyName === 'No agencies found' || option.agencyName === 'Type at least 3 characters') {
              return (
                <li key={key} {...optionProps}>
                  <Typography variant="body2" color="textSecondary">
                    {option.agencyName}
                  </Typography>
                </li>
              )
            }
            return (
              <li key={key} {...optionProps}>
                <Checkbox icon={icon} checkedIcon={checkedIcon} style={{ marginRight: 8 }} checked={selected} />
                {option.agencyCode}: {option.agencyName}
              </li>
            )
          }}
          filterOptions={(options, state) => {
            if (state.inputValue.length === 0) {
              return options
            }
            if (state.inputValue.length < 3) {
              return [{ agencyCode: '', agencyName: 'Type at least 3 characters' }]
            }
            const filtered = options.filter((option) => {
              const lowerCaseInput = state.inputValue.toLowerCase()
              const searchValue = option.agencyCode.toLowerCase() + ' ' + option.agencyName.toLowerCase()
              return searchValue.includes(lowerCaseInput)
            })
            return filtered.length === 0 ? [{ agencyCode: '', agencyName: 'No agencies found' }] : filtered
          }}
          renderTags={(selected, getTagProps) => {
            const additionalCount = selected.length - 1
            const tagProps = getTagProps({ index: 0 })

            return [
              // eslint-disable-next-line react/jsx-key
              <Chip {...tagProps} label={`${selected[0]?.agencyCode}: ${selected[0]?.agencyName}`} />,
              ...(additionalCount > 0
                ? [
                    <Tooltip
                      key="more"
                      title={selected
                        .slice(1)
                        .map((o) => `${o.agencyCode}: ${o.agencyName}`)
                        .join(', ')}
                    >
                      <Chip {...getTagProps({ index: 1 })} label={`+${additionalCount}`} onDelete={undefined} />
                    </Tooltip>
                  ]
                : [])
            ]
          }}
          onClose={() => {
            // Reset the search term when the dropdown is closed
            setAgencySearchTerm('')
            setAgencyInputValue('')
          }}
        />
      </Box>

      <Box sx={{ width: '100%', padding: '0.75rem 1rem' }}>
        <Typography variant="h3" sx={{ fontSize: '0.875rem', fontWeight: 'bold' }}>
          Location
        </Typography>
        <Autocomplete
          multiple
          disableCloseOnSelect
          sx={{ marginTop: '0.5rem' }}
          fullWidth
          disablePortal
          id="location"
          data-testid="location"
          options={[...(locations ?? [])]}
          getOptionLabel={(option) => `${option}`}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder="Search for a location..."
              InputProps={{
                ...params.InputProps,
                endAdornment: (
                  <>
                    {locationLoading ? <CircularProgress size={20} sx={{ color: '#999', marginRight: '8px' }} /> : null}
                    {params.InputProps.endAdornment}
                  </>
                )
              }}
            />
          )}
          size="small"
          isOptionEqualToValue={(option, value) => option === value}
          value={props.allocationDetailsFiltersFormData.locations ?? []}
          inputValue={locationSearchTerm}
          onInputChange={(_e, newInputValue, reason) => {
            if (reason === 'input') {
              setLocationSearchTerm(newInputValue)
            }
          }}
          onChange={(_e, newValue) => {
            props.setAllocationDetailsFiltersFormData((prev) => ({
              ...prev,
              locations: newValue
            }))
          }}
          noOptionsText={locationSearchTerm.length < 3 ? 'Type at least 3 characters' : 'No locations found'}
          loading={costCentersLoading}
          renderOption={(props, option, { selected }) => {
            const { key, ...optionProps } = props

            if (option === 'No locations found' || option === 'Type at least 3 characters') {
              return (
                <li key={key} {...optionProps}>
                  <Typography variant="body2" color="textSecondary">
                    {option}
                  </Typography>
                </li>
              )
            }

            return (
              <li key={key} {...optionProps}>
                <Checkbox icon={icon} checkedIcon={checkedIcon} style={{ marginRight: 8 }} checked={selected} />
                {option}
              </li>
            )
          }}
          filterOptions={(options, state) => {
            if (state.inputValue.length === 0) {
              return options
            }
            if (state.inputValue.length < 3) {
              return ['Type at least 3 characters']
            }
            const filtered = options.filter((option) => option.toLowerCase().includes(state.inputValue.toLowerCase()))
            return filtered.length === 0 ? ['No locations found'] : filtered
          }}
          renderTags={(selected, getTagProps) => {
            const additionalCount = selected.length - 1
            const tagProps = getTagProps({ index: 0 })

            return [
              // eslint-disable-next-line react/jsx-key
              <Chip {...tagProps} label={`${selected[0]}`} />,
              ...(additionalCount > 0
                ? [
                    <Tooltip
                      key="more"
                      title={selected
                        .slice(1)
                        .map((o) => `${o}`)
                        .join(', ')}
                    >
                      <Chip {...getTagProps({ index: 1 })} label={`+${additionalCount}`} onDelete={undefined} />
                    </Tooltip>
                  ]
                : [])
            ]
          }}
          onClose={() => {
            // Reset the search term when the dropdown is closed
            setLocationSearchTerm('')
          }}
        />
      </Box>

      <Box sx={{ width: '100%', padding: '0.75rem 1rem' }}>
        <FormLabel
          sx={{
            fontWeight: 'bold',
            color: palette.primary.dark,
            padding: '0.7rem 0',
            fontSize: '0.875rem'
          }}
          htmlFor="costCenter"
        >
          Cost Center
        </FormLabel>
        <Autocomplete
          multiple
          disableCloseOnSelect
          sx={{ marginTop: '0.5rem' }}
          fullWidth
          disablePortal
          id="costCenter"
          data-testid="costCenter"
          options={[...(costCenters ?? [])]}
          getOptionLabel={(option) => `${option?.costCenterCode}: ${option?.costCenterName}`}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder="Search for a cost center..."
              InputProps={{
                ...params.InputProps,
                endAdornment: (
                  <>
                    {costCentersLoading ? (
                      <CircularProgress size={20} sx={{ color: '#999', marginRight: '8px' }} />
                    ) : null}
                    {params.InputProps.endAdornment}
                  </>
                )
              }}
            />
          )}
          size="small"
          isOptionEqualToValue={(option, value) => option.costCenterCode === value.costCenterCode}
          value={props.allocationDetailsFiltersFormData.costCenters ?? []}
          inputValue={costCenterSearchTerm}
          onInputChange={(_e, newInputValue, reason) => {
            if (reason === 'input') {
              setCostCenterSearchTerm(newInputValue)
            }
          }}
          onChange={(_e, newValue) => {
            handleCostCenterChange(newValue)
          }}
          noOptionsText={costCenterSearchTerm.length < 3 ? 'Type at least 3 characters' : 'No cost centers found'}
          loading={costCentersLoading}
          renderOption={(props, option, { selected }) => {
            const { key, ...optionProps } = props
            if (
              option.costCenterName === 'No cost centers found' ||
              option.costCenterName === 'Type at least 3 characters'
            ) {
              return (
                <li key={key} {...optionProps}>
                  <Typography variant="body2" color="textSecondary">
                    {option.costCenterName}
                  </Typography>
                </li>
              )
            }
            return (
              <li key={key} {...optionProps}>
                <Checkbox icon={icon} checkedIcon={checkedIcon} style={{ marginRight: 8 }} checked={selected} />
                {option.costCenterCode}: {option.costCenterName}
              </li>
            )
          }}
          filterOptions={(options, state) => {
            if (state.inputValue.length === 0) {
              return options
            }
            if (state.inputValue.length < 3) {
              return [{ costCenterCode: '', costCenterName: 'Type at least 3 characters' }]
            }
            const filtered = options.filter((option) => {
              const lowerCaseInput = state.inputValue.toLowerCase()
              const searchValue = option.costCenterCode.toLowerCase() + ' ' + option.costCenterName.toLowerCase()
              return searchValue.includes(lowerCaseInput)
            })
            return filtered.length === 0 ? [{ costCenterCode: '', costCenterName: 'No cost centers found' }] : filtered
          }}
          renderTags={(selected, getTagProps) => {
            const additionalCount = selected.length - 1
            const tagProps = getTagProps({ index: 0 })

            return [
              // eslint-disable-next-line react/jsx-key
              <Chip {...tagProps} label={`${selected[0]?.costCenterCode}: ${selected[0]?.costCenterName}`} />,
              ...(additionalCount > 0
                ? [
                    <Tooltip
                      key="more"
                      title={selected
                        .slice(1)
                        .map((o) => `${o.costCenterCode}: ${o.costCenterName}`)
                        .join(', ')}
                    >
                      <Chip {...getTagProps({ index: 1 })} label={`+${additionalCount}`} onDelete={undefined} />
                    </Tooltip>
                  ]
                : [])
            ]
          }}
          onClose={() => {
            // Reset the search term when the dropdown is closed
            setCostCenterSearchTerm('')
          }}
        />
      </Box>

      <Divider sx={{ width: '100%' }} />

      <Box sx={{ width: '100%', padding: '0.75rem 1rem' }}>
        <Button
          sx={{
            backgroundColor: palette.common.white,
            gap: '0.5rem',
            borderColor: palette.secondary.main,
            fontWeight: 'bold'
          }}
          variant="outlined"
          onClick={handleClearFilters}
          fullWidth
          data-testid="Calendar.Header.Navigation.Day.Today.Button"
        >
          Clear
        </Button>
      </Box>
    </Box>
  )
}

export default AllocationDetailsFilter
