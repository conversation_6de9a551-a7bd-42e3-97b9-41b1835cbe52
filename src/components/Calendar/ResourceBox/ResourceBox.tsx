import type React from 'react'
import { memo } from 'react'
import { Box, palette, Typography } from 'lion-ui'
import type ResourceBoxProps from './ResourceBox.props'
import ResourceBoxUtilization from '../ResourceBoxUtilization/ResourceBoxUtilization'

const ResourceBox = ({ resource, assignments, type }: ResourceBoxProps): React.JSX.Element => {
  const calculateAvailableCapacity = () => {
    return (
      resource.totalCapacity -
        resource.timeOffDetails?.reduce((acc, timeOff) => {
          return acc + (+timeOff.timeOffHours || 0)
        }, 0) || 0
    )
  }
  return (
    <Box
      sx={{
        padding: 2,
        position: 'relative',
        height: '100%',
        width: '100%',
        background:
          type === 'resource'
            ? 'transparent'
            : `repeating-linear-gradient(125deg, ${palette.grey['200']}, ${palette.grey['200']} 1px, transparent 1px, transparent 5px)`
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between'
        }}
      >
        <Typography
          sx={{
            maxWidth: '13.5rem',
            fontWeight: 600,
            textOverflow: 'ellipsis',
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            ...(resource.requiresAssignApproval && {
              borderLeft: '4px solid #BDAB78',
              paddingLeft: '6px',
            }),
            fontSize: '0.875rem'
          }}
          variant="BodyLRegular"
        >
          {resource.name}
        </Typography>
        {type === 'resource' && (
          <ResourceBoxUtilization totalCapacity={calculateAvailableCapacity()} assignments={assignments} />
        )}
      </Box>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'flex-start',
          fontWeight: 400,
          ...(resource.requiresAssignApproval && {
            borderLeft: '4px solid #BDAB78',
            paddingLeft: '6px',
          }),
          gap: 3,
          paddingTop: 1
        }}
      >
        <Box
          sx={{
            display: 'flex',
            width: '65%',
            flexDirection: 'column',
            alignItems: 'flex-start'
          }}
        >
          <Typography
            variant="BodySRegular"
            sx={{
              color: palette.grey['700'],
              maxWidth: '8rem',
              textOverflow: 'ellipsis',
              overflow: 'hidden',
              whiteSpace: 'nowrap'
            }}
          >
            {resource.jobTitle}
          </Typography>
          <Typography
            variant="BodySRegular"
            sx={{
              color: palette.grey['700'],
              maxWidth: '8rem',
              textOverflow: 'ellipsis',
              overflow: 'hidden',
              whiteSpace: 'nowrap'
            }}
          >
            {resource.position}
          </Typography>
          <Typography
            variant="BodySRegular"
            sx={{
              color: palette.grey['700'],
              maxWidth: '8rem',
              textOverflow: 'ellipsis',
              overflow: 'hidden',
              whiteSpace: 'nowrap'
            }}
          >
            {resource.agencyCode} : {resource.agencyName}
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            width: '35%',
            flexDirection: 'column',
            justifyContent: 'flex-end'
          }}
        >
          <Typography
            variant="BodySRegular"
            sx={{
              color: palette.grey['700'],
              maxWidth: '5rem',
              fontWeight: 600,
              textOverflow: 'ellipsis',
              overflow: 'hidden',
              whiteSpace: 'nowrap'
            }}
          >
            {resource.workCode}
          </Typography>
          <Typography variant="BodySRegular" sx={{ color: palette.grey['700'] }}>
            {resource.location}
          </Typography>
        </Box>
      </Box>
    </Box>
  )
}

export default memo(ResourceBox)
