// Apollo client imports
import { makeVar } from '@apollo/client'

import { type Assignment } from '@typeDefs/Assignments'

export const sourceAssignmentVar = makeVar<Assignment | undefined>(undefined)
export const isLoadingAssignmentVar = makeVar<boolean>(false)
export const updateAssignmentIdVar = makeVar<string | undefined>(undefined)
export const placeholderAssignmentVar = makeVar<Assignment[] | undefined>(undefined)
