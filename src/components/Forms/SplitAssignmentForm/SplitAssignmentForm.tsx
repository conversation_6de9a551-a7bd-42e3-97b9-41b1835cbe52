// External Libraries
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  Divider,
  palette,
  Typography
} from 'lion-ui'
import dayjs, { type Dayjs } from 'dayjs'

// React and Apollo Client
import { useEffect, useState, useRef, useMemo, useCallback } from 'react'
import { useApolloClient, gql, useReactiveVar, useMutation } from '@apollo/client'
import { assignmentFieldsQuery, ASSIGNMENTS_QUERY } from '@gql/queries/assignments.query'
import { type Assignment } from '@typeDefs/Assignments'
import { TaskStates } from '@constants/taskStatus'
// Components
import FormHeader from '@components/Forms/Common/FormHeader/FormHeader'
import SplitAssignment from './SplitAssignment'

// Styles
import { getTileStatus } from '@components/Calendar/CalendarDragItem/CalendarDragItem.style'

// Local State
import {
  renderKeyVar,
  activeDatesVar,
  showSplitAssignmentFormVar,
  showAlertVar,
  shouldCloseTooltip,
  resourceTypeVar,
  userInfoVar
} from '@gql/client/localState'
import { CREATE_ASSIGNMENT_MUTATION, UPDATE_ASSIGNMENT_MUTATION } from '@gql/mutations/assignment.mutation'
import { gplError } from '@utils/gplError'
import { getAssignmentsQueryParams } from '@utils/getAssignmentsQueryParams'

const SplitAssignmentForm = () => {
  const activeUser = useReactiveVar(userInfoVar)
  const assignment = useReactiveVar(showSplitAssignmentFormVar)
  const datesRangeContainerRef = useRef<HTMLInputElement>(null)
  const [datesRange, setDatesRange] = useState<Dayjs[]>([])
  const resourceType = useReactiveVar(resourceTypeVar)
  const taskStateKey = assignment?.taskState?.toLowerCase?.() ?? ''
  const tileStatus = getTileStatus(
    assignment?.assignmentState ?? '',
    (TaskStates as Record<string, string>)[taskStateKey] ?? '',
    resourceType ?? ''
  )
  const normalizeTileStatus = (status: string) => status.replace(/-/g, '').toLowerCase()
  const [selectedDate, setSelectedDate] = useState<{ date: Dayjs; index: number } | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [currHoverIndex, setCurrHoverIndex] = useState<number | null>(null)
  const [hoverState, setHoverState] = useState({ hovering: false, position: { right: 0, left: 0 } })
  const columnsWidth = useMemo(() => {
    return datesRangeContainerRef.current && datesRange.length > 0
      ? datesRangeContainerRef.current.getBoundingClientRect().width / datesRange.length
      : 0
  }, [datesRange])
  const client = useApolloClient()

  // Fragment to update the assignment
  const ASSIGNMENT_FRAGMENT = gql`
    fragment AssignmentFragment on Assignment {
      ${assignmentFieldsQuery}
    }
  `

  shouldCloseTooltip(true)

  const [updateAssignmentMutation] = useMutation(UPDATE_ASSIGNMENT_MUTATION, {
    refetchQueries: [ASSIGNMENTS_QUERY],
    awaitRefetchQueries: true,
    onError: (error) => {
      showAlertVar({ message: error.message, show: true })
      gplError(error, error)
    }
  })

  const [createAssignmentMutation] = useMutation(CREATE_ASSIGNMENT_MUTATION, {
    awaitRefetchQueries: true,
    refetchQueries: [ASSIGNMENTS_QUERY],
    onCompleted: () => {
      renderKeyVar(Date.now())
      handleClose()
    },
    onError: (error) => {
      console.log('error', error)
    }
  })

  const updateAssignmentInCache = async (assignmentId: string, updatedFields: Record<string, unknown>) => {
    // Read the specific assignment from the cache
    const assignmentData = await client.readFragment({
      id: `Assignment:${assignmentId}`,
      fragment: ASSIGNMENT_FRAGMENT
    })

    if (assignmentData) {
      const updatedAssignment = {
        ...assignmentData,
        ...updatedFields
      }

      // Write the updated assignment back to the cache using writeFragment
      client.writeFragment({
        id: `Assignment:${assignmentId}`,
        fragment: ASSIGNMENT_FRAGMENT,
        data: updatedAssignment
      })

      await updateAssignmentMutation({
        variables: {
          assignment: {
            externalId: updatedAssignment.id,
            startDate: updatedAssignment.startDate,
            dueDate: updatedAssignment.dueDate,
            userId: updatedAssignment.userId,
            hoursPerDay: updatedAssignment.hoursPerDay,
            userLoggedInExternalId: activeUser?.altairNumber,
            updateAssignmentDate: dayjs().toISOString(),
            isSplit: true
          }
        }
      }).catch((error: Error) => {
        console.error('error', error)
      })
    }
  }

  const addNewAssignmentToCache = async (
    selectedDate: {
      date: Dayjs
      index: number
    } | null
  ) => {
    const totalDays = Math.abs(
      selectedDate ? selectedDate.date.diff(datesRange[datesRange.length - 1], 'days') : (assignment?.totalDays ?? 1)
    )

    const stringDates = getDatesRange(activeDatesVar().startDate, activeDatesVar().endDate).map((date) => {
      return date.format('YYYY-MM-DD')
    })

    const newX = stringDates.findIndex((date) => date === selectedDate?.date.format('YYYY-MM-DD')) + 1

    const newAssignment = {
      ...assignment,
      id: Date.now().toString(),
      startDate: dayjs(selectedDate?.date).add(1, 'days').format('YYYY-MM-DD') ?? assignment?.startDate,
      totalDays,
      width: totalDays,
      x: newX,
      y: assignment?.y,
      createAssignmentDate: dayjs().toISOString()
    }

    // Read the current cache data
    const data = await client.readQuery({
      query: ASSIGNMENTS_QUERY,
      variables: {
        params: getAssignmentsQueryParams()
      }
    })

    if (data?.assignments) {
      const assignments = [...data.assignments]
      assignments.push(newAssignment)

      // Write the updated assignments array back to the cache
      client.writeQuery({
        query: ASSIGNMENTS_QUERY,
        variables: {
          params: getAssignmentsQueryParams()
        },
        data: {
          assignments
        }
      })

      const params = {
        taskExternalId: newAssignment.taskId,
        userAssignedExternalId: newAssignment.userId,
        userLoggedInExternalId: activeUser?.altairNumber,
        startDate: newAssignment.startDate,
        dueDate: newAssignment.dueDate,
        hoursPerDay: newAssignment.hoursPerDay,
        calendarStartDate: activeDatesVar().startDate,
        calendarDueDate: activeDatesVar().endDate,
        createAssignmentDate: dayjs().toISOString(),
        agencyName: newAssignment.agencyName,
        costCenterName: newAssignment.costCenterName,
        locationName: newAssignment.locationName,
        agencyCode: newAssignment.agencyCode,
        costCenterCode: newAssignment.costCenterCode,
        nonWorkingDates: [],
        isSplit: true
      }

      await createAssignmentMutation({
        variables: {
          params
        }
      }).catch((error: Error) => {
        console.error('error', error)
      })
    }
  }

  const getUpdatedFields = (assignment: Assignment, selectedDate: Dayjs) => {
    if (!selectedDate) {
      return {
        dueDate: assignment.dueDate,
        totalDays: assignment.totalDays,
        width: assignment.width
      }
    } else {
      const newDueDate = selectedDate.format('YYYY-MM-DD')
      const newTotalDays = dayjs(newDueDate, 'YYYY-MM-DD').diff(datesRange[0], 'days') + 1
      const newWidth = newTotalDays

      return {
        dueDate: newDueDate,
        totalDays: newTotalDays,
        width: newWidth
      }
    }
  }

  const handleClose = useCallback(() => {
    showSplitAssignmentFormVar(undefined)
    shouldCloseTooltip(false)
  }, [])

  const handleConfirm = async () => {
    setIsLoading(true)

    if (assignment && selectedDate) {
      await updateAssignmentInCache(assignment.id, getUpdatedFields(assignment, selectedDate.date))
      await addNewAssignmentToCache(selectedDate)

      setSelectedDate(null)
      setCurrHoverIndex(null)
      setIsLoading(false)
    }
  }

  const handleMouseEnter = useCallback(
    (index: number) => {
      if (index === datesRange.length - 1 || selectedDate) return
      setCurrHoverIndex(index + 1)
    },
    [datesRange.length, selectedDate]
  )

  const handleMouseLeave = useCallback(() => {
    if (!selectedDate) {
      setHoverState({ ...hoverState, hovering: false })
      setCurrHoverIndex(null)
    }
  }, [hoverState, selectedDate])

  const handleSelectDay = (date: string, index: number) => {
    if (index === datesRange.length - 1) return
    setSelectedDate({ date: dayjs(date, 'YYYY-MM-DD'), index })
    setCurrHoverIndex(index + 1)
  }

  const getDatesRange = useCallback((startDate: string, dueDate: string): Dayjs[] => {
    const datesRange: Dayjs[] = []
    const endDate: Dayjs = dayjs(dueDate, 'YYYY-MM-DD')
    let currentDate: Dayjs = dayjs(activeDatesVar().startDate, 'YYYY-MM-DD')

    if (currentDate.isBefore(startDate)) {
      currentDate = dayjs(startDate, 'YYYY-MM-DD')
    }

    while (
      (currentDate.isBefore(endDate) || currentDate.isSame(endDate)) &&
      dayjs(activeDatesVar().endDate).add(1, 'day').isAfter(currentDate)
    ) {
      datesRange.push(currentDate)
      currentDate = currentDate.clone().add(1, 'day')
    }

    return datesRange
  }, [])

  const isWeekend = useCallback((date: Dayjs): boolean => {
    if (date) {
      return date.day() === 0 || date.day() === 6
    }
    return false
  }, [])

  const handleClear = useCallback(() => {
    setSelectedDate(null)
    setCurrHoverIndex(null)
  }, [])

  useEffect(() => {
    if (assignment) {
      setDatesRange(getDatesRange(assignment.startDate, assignment.dueDate))
    }
  }, [assignment, getDatesRange])

  useEffect(() => {
    if (assignment?.totalDays === 2) {
      setSelectedDate({ date: dayjs(assignment?.startDate, 'YYYY-MM-DD'), index: 0 })
      setCurrHoverIndex(1)
    }
  }, [assignment])

  return (
    <Dialog
      open={true}
      onClose={() => {
        shouldCloseTooltip(false)
        handleClose()
      }}
      fullWidth={true}
      maxWidth="md"
    >
      <FormHeader
        title="Split Task"
        projectName={assignment?.projectName ?? 'Project name not available'}
        taskName={assignment?.taskName ?? 'Task name not available'}
        state={(TaskStates as Record<string, string>)[tileStatus?.toLowerCase()] || tileStatus}
        stateStyle={normalizeTileStatus(tileStatus || '')}
        onClose={() => {
          shouldCloseTooltip(false)
          handleClose()
        }}
      />
      <Divider />

      <DialogContent sx={{ color: '#4D4D4D' }}>
        <Typography>
          Task duration:
          <b> {datesRange.length} days</b>
        </Typography>

        <Box
          sx={{
            maxWidth: '100%',
            overflow: 'hidden',
            position: 'relative',
            borderBottom: `1px solid ${palette.secondary.main}`
          }}
        >
          <Box
            sx={{
              height: '11rem',
              display: 'flex',
              borderTop: `1px solid ${palette.secondary.main}`,
              marginTop: '1rem'
            }}
            ref={datesRangeContainerRef}
          >
            {datesRange.map((date, index) => (
              <Box
                sx={{
                  background: date && isWeekend(date) ? palette.grey['50'] : palette.common.white,
                  position: 'relative',
                  color:
                    dayjs(date).format('YYYY-MM-DD') === dayjs().format('YYYY-MM-DD')
                      ? palette['500-Accent-1'].main
                      : 'inherit',
                  textAlign: 'center',
                  fontSize: '0.75rem',
                  height: '100%',
                  whiteSpace: 'nowrap',
                  paddingTop: '15px',
                  width: '100%',
                  borderRight:
                    date.date() === selectedDate?.date.date()
                      ? `1px solid ${palette['500-Accent-1'].main}`
                      : `1px solid ${palette.secondary.main}`,
                  // eslint-disable-next-line @typescript-eslint/naming-convention
                  '&:hover': {
                    cursor: 'pointer'
                  },
                  // eslint-disable-next-line @typescript-eslint/naming-convention
                  '&:last-child': {
                    border: 'none',
                    cursor: 'not-allowed'
                  }
                }}
                key={date.format('YYYY-MM-DD')}
                data-date={datesRange[index].format('YYYY-MM-DD')}
                onMouseEnter={(event) => {
                  handleMouseEnter(index)
                }}
                onMouseLeave={handleMouseLeave}
                onClick={() => {
                  handleSelectDay(date.format('YYYY-MM-DD'), index)
                }}
              >
                {date.format('ddd')} {date.format('DD')}
              </Box>
            ))}
          </Box>

          {!!currHoverIndex && <SplitAssignment tileWidth={columnsWidth * currHoverIndex} tileLeft={0} />}
          {!!currHoverIndex && (
            <SplitAssignment
              tileWidth={columnsWidth * datesRange.length - columnsWidth * currHoverIndex}
              tileLeft={columnsWidth * currHoverIndex}
            />
          )}

          {!currHoverIndex && <SplitAssignment tileLeft={0} />}
        </Box>
        <Box
          data-testid="split-assignment-overlay"
          sx={{
            position: 'absolute',
            width: '100%',
            top: '0',
            left: '0',
            height: '100%',
            backgroundColor: 'white',
            opacity: 0.5,
            display: isLoading ? 'block' : 'none'
          }}
        ></Box>
      </DialogContent>
      <Divider />

      <DialogActions sx={{ width: '100%', justifyContent: 'space-between', padding: '1rem' }}>
        <Box sx={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            sx={{ mx: 1, borderColor: palette.secondary.main }}
            variant="outlined"
            disabled={!selectedDate || isLoading}
            onClick={() => {
              handleClear()
            }}
          >
            Clear
          </Button>
          {/* eslint-disable */}
          <Button
            type="submit"
            form="form"
            sx={{ mx: 1 }}
            disabled={!selectedDate || isLoading}
            onClick={handleConfirm}
          >
            {isLoading && (
              <CircularProgress
                data-testid="split-assignment-loading"
                size={24}
                sx={{
                  color: palette.primary.main,
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  marginTop: '-12px',
                  marginLeft: '-12px'
                }}
              />
            )}
            {/* eslint-enable */}
            Confirm Split
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  )
}

export default SplitAssignmentForm
