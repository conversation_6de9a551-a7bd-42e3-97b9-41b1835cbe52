import { render, screen } from '@testing-library/react'
import { MockedProvider } from '@apollo/client/testing'
import AuditTrailGoToDetails from '@components/AuditTrail/AuditTrailGoToDetails/AuditTrailGoToDetails'

jest.mock('../../../gql/client/localState', () => {
  const originalModule = jest.requireActual('../../../gql/client/localState')
  return {
    ...originalModule,
    showAllocationDetailsFormVar: jest.fn(() => {})
  }
})

describe('AuditTrailGoToDetails', () => {
  const assignmentId = '12345'
  const resourceId = '67890'

  it('renders the button with correct text and icon', () => {
    render(
      <MockedProvider>
        <AuditTrailGoToDetails assignmentId={assignmentId} resourceId={resourceId} />
      </MockedProvider>
    )

    const button = screen.getByTestId('audit-trail-go-to-details-button')
    expect(button).toBeInTheDocument()
    expect(button).toHaveTextContent('Go to details')
  })
})
