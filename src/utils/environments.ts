import type { AuthEnvConfig } from '@typeDefs/common/Environments'

// TODO: @Dev - This is a temporary solution to get the environment variables
// until we have a proper solution for this from the devops team

function isInIframe(): boolean {
  if (typeof window === 'undefined') return false
  try {
    return window.self !== window.top
  } catch (e) {
    return true
  }
}

function getEffectiveHostname(): string {
  if (typeof window === 'undefined') return ''

  const hostname = window.location.hostname

  if (isInIframe()) {
    console.log('Running in iframe context, hostname:', hostname)

    const urlParams = new URLSearchParams(window.location.search)
    const envParam = urlParams.get('env')
    if (envParam) {
      console.log('Environment detected from URL parameter:', envParam)
      return envParam.toLowerCase()
    }

    if (hostname) {
      console.log('Using iframe hostname for environment detection:', hostname)
      return hostname
    }
  }

  return hostname
}

export function getBasePath(): string {
  const isDev = process.env.NODE_ENV === 'development'
  return isDev ? '' : '/rm'
}

export function getAuthEnvConfig(): AuthEnvConfig {
  const hostname = getEffectiveHostname()
  let clientId = '221009f8-9c93-4809-b3e1-6a67e43affc6'
  let tenantId = 'd52c9ea1-7c21-47b1-82a3-33a74b1f74b8'
  let redirectUri = 'http://localhost:3000'
  let env = 'DEV1'

  console.log('Environment detection - hostname:', hostname, 'isInIframe:', isInIframe())

  if (hostname.includes('dev1')) {
    console.log('ENV: DEV1')
    env = 'DEV1'
    redirectUri = 'https://roar-dev1.marcel.ai/rm/'
  } else if (hostname.includes('qa1')) {
    console.log('ENV: QA1')
    env = 'QA1'
    redirectUri = 'https://roar-qa1.marcel.ai/rm/'
  } else if (hostname.includes('uat')) {
    console.log('ENV: UAT')
    env = 'UAT'
    redirectUri = 'https://roar-uat.marcel.ai/rm/'
  } else if (hostname === 'roar.marcel.ai') {
    console.log('ENV: PRODUCTION')
    // TODO: Replace with real production values
    clientId = ''
    tenantId = ''
    env = 'PRODUCTION'
    redirectUri = 'https://roar.marcel.ai/rm/'
  }

  console.log('Final environment config:', { env, redirectUri, isInIframe: isInIframe() })
  return { clientId, tenantId, redirectUri, env }
}
